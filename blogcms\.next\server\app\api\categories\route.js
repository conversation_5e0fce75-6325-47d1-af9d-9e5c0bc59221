"use strict";(()=>{var e={};e.id=722,e.ids=[722],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65669:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var o={};t.r(o),t.d(o,{GET:()=>u,POST:()=>c});var s=t(96559),a=t(48088),n=t(37719),i=t(32190),p=t(31183);async function u(){try{let e=await p.z.category.findMany({orderBy:{name:"asc"},include:{_count:{select:{articles:!0}}}});return i.NextResponse.json(e)}catch(e){return console.error("Error fetching categories:",e),i.NextResponse.json({error:"Failed to fetch categories"},{status:500})}}async function c(e){try{let{name:r,description:t,color:o}=await e.json();if(!r)return i.NextResponse.json({error:"Category name is required"},{status:400});let s=r.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"");if(await p.z.category.findFirst({where:{OR:[{name:r},{slug:s}]}}))return i.NextResponse.json({error:"Category with this name already exists"},{status:400});let a=await p.z.category.create({data:{name:r,slug:s,description:t||null,color:o||"#6B7280"}});return i.NextResponse.json(a,{status:201})}catch(e){return console.error("Error creating category:",e),i.NextResponse.json({error:"Failed to create category"},{status:500})}}let d=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\categories\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:g}=d;function y(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},73024:e=>{e.exports=require("node:fs")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[243,580,275],()=>t(65669));module.exports=o})();