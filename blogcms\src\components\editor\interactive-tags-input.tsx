'use client'

import { useState, useRef, useEffect } from 'react'
import { Tag, X, Plus } from 'lucide-react'

interface Tag {
  id: string
  name: string
  slug: string
}

interface InteractiveTagsInputProps {
  selectedTags: string[]
  availableTags: Tag[]
  onTagsChange: (tagIds: string[]) => void
}

export function InteractiveTagsInput({ 
  selectedTags, 
  availableTags, 
  onTagsChange 
}: InteractiveTagsInputProps) {
  const [inputValue, setInputValue] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [filteredTags, setFilteredTags] = useState<Tag[]>([])
  const [highlightedIndex, setHighlightedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const suggestionsRef = useRef<HTMLDivElement>(null)

  // Filter tags based on input value
  useEffect(() => {
    if (inputValue.length >= 2) {
      const filtered = availableTags.filter(tag => 
        tag.name.toLowerCase().includes(inputValue.toLowerCase()) &&
        !selectedTags.includes(tag.id)
      )
      setFilteredTags(filtered)
      setShowSuggestions(filtered.length > 0)
      setHighlightedIndex(-1)
    } else {
      setFilteredTags([])
      setShowSuggestions(false)
      setHighlightedIndex(-1)
    }
  }, [inputValue, availableTags, selectedTags])

  // Get selected tag objects
  const selectedTagObjects = selectedTags.map(tagId => 
    availableTags.find(tag => tag.id === tagId)
  ).filter(Boolean) as Tag[]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value)
  }

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault()
      setHighlightedIndex(prev => 
        prev < filteredTags.length - 1 ? prev + 1 : prev
      )
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      setHighlightedIndex(prev => prev > 0 ? prev - 1 : -1)
    } else if (e.key === 'Enter') {
      e.preventDefault()
      if (highlightedIndex >= 0 && filteredTags[highlightedIndex]) {
        handleTagSelect(filteredTags[highlightedIndex])
      } else if (inputValue.trim() && filteredTags.length === 0) {
        // Create new tag if no matches found
        handleCreateNewTag(inputValue.trim())
      }
    } else if (e.key === 'Escape') {
      setShowSuggestions(false)
      setHighlightedIndex(-1)
    } else if (e.key === 'Tab' && highlightedIndex >= 0) {
      e.preventDefault()
      handleTagSelect(filteredTags[highlightedIndex])
    }
  }

  const handleTagSelect = (tag: Tag) => {
    if (!selectedTags.includes(tag.id)) {
      onTagsChange([...selectedTags, tag.id])
    }
    setInputValue('')
    setShowSuggestions(false)
    setHighlightedIndex(-1)
    inputRef.current?.focus()
  }

  const handleCreateNewTag = async (tagName: string) => {
    try {
      const response = await fetch('/api/tags', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: tagName }),
      })

      if (response.ok) {
        const newTag = await response.json()
        // Add the new tag to selected tags
        onTagsChange([...selectedTags, newTag.id])
        setInputValue('')
        setShowSuggestions(false)
        inputRef.current?.focus()
      } else {
        const error = await response.json()
        console.error('Failed to create tag:', error.error)
        // Still clear input even if creation failed
        setInputValue('')
      }
    } catch (error) {
      console.error('Error creating tag:', error)
      setInputValue('')
    }
  }

  const handleTagRemove = (tagId: string) => {
    onTagsChange(selectedTags.filter(id => id !== tagId))
  }

  const handleInputFocus = () => {
    if (inputValue.length >= 2 && filteredTags.length > 0) {
      setShowSuggestions(true)
    }
  }

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      setShowSuggestions(false)
      setHighlightedIndex(-1)
    }, 200)
  }

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700">
        <Tag className="h-4 w-4 inline mr-1" />
        Tags
      </label>

      {/* Selected Tags */}
      {selectedTagObjects.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {selectedTagObjects.map((tag) => (
            <span
              key={tag.id}
              className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full border border-blue-200"
            >
              {tag.name}
              <button
                type="button"
                onClick={() => handleTagRemove(tag.id)}
                className="hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                title={`Remove ${tag.name} tag`}
              >
                <X className="h-3 w-3" />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Tag Input */}
      <div className="relative">
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleInputKeyDown}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              placeholder="Type to search tags or create new ones..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            
            {/* Suggestions Dropdown */}
            {showSuggestions && (
              <div
                ref={suggestionsRef}
                className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto"
              >
                {filteredTags.map((tag, index) => (
                  <button
                    key={tag.id}
                    type="button"
                    onClick={() => handleTagSelect(tag)}
                    className={`w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors ${
                      index === highlightedIndex ? 'bg-blue-50 text-blue-700' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span>{tag.name}</span>
                      <Tag className="h-3 w-3 text-gray-400" />
                    </div>
                  </button>
                ))}
                
                {/* Create new tag option */}
                {inputValue.trim() && filteredTags.length === 0 && (
                  <button
                    type="button"
                    onClick={() => handleCreateNewTag(inputValue.trim())}
                    className="w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors border-t border-gray-200"
                  >
                    <div className="flex items-center gap-2 text-green-600">
                      <Plus className="h-3 w-3" />
                      <span>Create "{inputValue.trim()}"</span>
                    </div>
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Help text */}
        <p className="text-xs text-gray-500 mt-1">
          Type at least 2 characters to see suggestions. Press Enter to create new tags.
        </p>
      </div>
    </div>
  )
}
