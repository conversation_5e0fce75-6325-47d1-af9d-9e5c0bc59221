exports.id=575,exports.ids=[575],exports.modules={10590:(e,r,s)=>{"use strict";s.d(r,{Header:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call <PERSON><PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Books\\Blog\\blogcms\\src\\components\\layout\\header.tsx","Header")},15888:(e,r,s)=>{"use strict";s.d(r,{Providers:()=>i});var t=s(60687),o=s(82136),n=s(10218);function i({children:e}){return(0,t.jsx)(o.SessionProvider,{children:(0,t.jsx)(n.N,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:e})})}},21921:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,4536,23)),Promise.resolve().then(s.bind(s,10590)),Promise.resolve().then(s.bind(s,48150))},33343:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},35073:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,85814,23)),Promise.resolve().then(s.bind(s,74456)),Promise.resolve().then(s.bind(s,15888))},48150:(e,r,s)=>{"use strict";s.d(r,{Providers:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Books\\Blog\\blogcms\\src\\components\\providers.tsx","Providers")},61135:()=>{},70295:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},74456:(e,r,s)=>{"use strict";s.d(r,{Header:()=>j});var t=s(60687),o=s(85814),n=s.n(o),i=s(82136),a=s(43210),l=s(74606),c=s(99270),d=s(21134),m=s(363),h=s(58869),x=s(84027),u=s(40083),f=s(11860),p=s(12941),g=s(10218);function j(){let{data:e}=(0,i.useSession)(),{theme:r,setTheme:s}=(0,g.D)(),[o,j]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1);return(0,t.jsx)("header",{className:"sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"container mx-auto px-4",children:[(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,t.jsxs)(n(),{href:"/",className:"flex items-center space-x-2 hover:opacity-80 transition-opacity",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary rounded-lg flex items-center justify-center",children:(0,t.jsx)(l.A,{className:"h-4 w-4 text-primary-foreground"})}),(0,t.jsx)("span",{className:"text-xl font-bold",children:"BlogCMS"})]}),(0,t.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,t.jsx)(n(),{href:"/",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Home"}),(0,t.jsx)(n(),{href:"/articles",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Articles"}),(0,t.jsx)(n(),{href:"/categories",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Categories"}),(0,t.jsx)(n(),{href:"/authors",className:"text-sm font-medium text-muted-foreground hover:text-foreground transition-colors",children:"Authors"})]}),(0,t.jsx)("div",{className:"hidden lg:flex items-center space-x-4",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)("input",{type:"text",placeholder:"Search articles...",className:"pl-10 pr-4 py-2 w-64 rounded-lg border border-input bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all"})]})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{type:"button",onClick:()=>{s("dark"===r?"light":"dark")},className:"p-2 rounded-md hover:bg-accent transition-colors","aria-label":"Toggle theme",children:"dark"===r?(0,t.jsx)(d.A,{className:"h-4 w-4"}):(0,t.jsx)(m.A,{className:"h-4 w-4"})}),e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(n(),{href:"/write",className:"hidden md:inline-flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Write"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{type:"button",onClick:()=>v(!b),className:"flex items-center space-x-2 p-2 rounded-md hover:bg-accent transition-colors",children:e.user.avatarUrl?(0,t.jsx)("img",{src:e.user.avatarUrl,alt:e.user.username,className:"h-8 w-8 rounded-full"}):(0,t.jsx)("div",{className:"h-8 w-8 rounded-full bg-primary flex items-center justify-center",children:(0,t.jsx)(h.A,{className:"h-4 w-4 text-primary-foreground"})})}),b&&(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-background border rounded-md shadow-lg py-1 z-50",children:[(0,t.jsxs)(n(),{href:`/profile/${e.user.username}`,className:"flex items-center space-x-2 px-4 py-2 text-sm hover:bg-accent transition-colors",onClick:()=>v(!1),children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Profile"})]}),(0,t.jsxs)(n(),{href:"/dashboard",className:"flex items-center space-x-2 px-4 py-2 text-sm hover:bg-accent transition-colors",onClick:()=>v(!1),children:[(0,t.jsx)(x.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Dashboard"})]}),(0,t.jsx)("hr",{className:"my-1"}),(0,t.jsxs)("button",{type:"button",onClick:()=>{(0,i.signOut)(),v(!1)},className:"flex items-center space-x-2 w-full px-4 py-2 text-sm hover:bg-accent transition-colors text-left",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Sign Out"})]})]})]})]}):null,(0,t.jsx)("button",{type:"button",onClick:()=>j(!o),className:"md:hidden p-2 rounded-md hover:bg-accent transition-colors","aria-label":"Toggle menu",children:o?(0,t.jsx)(f.A,{className:"h-5 w-5"}):(0,t.jsx)(p.A,{className:"h-5 w-5"})})]})]}),o&&(0,t.jsx)("div",{className:"md:hidden border-t py-4",children:(0,t.jsxs)("nav",{className:"flex flex-col space-y-4",children:[(0,t.jsx)(n(),{href:"/",className:"text-sm font-medium hover:text-primary transition-colors",onClick:()=>j(!1),children:"Home"}),(0,t.jsx)(n(),{href:"/articles",className:"text-sm font-medium hover:text-primary transition-colors",onClick:()=>j(!1),children:"Articles"}),(0,t.jsx)(n(),{href:"/categories",className:"text-sm font-medium hover:text-primary transition-colors",onClick:()=>j(!1),children:"Categories"}),(0,t.jsx)(n(),{href:"/authors",className:"text-sm font-medium hover:text-primary transition-colors",onClick:()=>j(!1),children:"Authors"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)("input",{type:"text",placeholder:"Search articles...",className:"pl-10 pr-4 py-2 w-full rounded-md border border-input bg-background text-sm focus:outline-none focus:ring-2 focus:ring-ring"})]}),e?(0,t.jsxs)("div",{className:"flex flex-col space-y-2 pt-4 border-t",children:[(0,t.jsxs)(n(),{href:"/write",className:"flex items-center space-x-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors",onClick:()=>j(!1),children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Write"})]}),(0,t.jsx)(n(),{href:`/profile/${e.user.username}`,className:"text-sm font-medium hover:text-primary transition-colors",onClick:()=>j(!1),children:"Profile"}),(0,t.jsx)(n(),{href:"/dashboard",className:"text-sm font-medium hover:text-primary transition-colors",onClick:()=>j(!1),children:"Dashboard"}),(0,t.jsx)("button",{type:"button",onClick:()=>{(0,i.signOut)(),j(!1)},className:"text-sm font-medium hover:text-primary transition-colors text-left",children:"Sign Out"})]}):null]})})]})})}},80871:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>g,metadata:()=>p});var t=s(37413),o=s(25091),n=s.n(o);s(61135);var i=s(48150),a=s(10590),l=s(4536),c=s.n(l),d=s(55392),m=s(63353),h=s(92715),x=s(96262),u=s(60343);function f(){return(0,t.jsx)("footer",{className:"border-t bg-background",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(c(),{href:"/",className:"flex items-center space-x-2",children:[(0,t.jsx)(d.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"text-xl font-bold",children:"BlogCMS"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"A modern blogging platform built for creators, writers, and publishers."}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)("a",{href:"https://twitter.com",target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors",children:(0,t.jsx)(m.A,{className:"h-5 w-5"})}),(0,t.jsx)("a",{href:"https://github.com",target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors",children:(0,t.jsx)(h.A,{className:"h-5 w-5"})}),(0,t.jsx)("a",{href:"https://linkedin.com",target:"_blank",rel:"noopener noreferrer",className:"text-muted-foreground hover:text-primary transition-colors",children:(0,t.jsx)(x.A,{className:"h-5 w-5"})}),(0,t.jsx)("a",{href:"mailto:<EMAIL>",className:"text-muted-foreground hover:text-primary transition-colors",children:(0,t.jsx)(u.A,{className:"h-5 w-5"})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold",children:"Platform"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/articles",className:"text-muted-foreground hover:text-primary transition-colors",children:"Browse Articles"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/categories",className:"text-muted-foreground hover:text-primary transition-colors",children:"Categories"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/authors",className:"text-muted-foreground hover:text-primary transition-colors",children:"Authors"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/trending",className:"text-muted-foreground hover:text-primary transition-colors",children:"Trending"})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold",children:"Resources"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/help",className:"text-muted-foreground hover:text-primary transition-colors",children:"Help Center"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/docs",className:"text-muted-foreground hover:text-primary transition-colors",children:"Documentation"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/api",className:"text-muted-foreground hover:text-primary transition-colors",children:"API Reference"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/blog",className:"text-muted-foreground hover:text-primary transition-colors",children:"Blog"})})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-sm font-semibold",children:"Company"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/about",className:"text-muted-foreground hover:text-primary transition-colors",children:"About Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/contact",className:"text-muted-foreground hover:text-primary transition-colors",children:"Contact"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/privacy",className:"text-muted-foreground hover:text-primary transition-colors",children:"Privacy Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)(c(),{href:"/terms",className:"text-muted-foreground hover:text-primary transition-colors",children:"Terms of Service"})})]})]})]}),(0,t.jsx)("div",{className:"mt-8 pt-8 border-t",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["\xa9 ",new Date().getFullYear()," BlogCMS. All rights reserved."]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-2 md:mt-0",children:"Built with ❤️ using Next.js and TypeScript"})]})})]})})}let p={title:"BlogCMS - Modern Blogging Platform",description:"A comprehensive blogging platform built with Next.js, featuring rich content creation, user management, and analytics.",keywords:["blog","cms","content management","writing","publishing"],authors:[{name:"BlogCMS Team"}],openGraph:{title:"BlogCMS - Modern Blogging Platform",description:"A comprehensive blogging platform built with Next.js",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"BlogCMS - Modern Blogging Platform",description:"A comprehensive blogging platform built with Next.js"}};function g({children:e}){return(0,t.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,t.jsx)("body",{className:`${n().variable} font-sans antialiased`,children:(0,t.jsx)(i.Providers,{children:(0,t.jsxs)("div",{className:"min-h-screen flex flex-col",children:[(0,t.jsx)(a.Header,{}),(0,t.jsx)("main",{className:"flex-1",children:e}),(0,t.jsx)(f,{})]})})})})}}};