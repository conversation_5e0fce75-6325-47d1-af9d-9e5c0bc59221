"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./src/app/auth/signup/page.tsx":
/*!**************************************!*\
  !*** ./src/app/auth/signup/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SignUpPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Eye,EyeOff,Lock,Mail,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction SignUpPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: '',\n        lastName: '',\n        username: '',\n        email: '',\n        password: '',\n        confirmPassword: ''\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.firstName.trim()) {\n            newErrors.firstName = 'First name is required';\n        }\n        if (!formData.lastName.trim()) {\n            newErrors.lastName = 'Last name is required';\n        }\n        if (!formData.username.trim()) {\n            newErrors.username = 'Username is required';\n        } else if (formData.username.length < 3) {\n            newErrors.username = 'Username must be at least 3 characters';\n        } else if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {\n            newErrors.username = 'Username can only contain letters, numbers, and underscores';\n        }\n        if (!formData.email.trim()) {\n            newErrors.email = 'Email is required';\n        } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n            newErrors.email = 'Please enter a valid email address';\n        }\n        if (!formData.password) {\n            newErrors.password = 'Password is required';\n        } else if (formData.password.length < 6) {\n            newErrors.password = 'Password must be at least 6 characters';\n        }\n        if (!formData.confirmPassword) {\n            newErrors.confirmPassword = 'Please confirm your password';\n        } else if (formData.password !== formData.confirmPassword) {\n            newErrors.confirmPassword = 'Passwords do not match';\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsLoading(true);\n        setErrors({});\n        try {\n            const response = await fetch('/api/auth/signup', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    firstName: formData.firstName,\n                    lastName: formData.lastName,\n                    username: formData.username,\n                    email: formData.email,\n                    password: formData.password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                if (data.errors) {\n                    setErrors(data.errors);\n                } else {\n                    setErrors({\n                        general: data.error || 'An error occurred during registration'\n                    });\n                }\n            } else {\n                setSuccess(true);\n                setTimeout(()=>{\n                    router.push('/auth/signin?message=Account created successfully');\n                }, 2000);\n            }\n        } catch (e) {\n            setErrors({\n                general: 'An error occurred. Please try again.'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (success) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md w-full space-y-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"mx-auto h-16 w-16 text-green-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-3xl font-extrabold text-gray-900\",\n                            children: \"Account Created!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-sm text-gray-600\",\n                            children: \"Your account has been created successfully. Redirecting to sign in...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-6 text-center text-3xl font-extrabold text-foreground\",\n                            children: \"Create your account\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-center text-sm text-muted-foreground\",\n                            children: [\n                                \"Or\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/auth/signin\",\n                                    className: \"font-medium text-primary hover:text-primary/80\",\n                                    children: \"sign in to your existing account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                            children: errors.general\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mt-8 space-y-6\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"firstName\",\n                                                    className: \"block text-sm font-medium text-foreground\",\n                                                    children: \"First Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                            id: \"firstName\",\n                                                            name: \"firstName\",\n                                                            type: \"text\",\n                                                            required: true,\n                                                            value: formData.firstName,\n                                                            onChange: (e)=>setFormData({\n                                                                    ...formData,\n                                                                    firstName: e.target.value\n                                                                }),\n                                                            className: \"pl-10 \".concat(errors.firstName ? 'border-destructive' : ''),\n                                                            placeholder: \"First name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-destructive\",\n                                                    children: errors.firstName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"lastName\",\n                                                    className: \"block text-sm font-medium text-foreground\",\n                                                    children: \"Last Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"lastName\",\n                                                        name: \"lastName\",\n                                                        type: \"text\",\n                                                        required: true,\n                                                        value: formData.lastName,\n                                                        onChange: (e)=>setFormData({\n                                                                ...formData,\n                                                                lastName: e.target.value\n                                                            }),\n                                                        className: errors.lastName ? 'border-destructive' : '',\n                                                        placeholder: \"Last name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.lastName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"mt-1 text-sm text-destructive\",\n                                                    children: errors.lastName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"username\",\n                                            className: \"block text-sm font-medium text-foreground\",\n                                            children: \"Username\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"username\",\n                                                    name: \"username\",\n                                                    type: \"text\",\n                                                    required: true,\n                                                    value: formData.username,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            username: e.target.value.toLowerCase()\n                                                        }),\n                                                    className: \"pl-10 \".concat(errors.username ? 'border-destructive' : ''),\n                                                    placeholder: \"Choose a username\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.username && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-destructive\",\n                                            children: errors.username\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-foreground\",\n                                            children: \"Email address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    type: \"email\",\n                                                    autoComplete: \"email\",\n                                                    required: true,\n                                                    value: formData.email,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            email: e.target.value\n                                                        }),\n                                                    className: \"pl-10 \".concat(errors.email ? 'border-destructive' : ''),\n                                                    placeholder: \"Enter your email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-destructive\",\n                                            children: errors.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-foreground\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"password\",\n                                                    name: \"password\",\n                                                    type: showPassword ? 'text' : 'password',\n                                                    autoComplete: \"new-password\",\n                                                    required: true,\n                                                    value: formData.password,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            password: e.target.value\n                                                        }),\n                                                    className: \"pl-10 pr-10 \".concat(errors.password ? 'border-destructive' : ''),\n                                                    placeholder: \"Create a password\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-destructive\",\n                                            children: errors.password\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"confirmPassword\",\n                                            className: \"block text-sm font-medium text-foreground\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"confirmPassword\",\n                                                    name: \"confirmPassword\",\n                                                    type: showConfirmPassword ? 'text' : 'password',\n                                                    autoComplete: \"new-password\",\n                                                    required: true,\n                                                    value: formData.confirmPassword,\n                                                    onChange: (e)=>setFormData({\n                                                            ...formData,\n                                                            confirmPassword: e.target.value\n                                                        }),\n                                                    className: \"pl-10 pr-10 \".concat(errors.confirmPassword ? 'border-destructive' : ''),\n                                                    placeholder: \"Confirm your password\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                    onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                                    children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 21\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Eye_EyeOff_Lock_Mail_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-5 w-5 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.confirmPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-destructive\",\n                                            children: errors.confirmPassword\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isLoading,\n                                className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Creating account...\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 17\n                                }, this) : 'Create account'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n            lineNumber: 135,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(SignUpPage, \"JrENlFY94OHSUzqyPuMHgmN3vVQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = SignUpPage;\nvar _c;\n$RefreshReg$(_c, \"SignUpPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/signup/page.tsx\n"));

/***/ })

});