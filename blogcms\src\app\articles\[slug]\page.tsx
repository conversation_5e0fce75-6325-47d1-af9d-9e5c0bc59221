'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import Link from 'next/link'
import { RichTextEditor } from '@/components/editor/rich-text-editor'
import {
  Calendar,
  User,
  Eye,
  Clock,
  Heart,
  Bookmark,
  Share2,
  Tag,
  ArrowLeft
} from 'lucide-react'

interface Article {
  id: string
  title: string
  subtitle?: string
  slug: string
  content: string
  excerpt?: string
  featuredImageUrl?: string
  featuredImageAlt?: string
  status: string
  publishedAt?: string
  viewCount: number
  readingTime: number
  author: {
    id: string
    username: string
    firstName?: string
    lastName?: string
    bio?: string
    avatarUrl?: string
    _count: {
      articles: number
      followers: number
    }
  }
  category?: {
    id: string
    name: string
    slug: string
    color?: string
  }
  tags: Array<{
    tag: {
      id: string
      name: string
      slug: string
    }
  }>
  _count: {
    comments: number
    reactions: number
  }
}

export default function ArticlePage() {
  const params = useParams()
  const [article, setArticle] = useState<Article | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (params.slug) {
      loadArticle(params.slug as string)
    }
  }, [params.slug])

  const loadArticle = async (slug: string) => {
    try {
      const response = await fetch(`/api/articles/${slug}`)
      if (response.ok) {
        const data = await response.json()
        setArticle(data)
        
        // Track view
        if (data.slug) {
          fetch(`/api/articles/${data.slug}/view`, { method: 'POST' })
        }
      } else {
        setError('Article not found')
      }
    } catch (err) {
      console.error('Error loading article:', err)
      setError('Failed to load article')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getAuthorName = (author: Article['author']) => {
    if (author.firstName && author.lastName) {
      return `${author.firstName} ${author.lastName}`
    }
    return author.username
  }

  const handleShare = async () => {
    if (navigator.share && article) {
      try {
        await navigator.share({
          title: article.title,
          text: article.subtitle || article.excerpt,
          url: window.location.href,
        })
      } catch {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href)
        alert('Article URL copied to clipboard!')
      }
    } else {
      // Fallback for browsers without Web Share API
      navigator.clipboard.writeText(window.location.href)
      alert('Article URL copied to clipboard!')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto py-8 px-4">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="h-12 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded w-3/4 mb-8"></div>
            <div className="h-64 bg-gray-200 rounded mb-8"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !article) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {error || 'Article not found'}
          </h1>
          <Link
            href="/articles"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            ← Back to articles
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4">
        {/* Back Button */}
        <Link
          href="/articles"
          className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-6"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to articles
        </Link>

        <article className="bg-white rounded-lg shadow-sm border overflow-hidden">
          {/* Featured Image */}
          {article.featuredImageUrl && (
            <div className="aspect-video overflow-hidden">
              <img
                src={article.featuredImageUrl}
                alt={article.featuredImageAlt || article.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div className="p-8">
            {/* Category */}
            {article.category && (
              <div className="mb-4">
                <Link
                  href={`/articles?category=${article.category.slug}`}
                  className="inline-block px-3 py-1 text-sm font-medium rounded-full text-white hover:opacity-80 transition-opacity"
                  style={{ backgroundColor: article.category.color || '#6B7280' }}
                >
                  {article.category.name}
                </Link>
              </div>
            )}

            {/* Title */}
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {article.title}
            </h1>

            {/* Subtitle */}
            {article.subtitle && (
              <p className="text-xl text-gray-600 mb-6">{article.subtitle}</p>
            )}

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500 mb-8 pb-8 border-b">
              {/* Author */}
              <div className="flex items-center gap-3">
                {article.author.avatarUrl ? (
                  <img
                    src={article.author.avatarUrl}
                    alt={getAuthorName(article.author)}
                    className="w-10 h-10 rounded-full"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
                    <User className="h-5 w-5" />
                  </div>
                )}
                <div>
                  <Link
                    href={`/profile/${article.author.username}`}
                    className="font-medium text-gray-900 hover:text-blue-600"
                  >
                    {getAuthorName(article.author)}
                  </Link>
                  <p className="text-xs">
                    {article.author._count.articles} articles • {article.author._count.followers} followers
                  </p>
                </div>
              </div>

              {/* Date */}
              {article.publishedAt && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(article.publishedAt)}</span>
                </div>
              )}

              {/* Reading Time */}
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{article.readingTime} min read</span>
              </div>

              {/* Views */}
              <div className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                <span>{article.viewCount} views</span>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-4 ml-auto">
                <button
                  onClick={handleShare}
                  className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
                >
                  <Share2 className="h-4 w-4" />
                  Share
                </button>
                <button className="flex items-center gap-1 text-gray-500 hover:text-red-500">
                  <Heart className="h-4 w-4" />
                  {article._count.reactions}
                </button>
                <button className="flex items-center gap-1 text-gray-500 hover:text-blue-500">
                  <Bookmark className="h-4 w-4" />
                  Save
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="prose prose-lg max-w-none">
              <RichTextEditor
                content={article.content}
                readOnly={true}
                className="border-none p-0"
              />
            </div>

            {/* Tags */}
            {article.tags.length > 0 && (
              <div className="mt-8 pt-8 border-t">
                <div className="flex items-center gap-2 mb-3">
                  <Tag className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700">Tags</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {article.tags.map(({ tag }) => (
                    <Link
                      key={tag.id}
                      href={`/articles?tag=${tag.slug}`}
                      className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
                    >
                      {tag.name}
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Author Bio */}
            {article.author.bio && (
              <div className="mt-8 pt-8 border-t">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">About the Author</h3>
                <div className="flex gap-4">
                  {article.author.avatarUrl ? (
                    <img
                      src={article.author.avatarUrl}
                      alt={getAuthorName(article.author)}
                      className="w-16 h-16 rounded-full flex-shrink-0"
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0">
                      <User className="h-8 w-8" />
                    </div>
                  )}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">
                      {getAuthorName(article.author)}
                    </h4>
                    <p className="text-gray-600 text-sm">{article.author.bio}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </article>
      </div>
    </div>
  )
}
