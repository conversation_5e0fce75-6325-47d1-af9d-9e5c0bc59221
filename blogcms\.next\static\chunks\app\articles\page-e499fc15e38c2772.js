(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[292],{1007:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1366:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1976:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>c});var s=t(5155),a=t(2115),l=t(9434);let c=a.forwardRef((e,r)=>{let{className:t,type:a,...c}=e;return(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...c})});c.displayName="Input"},6695:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>d,ZB:()=>i,Zp:()=>c,aR:()=>n});var s=t(5155),a=t(2115),l=t(9434);let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",t),...a})});c.displayName="Card";let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});n.displayName="CardHeader";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});i.displayName="CardTitle",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})}).displayName="CardDescription";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...a})});d.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},7248:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(5155),a=t(2115),l=t(6874),c=t.n(l),n=t(7924);let i=(0,t(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var d=t(9074),o=t(1007),m=t(2657),u=t(1366),x=t(1976),h=t(6695),p=t(2523);function f(){let[e,r]=(0,a.useState)([]),[t,l]=(0,a.useState)([]),[f,g]=(0,a.useState)(!0),[j,N]=(0,a.useState)(""),[v,b]=(0,a.useState)(""),[y,w]=(0,a.useState)("latest");(0,a.useEffect)(()=>{A(),k()},[A]);let A=(0,a.useCallback)(async()=>{try{let e=new URLSearchParams({status:"PUBLISHED",...v&&{categoryId:v},...y&&{sortBy:y},...j&&{search:j}}),t=await fetch("/api/articles?".concat(e));if(t.ok){let e=await t.json();r(e.articles||[])}}catch(e){console.error("Error loading articles:",e)}finally{g(!1)}},[v,y]),k=async()=>{try{let e=await fetch("/api/categories");if(e.ok){let r=await e.json();l(r)}}catch(e){console.error("Error loading categories:",e)}},C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),S=e=>e.firstName&&e.lastName?"".concat(e.firstName," ").concat(e.lastName):e.username;return f?(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto py-8 px-4",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,s.jsx)("div",{className:"h-8 bg-muted rounded w-1/4"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,r)=>(0,s.jsx)(h.Zp,{children:(0,s.jsxs)(h.Wu,{className:"p-6",children:[(0,s.jsx)("div",{className:"h-48 bg-muted rounded mb-4"}),(0,s.jsx)("div",{className:"h-6 bg-muted rounded mb-2"}),(0,s.jsx)("div",{className:"h-4 bg-muted rounded w-3/4"})]})},r))})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-background",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto py-8 px-4",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-4",children:"Articles"}),(0,s.jsx)(h.Zp,{children:(0,s.jsxs)(h.Wu,{className:"p-6",children:[(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),A()},className:"mb-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5"}),(0,s.jsx)(p.p,{type:"text",placeholder:"Search articles...",value:j,onChange:e=>N(e.target.value),className:"pl-10"})]})}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(i,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsxs)("select",{value:v,onChange:e=>b(e.target.value),className:"px-3 py-1 border border-input rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground","aria-label":"Filter by category",children:[(0,s.jsx)("option",{value:"",children:"All Categories"}),t.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsxs)("select",{value:y,onChange:e=>w(e.target.value),className:"px-3 py-1 border border-input rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground","aria-label":"Sort articles",children:[(0,s.jsx)("option",{value:"latest",children:"Latest"}),(0,s.jsx)("option",{value:"oldest",children:"Oldest"}),(0,s.jsx)("option",{value:"popular",children:"Most Popular"}),(0,s.jsx)("option",{value:"trending",children:"Trending"})]})]})]})]})})]}),0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-muted-foreground text-lg",children:"No articles found."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,s.jsxs)(h.Zp,{className:"hover:shadow-md transition-shadow",children:[e.featuredImageUrl&&(0,s.jsx)("div",{className:"aspect-video overflow-hidden rounded-t-lg",children:(0,s.jsx)("img",{src:e.featuredImageUrl,alt:e.title,className:"w-full h-full object-cover hover:scale-105 transition-transform duration-300"})}),(0,s.jsxs)(h.Wu,{className:"p-6",children:[e.category&&(0,s.jsx)("div",{className:"mb-3",children:(0,s.jsx)("span",{className:"inline-block px-2 py-1 text-xs font-medium rounded-full text-white bg-primary",children:e.category.name})}),(0,s.jsx)("h2",{className:"text-xl font-bold text-foreground mb-2 line-clamp-2",children:(0,s.jsx)(c(),{href:"/articles/".concat(e.slug),className:"hover:text-primary transition-colors",children:e.title})}),e.subtitle&&(0,s.jsx)("p",{className:"text-muted-foreground mb-3 line-clamp-2",children:e.subtitle}),e.excerpt&&(0,s.jsx)("p",{className:"text-muted-foreground text-sm mb-4 line-clamp-3",children:e.excerpt}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.author.avatarUrl?(0,s.jsx)("img",{src:e.author.avatarUrl,alt:S(e.author),className:"w-6 h-6 rounded-full"}):(0,s.jsx)("div",{className:"w-6 h-6 rounded-full bg-muted flex items-center justify-center",children:(0,s.jsx)(o.A,{className:"h-3 w-3"})}),(0,s.jsx)("span",{children:S(e.author)})]}),(0,s.jsxs)("span",{children:[e.readingTime," min read"]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground pt-4 border-t border-border",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),e.viewCount]}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),e._count.comments]}),(0,s.jsxs)("span",{className:"flex items-center gap-1",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),e._count.reactions]})]}),e.publishedAt&&(0,s.jsx)("span",{children:C(e.publishedAt)})]})]})]},e.id))})]})})}},7924:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9074:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9275:(e,r,t)=>{Promise.resolve().then(t.bind(t,7248))},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var s=t(2596),a=t(9688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var s=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),c=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},n=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},i=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:c,className:o="",children:m,iconNode:u,...x}=e;return(0,s.createElement)("svg",{ref:r,...d,width:a,height:a,stroke:t,strokeWidth:c?24*Number(l)/Number(a):l,className:n("lucide",o),...!m&&!i(x)&&{"aria-hidden":"true"},...x},[...u.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(m)?m:[m]])}),m=(e,r)=>{let t=(0,s.forwardRef)((t,l)=>{let{className:i,...d}=t;return(0,s.createElement)(o,{ref:l,iconNode:r,className:n("lucide-".concat(a(c(e))),"lucide-".concat(e),i),...d})});return t.displayName=c(e),t}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,13,441,684,358],()=>r(9275)),_N_E=e.O()}]);