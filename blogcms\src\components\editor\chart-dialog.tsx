'use client'

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>hart<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp } from 'lucide-react'

interface ChartDialogProps {
  isOpen: boolean
  onClose: () => void
  onInsert: (chartHtml: string) => void
}

const sampleData = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: 300 },
  { name: 'Mar', value: 200 },
  { name: 'Apr', value: 278 },
  { name: 'May', value: 189 },
  { name: 'Jun', value: 239 },
]

export function ChartDialog({ isOpen, onClose, onInsert }: ChartDialogProps) {
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie' | 'area'>('bar')
  const [chartData, setChartData] = useState(JSON.stringify(sampleData, null, 2))
  const [chartTitle, setChartTitle] = useState('Sample Chart')

  const handleInsert = () => {
    try {
      const data = JSON.parse(chartData)
      const chartHtml = `
        <div class="chart-container my-6 p-4 border rounded-lg bg-gray-50">
          <h4 class="text-lg font-semibold mb-4 text-center">${chartTitle}</h4>
          <div class="h-64 flex items-center justify-center bg-white rounded border">
            <div class="text-gray-500 text-center">
              <p>${chartTitle}</p>
              <p class="text-sm">Chart placeholder (${data.length} data points)</p>
            </div>
          </div>
        </div>
      `
      onInsert(chartHtml)
      onClose()
    } catch (error) {
      alert('Invalid JSON data. Please check your chart data format.')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Insert Chart</h3>
          <button
            type="button"
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Chart Title
            </label>
            <input
              type="text"
              value={chartTitle}
              onChange={(e) => setChartTitle(e.target.value)}
              className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Chart Type
            </label>
            <div className="grid grid-cols-4 gap-2">
              <button
                type="button"
                onClick={() => setChartType('bar')}
                className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                  chartType === 'bar' ? 'border-primary bg-primary/10' : 'border-input'
                }`}
              >
                <BarChart3 className="h-6 w-6" />
                <span className="text-xs">Bar</span>
              </button>
              <button
                type="button"
                onClick={() => setChartType('line')}
                className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                  chartType === 'line' ? 'border-primary bg-primary/10' : 'border-input'
                }`}
              >
                <LineChart className="h-6 w-6" />
                <span className="text-xs">Line</span>
              </button>
              <button
                type="button"
                onClick={() => setChartType('pie')}
                className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                  chartType === 'pie' ? 'border-primary bg-primary/10' : 'border-input'
                }`}
              >
                <PieChart className="h-6 w-6" />
                <span className="text-xs">Pie</span>
              </button>
              <button
                type="button"
                onClick={() => setChartType('area')}
                className={`p-3 border rounded-lg flex flex-col items-center gap-2 ${
                  chartType === 'area' ? 'border-primary bg-primary/10' : 'border-input'
                }`}
              >
                <TrendingUp className="h-6 w-6" />
                <span className="text-xs">Area</span>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-foreground mb-2">
              Chart Data (JSON)
            </label>
            <textarea
              value={chartData}
              onChange={(e) => setChartData(e.target.value)}
              rows={6}
              className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-md focus:outline-none focus:ring-2 focus:ring-ring font-mono text-sm"
              placeholder="Enter your chart data in JSON format..."
            />
          </div>

          <button
            type="button"
            onClick={handleInsert}
            className="w-full bg-primary text-primary-foreground py-2 px-4 rounded-md hover:bg-primary/90"
          >
            Insert Chart
          </button>
        </div>
      </div>
    </div>
  )
}
