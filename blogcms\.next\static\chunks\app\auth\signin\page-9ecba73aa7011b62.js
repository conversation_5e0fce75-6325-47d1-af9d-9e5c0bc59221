(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(5155),a=t(2115),l=t(9434);let n=a.forwardRef((e,r)=>{let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...n})});n.displayName="Input"},2919:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3738:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var s=t(5155),a=t(2115),l=t(2108),n=t(5695),i=t(6874),d=t.n(i),o=t(5339),c=t(8883),u=t(2919),m=t(8749),f=t(2657),x=t(5476),h=t(6695),p=t(2523);function v(){let e=(0,n.useRouter)(),r=(0,n.useSearchParams)(),t=r.get("callbackUrl")||"/",i=r.get("error"),[v,g]=(0,a.useState)({email:"",password:""}),[b,y]=(0,a.useState)(!1),[N,w]=(0,a.useState)(!1),[j,k]=(0,a.useState)(""),A=async r=>{r.preventDefault(),w(!0),k("");try{let r=await (0,l.signIn)("credentials",{email:v.email,password:v.password,redirect:!1});(null==r?void 0:r.error)?k("Invalid email or password"):await (0,l.getSession)()&&(e.push(t),e.refresh())}catch(e){k("An error occurred. Please try again.")}finally{w(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)(h.Zp,{children:[(0,s.jsxs)(h.aR,{className:"text-center",children:[(0,s.jsx)(h.ZB,{className:"text-3xl font-extrabold",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Or"," ",(0,s.jsx)(d(),{href:"/auth/signup",className:"font-medium text-primary hover:text-primary/80",children:"create a new account"})]})]}),(0,s.jsxs)(h.Wu,{children:[(i||j)&&(0,s.jsx)("div",{className:"bg-destructive/15 border border-destructive/20 rounded-md p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-destructive"}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm text-destructive",children:j||i&&(e=>{switch(e){case"CredentialsSignin":return"Invalid email or password";case"EmailSignin":return"Error sending email";case"OAuthSignin":return"Error with OAuth provider";case"OAuthCallback":return"Error with OAuth callback";case"OAuthCreateAccount":return"Could not create OAuth account";case"EmailCreateAccount":return"Could not create email account";case"Callback":return"Error in callback";case"OAuthAccountNotLinked":return"OAuth account not linked";case"SessionRequired":return"Please sign in to access this page";default:return"An error occurred during sign in"}})(i)})})]})}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:A,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground mb-2",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5"}),(0,s.jsx)(p.p,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:v.email,onChange:e=>g({...v,email:e.target.value}),className:"pl-12",placeholder:"Enter your email"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5"}),(0,s.jsx)(p.p,{id:"password",name:"password",type:b?"text":"password",autoComplete:"current-password",required:!0,value:v.password,onChange:e=>g({...v,password:e.target.value}),className:"pl-12 pr-12",placeholder:"Enter your password"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>y(!b),children:b?(0,s.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"}):(0,s.jsx)(f.A,{className:"h-5 w-5 text-muted-foreground"})})]})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(d(),{href:"/auth/forgot-password",className:"font-medium text-primary hover:text-primary/80",children:"Forgot your password?"})})}),(0,s.jsx)(x.$,{type:"submit",disabled:N,className:"w-full",children:N?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Demo accounts:"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-xs text-muted-foreground",children:[(0,s.jsx)("p",{children:"Admin: <EMAIL> / admin123"}),(0,s.jsx)("p",{children:"Author: <EMAIL> / author123"})]})]})]})]})]})})})}},5339:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5476:(e,r,t)=>{"use strict";t.d(r,{$:()=>c});var s=t(5155),a=t(2115),l=t(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=l.$;var d=t(9434);let o=((e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return i(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:l}=r,d=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],s=null==l?void 0:l[e];if(null===r)return null;let i=n(r)||n(s);return a[e][i]}),o=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return i(e,d,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...o}[r]):({...l,...o})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,r)=>{let{className:t,variant:a,size:l,asChild:n=!1,...i}=e;return(0,s.jsx)("button",{className:(0,d.cn)(o({variant:a,size:l,className:t})),ref:r,...i})});c.displayName="Button"},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},6695:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i});var s=t(5155),a=t(2115),l=t(9434);let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});d.displayName="CardTitle",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})}).displayName="CardDescription";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},8674:(e,r,t)=>{Promise.resolve().then(t.bind(t,3738))},8749:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var s=t(2596),a=t(9688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,533,13,441,684,358],()=>r(8674)),_N_E=e.O()}]);