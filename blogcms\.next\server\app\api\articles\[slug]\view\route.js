"use strict";(()=>{var e={};e.id=343,e.ids=[343],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},33984:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>u});var o=t(96559),i=t(48088),a=t(37719),n=t(32190),p=t(31183);async function u(e,{params:r}){try{let{slug:t}=await r,s=await p.z.article.findUnique({where:{slug:t},select:{id:!0}});if(!s)return n.NextResponse.json({error:"Article not found"},{status:404});let o=e.headers.get("x-forwarded-for"),i=o?o.split(",")[0]:e.headers.get("x-real-ip")||"unknown",a=e.headers.get("user-agent")||"unknown";return await p.z.article.update({where:{id:s.id},data:{viewCount:{increment:1}}}),await p.z.articleView.create({data:{articleId:s.id,ipAddress:i,userAgent:a}}),n.NextResponse.json({success:!0})}catch(e){return console.error("Error tracking article view:",e),n.NextResponse.json({error:"Failed to track view"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/articles/[slug]/view/route",pathname:"/api/articles/[slug]/view",filename:"route",bundlePath:"app/api/articles/[slug]/view/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\articles\\[slug]\\view\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:x}=d;function w(){return(0,a.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,275],()=>t(33984));module.exports=s})();