"use strict";exports.id=351,exports.ids=[351],exports.modules={62904:(e,s,r)=>{r.d(s,{d:()=>eh});var t=r(60687),a=r(7203),l=r(39710),i=r(79801),o=r(84963),n=r(65557),d=r(94157),c=r(84082),u=r(1937),m=r(2188),g=r(84633),b=r(55023),h=r(98926),x=r(61070),p=r(68418),j=r(94249),v=r(36682),y=r(21500),f=r(56286),N=r(13659),w=r(82221),A=r(70297),C=r(27235),k=r(94107),F=r(54427),$=r(84199),M=r(7989),L=r(43210),B=r(51934),S=r(27777),E=r(11922),T=r(44692),D=r(75687),I=r(80375),H=r(45984),P=r(69169),U=r(21782),R=r(25366),z=r(14290),V=r(77767),W=r(98916),O=r(95999),Y=r(80051),G=r(8366),Q=r(91304),q=r(47342),J=r(37461),X=r(9005),Z=r(2943),K=r(82164),_=r(53411),ee=r(98971),es=r(27063),er=r(69282),et=r(58165),ea=r(84084),el=r(10022),ei=r(54388),eo=r(31110);function en({editor:e,onOpenMediaDialog:s,onOpenChartDialog:r,onOpenTableDialog:a}){let[l,i]=(0,L.useState)(!1),[o,n]=(0,L.useState)(!1),[d,c]=(0,L.useState)(!1);return(0,t.jsxs)("div",{className:"border-b bg-gray-50 p-2 flex flex-wrap gap-1 items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleBold().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("bold")?"bg-gray-300":""}`,title:"Bold",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleItalic().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("italic")?"bg-gray-300":""}`,title:"Italic",children:(0,t.jsx)(E.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleUnderline().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("underline")?"bg-gray-300":""}`,title:"Underline",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleStrike().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("strike")?"bg-gray-300":""}`,title:"Strikethrough",children:(0,t.jsx)(D.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleCode().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("code")?"bg-gray-300":""}`,title:"Inline Code",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleHeading({level:1}).run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("heading",{level:1})?"bg-gray-300":""}`,title:"Heading 1",children:(0,t.jsx)(H.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleHeading({level:2}).run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("heading",{level:2})?"bg-gray-300":""}`,title:"Heading 2",children:(0,t.jsx)(P.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleHeading({level:3}).run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("heading",{level:3})?"bg-gray-300":""}`,title:"Heading 3",children:(0,t.jsx)(U.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleBulletList().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("bulletList")?"bg-gray-300":""}`,title:"Bullet List",children:(0,t.jsx)(R.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleOrderedList().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("orderedList")?"bg-gray-300":""}`,title:"Numbered List",children:(0,t.jsx)(z.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleTaskList().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("taskList")?"bg-gray-300":""}`,title:"Task List",children:(0,t.jsx)(V.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleBlockquote().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("blockquote")?"bg-gray-300":""}`,title:"Quote",children:(0,t.jsx)(W.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,t.jsx)("button",{onClick:()=>e.chain().focus().setTextAlign("left").run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive({textAlign:"left"})?"bg-gray-300":""}`,title:"Align Left",children:(0,t.jsx)(O.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().setTextAlign("center").run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive({textAlign:"center"})?"bg-gray-300":""}`,title:"Align Center",children:(0,t.jsx)(Y.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().setTextAlign("right").run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive({textAlign:"right"})?"bg-gray-300":""}`,title:"Align Right",children:(0,t.jsx)(G.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().setTextAlign("justify").run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive({textAlign:"justify"})?"bg-gray-300":""}`,title:"Justify",children:(0,t.jsx)(Q.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,t.jsx)("button",{onClick:()=>{let s=window.prompt("Enter URL:");s&&e.chain().focus().setLink({href:s}).run()},className:`p-2 rounded hover:bg-gray-200 ${e.isActive("link")?"bg-gray-300":""}`,title:"Add Link",children:(0,t.jsx)(q.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().unsetLink().run(),className:"p-2 rounded hover:bg-gray-200",title:"Remove Link",disabled:!e.isActive("link"),children:(0,t.jsx)(J.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,t.jsx)("button",{type:"button",onClick:s,className:"p-2 rounded hover:bg-gray-200",title:"Insert Media (Images, Videos, Audio, PDFs)",children:(0,t.jsx)(X.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>{let s=window.prompt("Enter YouTube URL:");s&&e.chain().focus().setYoutubeVideo({src:s}).run()},className:"p-2 rounded hover:bg-gray-200",title:"Insert Video",children:(0,t.jsx)(Z.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:a,className:"p-2 rounded hover:bg-gray-200",title:"Insert Table",children:(0,t.jsx)(K.A,{className:"h-4 w-4"})}),r&&(0,t.jsx)("button",{type:"button",onClick:r,className:"p-2 rounded hover:bg-gray-200",title:"Insert Chart",children:(0,t.jsx)(_.A,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2 relative",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{onClick:()=>i(!l),className:"p-2 rounded hover:bg-gray-200",title:"Text Color",children:(0,t.jsx)(ee.A,{className:"h-4 w-4"})}),l&&(0,t.jsx)("div",{className:"absolute top-full left-0 mt-1 p-2 bg-white border rounded shadow-lg z-10 grid grid-cols-5 gap-1",children:["#000000","#374151","#6B7280","#9CA3AF","#EF4444","#F97316","#F59E0B","#EAB308","#84CC16","#22C55E","#10B981","#14B8A6","#06B6D4","#0EA5E9","#3B82F6","#6366F1","#8B5CF6","#A855F7","#D946EF","#EC4899"].map(s=>(0,t.jsx)("button",{onClick:()=>{e.chain().focus().setColor(s).run(),i(!1)},className:"w-6 h-6 rounded border",style:{backgroundColor:s}},s))})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{onClick:()=>n(!o),className:"p-2 rounded hover:bg-gray-200",title:"Highlight",children:(0,t.jsx)(es.A,{className:"h-4 w-4"})}),o&&(0,t.jsx)("div",{className:"absolute top-full left-0 mt-1 p-2 bg-white border rounded shadow-lg z-10 grid grid-cols-5 gap-1",children:["#FEF3C7","#FDE68A","#FCD34D","#F59E0B","#FECACA","#FCA5A5","#F87171","#EF4444","#D1FAE5","#A7F3D0","#6EE7B7","#10B981","#DBEAFE","#93C5FD","#60A5FA","#3B82F6","#E0E7FF","#C7D2FE","#A5B4FC","#6366F1"].map(s=>(0,t.jsx)("button",{onClick:()=>{e.chain().focus().toggleHighlight({color:s}).run(),n(!1)},className:"w-6 h-6 rounded border",style:{backgroundColor:s}},s))})]})]}),(0,t.jsx)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2 relative",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{onClick:()=>c(!d),className:"p-2 rounded hover:bg-gray-200",title:"Font Family",children:(0,t.jsx)(er.A,{className:"h-4 w-4"})}),d&&(0,t.jsx)("div",{className:"absolute top-full left-0 mt-1 p-2 bg-white border rounded shadow-lg z-10 min-w-32",children:["Inter","Arial","Helvetica","Times New Roman","Georgia","Courier New","Monaco","Roboto","Open Sans","Lato"].map(s=>(0,t.jsx)("button",{onClick:()=>{e.chain().focus().setFontFamily(s).run(),c(!1)},className:"block w-full text-left px-2 py-1 hover:bg-gray-100 text-sm",style:{fontFamily:s},children:s},s))})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleSubscript().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("subscript")?"bg-gray-300":""}`,title:"Subscript",children:(0,t.jsx)(et.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleSuperscript().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("superscript")?"bg-gray-300":""}`,title:"Superscript",children:(0,t.jsx)(ea.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:(0,t.jsx)("button",{onClick:()=>e.chain().focus().toggleCodeBlock().run(),className:`p-2 rounded hover:bg-gray-200 ${e.isActive("codeBlock")?"bg-gray-300":""}`,title:"Code Block",children:(0,t.jsx)(el.A,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("button",{onClick:()=>e.chain().focus().undo().run(),className:"p-2 rounded hover:bg-gray-200",title:"Undo",disabled:!e.can().undo(),children:(0,t.jsx)(ei.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>e.chain().focus().redo().run(),className:"p-2 rounded hover:bg-gray-200",title:"Redo",disabled:!e.can().redo(),children:(0,t.jsx)(eo.A,{className:"h-4 w-4"})})]})]})}var ed=r(11860),ec=r(93613),eu=r(16023),em=r(5336);function eg({isOpen:e,onClose:s,onInsert:r}){let[a,l]=(0,L.useState)("upload"),[i,o]=(0,L.useState)(""),[n,d]=(0,L.useState)(!1),[c,u]=(0,L.useState)([]),[m,g]=(0,L.useState)(""),[b,h]=(0,L.useState)(""),x={image:0xa00000,video:0x6400000,pdf:0x1900000,audio:0x3200000},p=e=>{let s=e.type.startsWith("image/"),r=e.type.startsWith("video/"),t="application/pdf"===e.type,a=e.type.startsWith("audio/");if(!s&&!r&&!t&&!a)return{valid:!1,error:"Unsupported file type. Please upload images, videos, PDFs, or audio files."};let l=x.image;if(r?l=x.video:t?l=x.pdf:a&&(l=x.audio),e.size>l){let e=Math.round(l/1048576);return{valid:!1,error:`File size too large. Maximum size is ${e}MB.`}}return{valid:!0}},j=(0,L.useCallback)(async e=>{g("");let s=e[0];if(!s)return;let r=p(s);if(!r.valid)return void g(r.error||"Invalid file");d(!0);try{let e=new FileReader;e.onload=()=>{let r=e.result,t="image";s.type.startsWith("video/")?t="video":"application/pdf"===s.type?t="pdf":s.type.startsWith("audio/")&&(t="audio"),u([{file:s,preview:r,type:t}])},e.readAsDataURL(s)}catch(e){console.error("Upload failed:",e),g("Failed to process file. Please try again.")}finally{d(!1)}},[]),{getRootProps:v,getInputProps:y,isDragActive:f}=(0,B.VB)({onDrop:j,accept:{"image/*":[".png",".jpg",".jpeg",".gif",".webp",".svg"],"video/*":[".mp4",".webm",".ogg"],"audio/*":[".mp3",".wav",".ogg",".m4a"],"application/pdf":[".pdf"]},maxFiles:1}),N=e=>{let s=e.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);if(s)return{embedUrl:`https://www.youtube.com/embed/${s[1]}`,type:"youtube"};let r=e.match(/(?:vimeo\.com\/)(?:.*\/)?(\d+)/);if(r)return{embedUrl:`https://player.vimeo.com/video/${r[1]}`,type:"vimeo"};let t=e.match(/(?:dailymotion\.com\/video\/|dai\.ly\/)([a-zA-Z0-9]+)/);return t?{embedUrl:`https://www.dailymotion.com/embed/video/${t[1]}`,type:"dailymotion"}:null},w=e=>{r(e.preview,e.type),u([]),s()};return e?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Insert Media"}),(0,t.jsx)("button",{type:"button",onClick:s,className:"p-1 hover:bg-gray-100 rounded",title:"Close dialog",children:(0,t.jsx)(ed.A,{className:"h-5 w-5"})})]}),m&&(0,t.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2",children:[(0,t.jsx)(ec.A,{className:"h-4 w-4 text-red-500"}),(0,t.jsx)("span",{className:"text-red-700 text-sm",children:m})]}),(0,t.jsxs)("div",{className:"flex border-b mb-4",children:[(0,t.jsxs)("button",{type:"button",onClick:()=>l("upload"),className:`px-4 py-2 font-medium ${"upload"===a?"border-b-2 border-blue-500 text-blue-600":"text-gray-600 hover:text-gray-800"}`,children:[(0,t.jsx)(eu.A,{className:"h-4 w-4 inline mr-2"}),"Upload"]}),(0,t.jsxs)("button",{type:"button",onClick:()=>l("url"),className:`px-4 py-2 font-medium ${"url"===a?"border-b-2 border-blue-500 text-blue-600":"text-gray-600 hover:text-gray-800"}`,children:[(0,t.jsx)(q.A,{className:"h-4 w-4 inline mr-2"}),"URL"]}),(0,t.jsxs)("button",{type:"button",onClick:()=>l("embed"),className:`px-4 py-2 font-medium ${"embed"===a?"border-b-2 border-blue-500 text-blue-600":"text-gray-600 hover:text-gray-800"}`,children:[(0,t.jsx)(Z.A,{className:"h-4 w-4 inline mr-2"}),"Embed"]})]}),"upload"===a&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{...v(),className:`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${f?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"}`,children:[(0,t.jsx)("input",{...y()}),(0,t.jsx)(eu.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-400"}),(0,t.jsx)("p",{className:"text-gray-600 mb-2",children:f?"Drop the file here...":"Drag & drop a file here, or click to select"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Supports: Images (PNG, JPG, GIF, WebP, SVG), Videos (MP4, WebM), Audio (MP3, WAV), PDFs"}),(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Max sizes: Images 10MB, Videos 100MB, Audio 50MB, PDFs 25MB"})]}),n&&(0,t.jsxs)("div",{className:"mt-4 text-center",children:[(0,t.jsx)("div",{className:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Processing file..."})]}),c.length>0&&(0,t.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-700",children:"Preview:"}),c.map((e,s)=>(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(em.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.file.name}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(",Math.round(e.file.size/1024),"KB)"]})]})}),"image"===e.type&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsx)("img",{src:e.preview,alt:"Preview",className:"max-w-full h-32 object-contain rounded border"})}),"video"===e.type&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsx)("video",{src:e.preview,className:"max-w-full h-32 rounded border",controls:!0})}),"audio"===e.type&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsx)("audio",{src:e.preview,className:"w-full",controls:!0})}),(0,t.jsxs)("button",{type:"button",onClick:()=>w(e),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700",children:["Insert ",e.type]})]},s))]})]}),"url"===a&&(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Media URL"}),(0,t.jsx)("input",{type:"url",value:i,onChange:e=>o(e.target.value),placeholder:"https://example.com/image.jpg",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("p",{className:"font-medium mb-2",children:"Supported URLs:"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(X.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Direct image links (.jpg, .png, .gif, etc.)"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"YouTube, Vimeo, Dailymotion, or direct video links"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(el.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"PDF documents and audio files"})]})]}),(0,t.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:[(0,t.jsx)("p",{children:"Examples:"}),(0,t.jsx)("p",{children:"• https://youtube.com/watch?v=dQw4w9WgXcQ"}),(0,t.jsx)("p",{children:"• https://youtu.be/dQw4w9WgXcQ"}),(0,t.jsx)("p",{children:"• https://vimeo.com/123456789"}),(0,t.jsx)("p",{children:"• https://example.com/image.jpg"})]})]}),(0,t.jsx)("button",{type:"button",onClick:()=>{if(!i.trim())return;g("");let e="image",t=i.trim(),a=N(t);a?(e="video",t=a.embedUrl):t.includes("vimeo.com")||t.includes("youtube.com")||t.includes("youtu.be")||t.includes("dailymotion.com")?e="video":t.endsWith(".pdf")?e="pdf":t.match(/\.(mp3|wav|ogg|m4a)$/i)?e="audio":t.match(/\.(mp4|webm|ogg|avi|mov)$/i)&&(e="video"),r(t,e),o(""),s()},disabled:!i.trim(),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Insert Media"})]})}),"embed"===a&&(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Embed Code"}),(0,t.jsx)("textarea",{value:b,onChange:e=>h(e.target.value),placeholder:'<iframe src="https://www.youtube.com/embed/..." width="560" height="315" frameborder="0"></iframe>',rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)("p",{className:"font-medium mb-2",children:"Supported embed codes:"}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"YouTube, Vimeo, Dailymotion iframes"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Social media embeds (Twitter, Instagram)"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(Z.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Other iframe-based embeds"})]})]}),(0,t.jsx)("div",{className:"mt-2 text-xs text-gray-500",children:(0,t.jsx)("p",{children:"Paste the complete embed code from the platform's share/embed option."})})]}),(0,t.jsx)("button",{type:"button",onClick:()=>{b.trim()&&(g(""),r(b.trim(),"video"),h(""),s())},disabled:!b.trim(),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Insert Embed"})]})})]})}):null}function eb({isOpen:e,onClose:s,onInsert:r}){let[a,l]=(0,L.useState)(3),[i,o]=(0,L.useState)(3);return e?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl mx-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold flex items-center gap-2",children:[(0,t.jsx)(K.A,{className:"h-5 w-5"}),"Insert Table"]}),(0,t.jsx)("button",{type:"button",onClick:s,className:"p-1 hover:bg-gray-100 rounded",children:(0,t.jsx)(ed.A,{className:"h-5 w-5"})})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Rows"}),(0,t.jsx)("input",{type:"number",min:"2",max:"20",value:a,onChange:e=>l(Math.max(2,Math.min(20,parseInt(e.target.value)||2))),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Includes header row"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Columns"}),(0,t.jsx)("input",{type:"number",min:"1",max:"10",value:i,onChange:e=>o(Math.max(1,Math.min(10,parseInt(e.target.value)||1))),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Preview"}),(0,t.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,t.jsx)("table",{className:"w-full border-collapse",children:(0,t.jsx)("tbody",{children:(()=>{let e=[];e.push((0,t.jsx)("tr",{className:"bg-gray-100",children:Array.from({length:i},(e,s)=>(0,t.jsxs)("th",{className:"border border-gray-300 p-2 text-sm font-semibold",children:["Header ",s+1]},s))},"header"));for(let s=1;s<a;s++)e.push((0,t.jsx)("tr",{children:Array.from({length:i},(e,r)=>(0,t.jsxs)("td",{className:"border border-gray-300 p-2 text-sm",children:["Cell ",s,"-",r+1]},r))},s));return e})()})})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Quick Select"}),(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,t.jsx)("button",{type:"button",onClick:()=>{l(3),o(3)},className:"p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm",children:"3\xd73"}),(0,t.jsx)("button",{type:"button",onClick:()=>{l(4),o(4)},className:"p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm",children:"4\xd74"}),(0,t.jsx)("button",{type:"button",onClick:()=>{l(5),o(3)},className:"p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm",children:"5\xd73"}),(0,t.jsx)("button",{type:"button",onClick:()=>{l(3),o(5)},className:"p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm",children:"3\xd75"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,t.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,t.jsx)("button",{type:"button",onClick:()=>{r(a,i),s()},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Insert Table"})]})]})]})}):null}function eh({content:e="",onChange:s,placeholder:r="Start writing your story...",className:S="",readOnly:E=!1}){let[T,D]=(0,L.useState)(!1),[I,H]=(0,L.useState)(!1),[P,U]=(0,L.useState)(!1),R=(0,C.$)();R.register("javascript",k.A),R.register("typescript",F.A),R.register("html",$.A),R.register("css",M.A);let z=(0,a.hG)({extensions:[l.A.configure({codeBlock:!1}),i.Ay.configure({HTMLAttributes:{class:"rounded-lg max-w-full h-auto"}}),o.Ay.configure({resizable:!0,HTMLAttributes:{class:"border-collapse border border-border my-4"}}),n.A.configure({HTMLAttributes:{class:"border border-border"}}),c.h.configure({HTMLAttributes:{class:"border border-border bg-muted font-semibold p-2"}}),d.A.configure({HTMLAttributes:{class:"border border-border p-2"}}),u.A.configure({width:640,height:480,HTMLAttributes:{class:"rounded-lg my-4"}}),m.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-primary hover:text-primary/80 underline"}}),g.A.configure({types:["heading","paragraph"]}),b.A.configure({types:["textStyle"]}),h.Ay.configure({multicolor:!0}),x.A.configure({types:["textStyle"]}),p.A,j.A,v.A,y.A.configure({lowlight:R,HTMLAttributes:{class:"bg-muted rounded-lg p-4 my-4 overflow-x-auto"}}),f.A.configure({HTMLAttributes:{class:"not-prose"}}),N.Ay.configure({nested:!0,HTMLAttributes:{class:"flex items-start gap-2"}}),w.A,A.A.configure({placeholder:r})],content:e,editable:!E,onUpdate:({editor:e})=>{s?.(e.getHTML())},editorProps:{attributes:{class:`prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4 ${S}`}}}),V=(0,L.useCallback)(e=>{e.forEach(e=>{if(e.type.startsWith("image/")){let s=new FileReader;s.onload=()=>{let e=s.result;z?.chain().focus().setImage({src:e}).run()},s.readAsDataURL(e)}})},[z]),{getRootProps:W,getInputProps:O,isDragActive:Y}=(0,B.VB)({onDrop:V,accept:{"image/*":[".png",".jpg",".jpeg",".gif",".webp"],"video/*":[".mp4",".webm",".ogg"],"application/pdf":[".pdf"]},noClick:!0});return z?(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[!E&&(0,t.jsx)(en,{editor:z,onOpenMediaDialog:()=>D(!0),onOpenTableDialog:()=>U(!0)}),(0,t.jsxs)("div",{...W(),className:`relative ${Y?"bg-primary/5 border-primary/30":""}`,children:[(0,t.jsx)("input",{...O()}),Y&&(0,t.jsx)("div",{className:"absolute inset-0 bg-primary/5 bg-opacity-90 flex items-center justify-center z-10 border-2 border-dashed border-primary/30",children:(0,t.jsx)("p",{className:"text-primary font-medium",children:"Drop files here to upload"})}),(0,t.jsx)(a.$Z,{editor:z})]}),!E&&z.storage.characterCount&&(0,t.jsxs)("div",{className:"px-4 py-2 bg-muted border-t text-sm text-muted-foreground flex justify-between",children:[(0,t.jsxs)("span",{children:[z.storage.characterCount.characters()," characters, ",z.storage.characterCount.words()," words"]}),(0,t.jsxs)("span",{children:["Reading time: ~",Math.ceil(z.storage.characterCount.words()/200)," min"]})]}),(0,t.jsx)(eg,{isOpen:T,onClose:()=>D(!1),onInsert:(e,s)=>{if("image"===s)z.chain().focus().setImage({src:e}).run();else if("video"===s)if(e.includes("youtube.com/embed/")||e.includes("youtu.be/")||e.includes("youtube.com/watch"))z.chain().focus().setYoutubeVideo({src:e}).run();else if(e.startsWith("<iframe")||e.startsWith("<embed"))z.chain().focus().insertContent(e).run();else{let s=`<video controls class="max-w-full h-auto rounded-lg my-4"><source src="${e}" type="video/mp4">Your browser does not support the video tag.</video>`;z.chain().focus().insertContent(s).run()}else if("audio"===s){let s=`<audio controls class="w-full my-4"><source src="${e}" type="audio/mpeg">Your browser does not support the audio tag.</audio>`;z.chain().focus().insertContent(s).run()}else if("pdf"===s){let s=`<div class="pdf-embed my-4 p-4 border rounded-lg bg-gray-50"><a href="${e}" target="_blank" class="flex items-center gap-2 text-blue-600 hover:text-blue-800"><svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path d="M4 18h12V6l-4-4H4v16z"/></svg>View PDF Document</a></div>`;z.chain().focus().insertContent(s).run()}}}),(0,t.jsx)(eb,{isOpen:P,onClose:()=>U(!1),onInsert:(e,s)=>{z.chain().focus().insertTable({rows:e,cols:s,withHeaderRow:!0}).run()}})]}):(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-12 bg-muted rounded mb-4"}),(0,t.jsx)("div",{className:"h-64 bg-muted rounded"})]})}},70440:(e,s,r)=>{r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]}};