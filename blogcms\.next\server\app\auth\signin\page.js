(()=>{var e={};e.id=680,e.ids=[680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(49384),a=t(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}},10182:(e,r,t)=>{Promise.resolve().then(t.bind(t,43476))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21611:(e,r,t)=>{"use strict";t.d(r,{$:()=>c});var s=t(60687),a=t(43210),n=t(49384);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$;var l=t(4780);let d=((e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return o(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:n}=r,l=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],s=null==n?void 0:n[e];if(null===r)return null;let o=i(r)||i(s);return a[e][o]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return o(e,l,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...n,...d}[r]):({...n,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...n},i)=>(0,s.jsx)("button",{className:(0,l.cn)(d({variant:r,size:t,className:e})),ref:i,...n}));c.displayName="Button"},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43476:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(60687),a=t(43210),n=t(82136),i=t(16189),o=t(85814),l=t.n(o),d=t(93613),c=t(41550),u=t(64021),m=t(12597),p=t(13861),x=t(21611),f=t(44493),h=t(89667);function g(){let e=(0,i.useRouter)(),r=(0,i.useSearchParams)(),t=r.get("callbackUrl")||"/",o=r.get("error"),[g,v]=(0,a.useState)({email:"",password:""}),[b,y]=(0,a.useState)(!1),[w,j]=(0,a.useState)(!1),[N,k]=(0,a.useState)(""),A=async r=>{r.preventDefault(),j(!0),k("");try{let r=await (0,n.signIn)("credentials",{email:g.email,password:g.password,redirect:!1});r?.error?k("Invalid email or password"):await (0,n.getSession)()&&(e.push(t),e.refresh())}catch{k("An error occurred. Please try again.")}finally{j(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)(f.Zp,{children:[(0,s.jsxs)(f.aR,{className:"text-center",children:[(0,s.jsx)(f.ZB,{className:"text-3xl font-extrabold",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Or"," ",(0,s.jsx)(l(),{href:"/auth/signup",className:"font-medium text-primary hover:text-primary/80",children:"create a new account"})]})]}),(0,s.jsxs)(f.Wu,{children:[(o||N)&&(0,s.jsx)("div",{className:"bg-destructive/15 border border-destructive/20 rounded-md p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 text-destructive"}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("p",{className:"text-sm text-destructive",children:N||o&&(e=>{switch(e){case"CredentialsSignin":return"Invalid email or password";case"EmailSignin":return"Error sending email";case"OAuthSignin":return"Error with OAuth provider";case"OAuthCallback":return"Error with OAuth callback";case"OAuthCreateAccount":return"Could not create OAuth account";case"EmailCreateAccount":return"Could not create email account";case"Callback":return"Error in callback";case"OAuthAccountNotLinked":return"OAuth account not linked";case"SessionRequired":return"Please sign in to access this page";default:return"An error occurred during sign in"}})(o)})})]})}),(0,s.jsxs)("form",{className:"space-y-6",onSubmit:A,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground mb-2",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5"}),(0,s.jsx)(h.p,{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:g.email,onChange:e=>v({...g,email:e.target.value}),className:"pl-12",placeholder:"Enter your email"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground mb-2",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5"}),(0,s.jsx)(h.p,{id:"password",name:"password",type:b?"text":"password",autoComplete:"current-password",required:!0,value:g.password,onChange:e=>v({...g,password:e.target.value}),className:"pl-12 pr-12",placeholder:"Enter your password"}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>y(!b),children:b?(0,s.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"}):(0,s.jsx)(p.A,{className:"h-5 w-5 text-muted-foreground"})})]})]})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(l(),{href:"/auth/forgot-password",className:"font-medium text-primary hover:text-primary/80",children:"Forgot your password?"})})}),(0,s.jsx)(x.$,{type:"submit",disabled:w,className:"w-full",children:w?(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Demo accounts:"}),(0,s.jsxs)("div",{className:"mt-2 space-y-1 text-xs text-muted-foreground",children:[(0,s.jsx)("p",{children:"Admin: <EMAIL> / admin123"}),(0,s.jsx)("p",{children:"Author: <EMAIL> / author123"})]})]})]})]})]})})})}},43838:(e,r,t)=>{Promise.resolve().then(t.bind(t,87578))},44493:(e,r,t)=>{"use strict";t.d(r,{Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>o});var s=t(60687),a=t(43210),n=t(4780);let i=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...r})).displayName="CardDescription";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...r}));d.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},87578:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Books\\Blog\\blogcms\\src\\app\\auth\\signin\\page.tsx","default")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687),a=t(43210),n=t(4780);let i=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));i.displayName="Input"},90086:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87578)),"C:\\D\\Books\\Blog\\blogcms\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,80871)),"C:\\D\\Books\\Blog\\blogcms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\D\\Books\\Blog\\blogcms\\src\\app\\auth\\signin\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93613:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,789,658,945,575],()=>t(90086));module.exports=s})();