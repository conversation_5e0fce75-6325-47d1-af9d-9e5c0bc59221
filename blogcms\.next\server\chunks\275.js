exports.id=275,exports.ids=[275],exports.modules={23604:(e,t,i)=>{"use strict";var n=Object.create,r=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,l=Object.getPrototypeOf,o=Object.prototype.hasOwnProperty,u=(e,t)=>()=>(e&&(t=e(e=0)),t),d=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),c=(e,t)=>{for(var i in t)r(e,i,{get:t[i],enumerable:!0})},f=(e,t,i,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of a(t))o.call(e,l)||l===i||r(e,l,{get:()=>t[l],enumerable:!(n=s(t,l))||n.enumerable});return e},p=(e,t,i)=>(i=null!=e?n(l(e)):{},f(!t&&e&&e.__esModule?i:r(i,"default",{value:e,enumerable:!0}),e)),h=d((e,t)=>{t.exports=(e,t=process.argv)=>{let i=e.startsWith("-")?"":1===e.length?"-":"--",n=t.indexOf(i+e),r=t.indexOf("--");return -1!==n&&(-1===r||n<r)}}),m=d((e,t)=>{var n,r=i(48161),s=i(7066),a=h(),{env:l}=process;function o(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function u(e,t){if(0===n)return 0;if(a("color=16m")||a("color=full")||a("color=truecolor"))return 3;if(a("color=256"))return 2;if(e&&!t&&void 0===n)return 0;let i=n||0;if("dumb"===l.TERM)return i;if("win32"===process.platform){let e=r.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in l)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in l)||"codeship"===l.CI_NAME?1:i;if("TEAMCITY_VERSION"in l)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(l.TEAMCITY_VERSION);if("truecolor"===l.COLORTERM)return 3;if("TERM_PROGRAM"in l){let e=parseInt((l.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(l.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(l.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(l.TERM)||"COLORTERM"in l?1:i}a("no-color")||a("no-colors")||a("color=false")||a("color=never")?n=0:(a("color")||a("colors")||a("color=true")||a("color=always"))&&(n=1),"FORCE_COLOR"in l&&(n="true"===l.FORCE_COLOR?1:"false"===l.FORCE_COLOR?0:0===l.FORCE_COLOR.length?1:Math.min(parseInt(l.FORCE_COLOR,10),3)),t.exports={supportsColor:function(e){return o(u(e,e&&e.isTTY))},stdout:o(u(!0,s.isatty(1))),stderr:o(u(!0,s.isatty(2)))}}),g=d((e,t)=>{var i=m(),n=h();function r(e){if(/^\d{3,4}$/.test(e)){let t=/(\d{1,2})(\d{2})/.exec(e)||[];return{major:0,minor:parseInt(t[1],10),patch:parseInt(t[2],10)}}let t=(e||"").split(".").map(e=>parseInt(e,10));return{major:t[0],minor:t[1],patch:t[2]}}function s(e){let{CI:t,FORCE_HYPERLINK:s,NETLIFY:a,TEAMCITY_VERSION:l,TERM_PROGRAM:o,TERM_PROGRAM_VERSION:u,VTE_VERSION:d,TERM:c}=process.env;if(s)return!(s.length>0&&0===parseInt(s,10));if(n("no-hyperlink")||n("no-hyperlinks")||n("hyperlink=false")||n("hyperlink=never"))return!1;if(n("hyperlink=true")||n("hyperlink=always")||a)return!0;if(!i.supportsColor(e)||e&&!e.isTTY)return!1;if("WT_SESSION"in process.env)return!0;if("win32"===process.platform||t||l)return!1;if(o){let e=r(u||"");switch(o){case"iTerm.app":return 3===e.major?e.minor>=1:e.major>3;case"WezTerm":return e.major>=0x1343cac;case"vscode":return e.major>1||1===e.major&&e.minor>=72;case"ghostty":return!0}}if(d){if("0.50.0"===d)return!1;let e=r(d);return e.major>0||e.minor>=50}return"alacritty"===c}t.exports={supportsHyperlink:s,stdout:s(process.stdout),stderr:s(process.stderr)}}),y=d((e,t)=>{t.exports={name:"@prisma/internals",version:"6.8.2",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.1","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e","@prisma/schema-engine-wasm":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}}),v=d((e,t)=>{t.exports={name:"@prisma/engines-version",version:"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"2060c79ba17c6bb9f5823312b6f6b7f4a845738e"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}}),w=d(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.enginesVersion=void 0,e.enginesVersion=v().prisma.enginesVersion}),b=d((e,t)=>{t.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((e,t)=>Math.min(e,t.length),1/0):0}}),E=d((e,t)=>{t.exports=(e,t=1,i)=>{if(i={indent:" ",includeEmptyLines:!1,...i},"string"!=typeof e)throw TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if("number"!=typeof t)throw TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if("string"!=typeof i.indent)throw TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof i.indent}\``);if(0===t)return e;let n=i.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,i.indent.repeat(t))}}),A=d((e,t)=>{t.exports=({onlyFirst:e=!1}={})=>RegExp("[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))",e?void 0:"g")}),R=d((e,t)=>{var i=A();t.exports=e=>"string"==typeof e?e.replace(i(),""):e}),T=d((e,t)=>{t.exports={name:"dotenv",version:"16.5.0",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},homepage:"https://github.com/motdotla/dotenv#readme",funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}}),S=d((e,t)=>{var n=i(73024),r=i(76760),s=i(48161),a=i(77598),l=T().version,o=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function u(e){console.log(`[dotenv@${l}][DEBUG] ${e}`)}function d(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function c(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let i of e.path)n.existsSync(i)&&(t=i.endsWith(".vault")?i:`${i}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=r.resolve(process.cwd(),".env.vault");return n.existsSync(t)?t:null}function f(e){return"~"===e[0]?r.join(s.homedir(),e.slice(1)):e}var p={configDotenv:function(e){let t=r.resolve(process.cwd(),".env"),i="utf8",s=!!(e&&e.debug);e&&e.encoding?i=e.encoding:s&&u("No encoding is specified. UTF-8 is used by default");let a=[t];if(e&&e.path)if(Array.isArray(e.path))for(let t of(a=[],e.path))a.push(f(t));else a=[f(e.path)];let l,o={};for(let t of a)try{let r=p.parse(n.readFileSync(t,{encoding:i}));p.populate(o,r,e)}catch(e){s&&u(`Failed to load ${t} ${e.message}`),l=e}let d=process.env;return e&&null!=e.processEnv&&(d=e.processEnv),p.populate(d,o,e),l?{parsed:o,error:l}:{parsed:o}},_configVault:function(e){e&&e.debug&&u("Loading env from encrypted .env.vault");let t=p._parseVault(e),i=process.env;return e&&null!=e.processEnv&&(i=e.processEnv),p.populate(i,t,e),{parsed:t}},_parseVault:function(e){let t=c(e),i=p.configDotenv({path:t});if(!i.parsed){let e=Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw e.code="MISSING_DATA",e}let n=d(e).split(","),r=n.length,s;for(let e=0;e<r;e++)try{let t=n[e].trim(),r=function(e,t){let i;try{i=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let n=i.password;if(!n){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let r=i.searchParams.get("environment");if(!r){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let s=`DOTENV_VAULT_${r.toUpperCase()}`,a=e.parsed[s];if(!a){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${s} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:a,key:n}}(i,t);s=p.decrypt(r.ciphertext,r.key);break}catch(t){if(e+1>=r)throw t}return p.parse(s)},config:function(e){var t;if(0===d(e).length)return p.configDotenv(e);let i=c(e);return i?p._configVault(e):(t=`You set DOTENV_KEY but you are missing a .env.vault file at ${i}. Did you forget to build it?`,console.log(`[dotenv@${l}][WARN] ${t}`),p.configDotenv(e))},decrypt:function(e,t){let i=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),r=n.subarray(0,12),s=n.subarray(-16);n=n.subarray(12,-16);try{let e=a.createDecipheriv("aes-256-gcm",i,r);return e.setAuthTag(s),`${e.update(n)}${e.final()}`}catch(n){let e=n instanceof RangeError,t="Invalid key length"===n.message,i="Unsupported state or unable to authenticate data"===n.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(i){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw n}},parse:function(e){let t,i={},n=e.toString();for(n=n.replace(/\r\n?/mg,`
`);null!=(t=o.exec(n));){let e=t[1],n=t[2]||"",r=(n=n.trim())[0];n=n.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===r&&(n=(n=n.replace(/\\n/g,`
`)).replace(/\\r/g,"\r")),i[e]=n}return i},populate:function(e,t,i={}){let n=!!(i&&i.debug),r=!!(i&&i.override);if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let i of Object.keys(t))Object.prototype.hasOwnProperty.call(e,i)?(!0===r&&(e[i]=t[i]),n&&u(!0===r?`"${i}" is already defined and WAS overwritten`:`"${i}" is already defined and was NOT overwritten`)):e[i]=t[i]}};t.exports.configDotenv=p.configDotenv,t.exports._configVault=p._configVault,t.exports._parseVault=p._parseVault,t.exports.config=p.config,t.exports.decrypt=p.decrypt,t.exports.parse=p.parse,t.exports.populate=p.populate,t.exports=p}),I=d((e,t)=>{t.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let i=new URL(`${t}/issues/new`);for(let t of["body","title","labels","template","milestone","assignee","projects"]){let n=e[t];if(void 0!==n){if("labels"===t||"projects"===t){if(!Array.isArray(n))throw TypeError(`The \`${t}\` option should be an array`);n=n.join(",")}i.searchParams.set(t,n)}}return i.toString()},t.exports.default=t.exports}),x=d((e,t)=>{t.exports=function(){function e(e,t,i,n,r){return e<t||i<t?e>i?i+1:e+1:n===r?t:t+1}return function(t,i){if(t===i)return 0;if(t.length>i.length){var n=t;t=i,i=n}for(var r=t.length,s=i.length;r>0&&t.charCodeAt(r-1)===i.charCodeAt(s-1);)r--,s--;for(var a=0;a<r&&t.charCodeAt(a)===i.charCodeAt(a);)a++;if(r-=a,s-=a,0===r||s<3)return s;var l,o,u,d,c,f,p,h,m,g,y,v,w=0,b=[];for(l=0;l<r;l++)b.push(l+1),b.push(t.charCodeAt(a+l));for(var E=b.length-1;w<s-3;)for(m=i.charCodeAt(a+(o=w)),g=i.charCodeAt(a+(u=w+1)),y=i.charCodeAt(a+(d=w+2)),v=i.charCodeAt(a+(c=w+3)),f=w+=4,l=0;l<E;l+=2)o=e(p=b[l],o,u,m,h=b[l+1]),u=e(o,u,d,g,h),d=e(u,d,c,y,h),f=e(d,c,f,v,h),b[l]=f,c=d,d=u,u=o,o=p;for(;w<s;)for(m=i.charCodeAt(a+(o=w)),f=++w,l=0;l<E;l+=2)p=b[l],b[l]=f=e(p,o,f,m,b[l+1]),o=p;return f}}()}),P=u(()=>{}),q=u(()=>{}),k={};c(k,{DMMF:()=>n0,Debug:()=>eA,Decimal:()=>nB,Extensions:()=>O,MetricsClient:()=>r7,PrismaClientInitializationError:()=>iy,PrismaClientKnownRequestError:()=>iv,PrismaClientRustPanicError:()=>iw,PrismaClientUnknownRequestError:()=>ib,PrismaClientValidationError:()=>iE,Public:()=>D,Sql:()=>so,createParam:()=>rK,defineDmmfProperty:()=>r6,deserializeJsonResponse:()=>nW,deserializeRawResult:()=>lU,dmmfToRuntimeDataModel:()=>nQ,empty:()=>sc,getPrismaClient:()=>lz,getRuntime:()=>ae,join:()=>su,makeStrictEnum:()=>l0,makeTypedQueryFactory:()=>st,objectEnumValues:()=>rD,raw:()=>sd,serializeJsonQuery:()=>r0,skip:()=>rY,sqltag:()=>sf,warnEnvConflicts:()=>l1,warnOnce:()=>ig}),e.exports=f(r({},"__esModule",{value:!0}),k);var O={};function N(e){return"function"==typeof e?e:t=>t.$extends(e)}function _(e){return e}c(O,{defineExtension:()=>N,getExtensionContext:()=>_});var D={};function U(...e){return e=>e}c(D,{validator:()=>U});var $={};c($,{$:()=>M,bgBlack:()=>eo,bgBlue:()=>ef,bgCyan:()=>eh,bgGreen:()=>ed,bgMagenta:()=>ep,bgRed:()=>eu,bgWhite:()=>em,bgYellow:()=>ec,black:()=>Z,blue:()=>ei,bold:()=>W,cyan:()=>er,dim:()=>H,gray:()=>ea,green:()=>ee,grey:()=>el,hidden:()=>Y,inverse:()=>Q,italic:()=>K,magenta:()=>en,red:()=>X,reset:()=>B,strikethrough:()=>z,underline:()=>J,white:()=>es,yellow:()=>et});var F,V,C,L,j=!0;"u">typeof process&&({FORCE_COLOR:F,NODE_DISABLE_COLORS:V,NO_COLOR:C,TERM:L}=process.env||{},j=process.stdout&&process.stdout.isTTY);var M={enabled:!V&&null==C&&"dumb"!==L&&(null!=F&&"0"!==F||j)};function G(e,t){let i=RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1b[${e}m`,r=`\x1b[${t}m`;return function(e){return M.enabled&&null!=e?n+(~(""+e).indexOf(r)?e.replace(i,r+n):e)+r:e}}var B=G(0,0),W=G(1,22),H=G(2,22),K=G(3,23),J=G(4,24),Q=G(7,27),Y=G(8,28),z=G(9,29),Z=G(30,39),X=G(31,39),ee=G(32,39),et=G(33,39),ei=G(34,39),en=G(35,39),er=G(36,39),es=G(37,39),ea=G(90,39),el=G(90,39),eo=G(40,49),eu=G(41,49),ed=G(42,49),ec=G(43,49),ef=G(44,49),ep=G(45,49),eh=G(46,49),em=G(47,49),eg=["green","yellow","blue","magenta","cyan","red"],ey=[],ev=Date.now(),ew=0,eb="u">typeof process?process.env:{};globalThis.DEBUG??=eb.DEBUG??"",globalThis.DEBUG_COLORS??=!eb.DEBUG_COLORS||"true"===eb.DEBUG_COLORS;var eE={enable(e){"string"==typeof e&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(e=>e.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),i=t.some(t=>""!==t&&"-"!==t[0]&&e.match(RegExp(t.split("*").join(".*")+"$"))),n=t.some(t=>""!==t&&"-"===t[0]&&e.match(RegExp(t.slice(1).split("*").join(".*")+"$")));return i&&!n},log:(...e)=>{let[t,i,...n]=e;(console.warn??console.log)(`${t} ${i}`,...n)},formatters:{}},eA=new Proxy(function(e){let t={color:eg[ew++%eg.length],enabled:eE.enabled(e),namespace:e,log:eE.log,extend:()=>{}};return new Proxy((...e)=>{let{enabled:i,namespace:n,color:r,log:s}=t;if(0!==e.length&&ey.push([n,...e]),ey.length>100&&ey.shift(),eE.enabled(n)||i){let t=e.map(e=>"string"==typeof e?e:function(e,t=2){let i=new Set;return JSON.stringify(e,(e,t)=>{if("object"==typeof t&&null!==t){if(i.has(t))return"[Circular *]";i.add(t)}else if("bigint"==typeof t)return t.toString();return t},t)}(e)),i=`+${Date.now()-ev}ms`;ev=Date.now(),globalThis.DEBUG_COLORS?s($[r](W(n)),...t,$[r](i)):s(n,...t,i)}},{get:(e,i)=>t[i],set:(e,i,n)=>t[i]=n})},{get:(e,t)=>eE[t],set:(e,t,i)=>eE[t]=i}),eR=p(i(73024)),eT="libquery_engine",eS=p(i(31421)),eI=p(i(51455)),ex=p(i(48161)),eP=Symbol.for("@ts-pattern/matcher"),eq=Symbol.for("@ts-pattern/isVariadic"),ek="@ts-pattern/anonymous-select-key",eO=e=>!!(e&&"object"==typeof e),eN=e=>e&&!!e[eP],e_=(e,t,i)=>{if(eN(e)){let{matched:n,selections:r}=e[eP]().match(t);return n&&r&&Object.keys(r).forEach(e=>i(e,r[e])),n}if(eO(e)){if(!eO(t))return!1;if(Array.isArray(e)){if(!Array.isArray(t))return!1;let n=[],r=[],s=[];for(let t of e.keys()){let i=e[t];eN(i)&&i[eq]?s.push(i):s.length?r.push(i):n.push(i)}if(s.length){if(s.length>1)throw Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(t.length<n.length+r.length)return!1;let e=t.slice(0,n.length),a=0===r.length?[]:t.slice(-r.length),l=t.slice(n.length,0===r.length?1/0:-r.length);return n.every((t,n)=>e_(t,e[n],i))&&r.every((e,t)=>e_(e,a[t],i))&&(0===s.length||e_(s[0],l,i))}return e.length===t.length&&e.every((e,n)=>e_(e,t[n],i))}return Reflect.ownKeys(e).every(n=>{let r=e[n];return(n in t||eN(r)&&"optional"===r[eP]().matcherType)&&e_(r,t[n],i)})}return Object.is(t,e)},eD=e=>{var t,i,n;return eO(e)?eN(e)?null!=(t=null==(i=(n=e[eP]()).getSelectionKeys)?void 0:i.call(n))?t:[]:Array.isArray(e)?eU(e,eD):eU(Object.values(e),eD):[]},eU=(e,t)=>e.reduce((e,i)=>e.concat(t(i)),[]);function e$(e){return Object.assign(e,{optional:()=>{var t;return t=e,e$({[eP]:()=>({match:e=>{let i={},n=(e,t)=>{i[e]=t};return void 0===e?(eD(t).forEach(e=>n(e,void 0)),{matched:!0,selections:i}):{matched:e_(t,e,n),selections:i}},getSelectionKeys:()=>eD(t),matcherType:"optional"})})},and:t=>eF(e,t),or:t=>(function(...e){return e$({[eP]:()=>({match:t=>{let i={},n=(e,t)=>{i[e]=t};return eU(e,eD).forEach(e=>n(e,void 0)),{matched:e.some(e=>e_(e,t,n)),selections:i}},getSelectionKeys:()=>eU(e,eD),matcherType:"or"})})})(e,t),select:t=>void 0===t?eC(e):eC(t,e)})}function eF(...e){return e$({[eP]:()=>({match:t=>{let i={},n=(e,t)=>{i[e]=t};return{matched:e.every(e=>e_(e,t,n)),selections:i}},getSelectionKeys:()=>eU(e,eD),matcherType:"and"})})}function eV(e){return{[eP]:()=>({match:t=>({matched:!!e(t)})})}}function eC(...e){let t="string"==typeof e[0]?e[0]:void 0,i=2===e.length?e[1]:"string"==typeof e[0]?void 0:e[0];return e$({[eP]:()=>({match:e=>{let n={[t??ek]:e};return{matched:void 0===i||e_(i,e,(e,t)=>{n[e]=t}),selections:n}},getSelectionKeys:()=>[t??ek].concat(void 0===i?[]:eD(i))})})}function eL(e){return"number"==typeof e}function ej(e){return"string"==typeof e}function eM(e){return"bigint"==typeof e}e$(eV(function(e){return!0}));var eG=e=>Object.assign(e$(e),{startsWith:t=>eG(eF(e,eV(e=>ej(e)&&e.startsWith(t)))),endsWith:t=>eG(eF(e,eV(e=>ej(e)&&e.endsWith(t)))),minLength:t=>eG(eF(e,eV(e=>ej(e)&&e.length>=t))),length:t=>eG(eF(e,eV(e=>ej(e)&&e.length===t))),maxLength:t=>eG(eF(e,eV(e=>ej(e)&&e.length<=t))),includes:t=>eG(eF(e,eV(e=>ej(e)&&e.includes(t)))),regex:t=>eG(eF(e,eV(e=>ej(e)&&!!e.match(t))))}),eB=(eG(eV(ej)),e=>Object.assign(e$(e),{between:(t,i)=>eB(eF(e,eV(e=>eL(e)&&t<=e&&i>=e))),lt:t=>eB(eF(e,eV(e=>eL(e)&&e<t))),gt:t=>eB(eF(e,eV(e=>eL(e)&&e>t))),lte:t=>eB(eF(e,eV(e=>eL(e)&&e<=t))),gte:t=>eB(eF(e,eV(e=>eL(e)&&e>=t))),int:()=>eB(eF(e,eV(e=>eL(e)&&Number.isInteger(e)))),finite:()=>eB(eF(e,eV(e=>eL(e)&&Number.isFinite(e)))),positive:()=>eB(eF(e,eV(e=>eL(e)&&e>0))),negative:()=>eB(eF(e,eV(e=>eL(e)&&e<0)))})),eW=(eB(eV(eL)),e=>Object.assign(e$(e),{between:(t,i)=>eW(eF(e,eV(e=>eM(e)&&t<=e&&i>=e))),lt:t=>eW(eF(e,eV(e=>eM(e)&&e<t))),gt:t=>eW(eF(e,eV(e=>eM(e)&&e>t))),lte:t=>eW(eF(e,eV(e=>eM(e)&&e<=t))),gte:t=>eW(eF(e,eV(e=>eM(e)&&e>=t))),positive:()=>eW(eF(e,eV(e=>eM(e)&&e>0))),negative:()=>eW(eF(e,eV(e=>eM(e)&&e<0)))}));eW(eV(eM)),e$(eV(function(e){return"boolean"==typeof e})),e$(eV(function(e){return"symbol"==typeof e})),e$(eV(function(e){return null==e})),e$(eV(function(e){return null!=e}));var eH=class extends Error{constructor(e){let t;try{t=JSON.stringify(e)}catch{t=e}super(`Pattern matching error: no pattern matches value ${t}`),this.input=void 0,this.input=e}},eK={matched:!1,value:void 0};function eJ(e){return new eQ(e,eK)}var eQ=class e{constructor(e,t){this.input=void 0,this.state=void 0,this.input=e,this.state=t}with(...t){if(this.state.matched)return this;let i=t[t.length-1],n=[t[0]],r;3===t.length&&"function"==typeof t[1]?r=t[1]:t.length>2&&n.push(...t.slice(1,t.length-1));let s=!1,a={},l=(e,t)=>{s=!0,a[e]=t},o=n.some(e=>e_(e,this.input,l))&&(!r||r(this.input))?{matched:!0,value:i(s?ek in a?a[ek]:a:this.input,this.input)}:eK;return new e(this.input,o)}when(t,i){if(this.state.matched)return this;let n=!!t(this.input);return new e(this.input,n?{matched:!0,value:i(this.input,this.input)}:eK)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new eH(this.input)}run(){return this.exhaustive()}returnType(){return this}},eY=i(57975),ez={warn:et("prisma:warn")},eZ={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function eX(e,...t){eZ.warn()&&console.warn(`${ez.warn} ${e}`,...t)}var e0=(0,eY.promisify)(eS.default.exec),e1=eA("prisma:get-platform"),e2=["1.0.x","1.1.x","3.0.x"];async function e4(){let e=ex.default.platform(),t=process.arch;if("freebsd"===e){let e=await ts("freebsd-version");if(e&&e.trim().length>0){let i=/^(\d+)\.?/.exec(e);if(i)return{platform:"freebsd",targetDistro:`freebsd${i[1]}`,arch:t}}}if("linux"!==e)return{platform:e,arch:t};let i=await e3(),n=await ta(),r=eJ({arch:t,archFromUname:n,familyDistro:i.familyDistro}).with({familyDistro:"musl"},()=>(e1('Trying platform-specific paths for "alpine"'),["/lib","/usr/lib"])).with({familyDistro:"debian"},({archFromUname:e})=>(e1('Trying platform-specific paths for "debian" (and "ubuntu")'),[`/usr/lib/${e}-linux-gnu`,`/lib/${e}-linux-gnu`])).with({familyDistro:"rhel"},()=>(e1('Trying platform-specific paths for "rhel"'),["/lib64","/usr/lib64"])).otherwise(({familyDistro:e,arch:t,archFromUname:i})=>(e1(`Don't know any platform-specific paths for "${e}" on ${t} (${i})`),[])),{libssl:s}=await e5(r);return{platform:"linux",libssl:s,arch:t,archFromUname:n,...i}}async function e3(){try{var e;let t,i,n,r;return e=await eI.default.readFile("/etc/os-release",{encoding:"utf-8"}),i=(t=/^ID="?([^"\n]*)"?$/im.exec(e))&&t[1]&&t[1].toLowerCase()||"",n=/^ID_LIKE="?([^"\n]*)"?$/im.exec(e),r=eJ({id:i,idLike:n&&n[1]&&n[1].toLowerCase()||""}).with({id:"alpine"},({id:e})=>({targetDistro:"musl",familyDistro:e,originalDistro:e})).with({id:"raspbian"},({id:e})=>({targetDistro:"arm",familyDistro:"debian",originalDistro:e})).with({id:"nixos"},({id:e})=>({targetDistro:"nixos",originalDistro:e,familyDistro:"nixos"})).with({id:"debian"},{id:"ubuntu"},({id:e})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:e})).with({id:"rhel"},{id:"centos"},{id:"fedora"},({id:e})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:e})).when(({idLike:e})=>e.includes("debian")||e.includes("ubuntu"),({id:e})=>({targetDistro:"debian",familyDistro:"debian",originalDistro:e})).when(({idLike:e})=>"arch"===i||e.includes("arch"),({id:e})=>({targetDistro:"debian",familyDistro:"arch",originalDistro:e})).when(({idLike:e})=>e.includes("centos")||e.includes("fedora")||e.includes("rhel")||e.includes("suse"),({id:e})=>({targetDistro:"rhel",familyDistro:"rhel",originalDistro:e})).otherwise(({id:e})=>({targetDistro:void 0,familyDistro:void 0,originalDistro:e})),e1(`Found distro info:
${JSON.stringify(r,null,2)}`),r}catch{return{targetDistro:void 0,familyDistro:void 0,originalDistro:void 0}}}function e7(e){let t=/libssl\.so\.(\d)(\.\d)?/.exec(e);if(t)return e6(`${t[1]}${t[2]??".0"}.x`)}function e6(e){let t=(()=>{if(tl(e))return e;let t=e.split(".");return t[1]="0",t.join(".")})();if(e2.includes(t))return t}async function e5(e){let t=await e9(e);if(t){e1(`Found libssl.so file using platform-specific paths: ${t}`);let e=e7(t);if(e1(`The parsed libssl version is: ${e}`),e)return{libssl:e,strategy:"libssl-specific-path"}}e1('Falling back to "ldconfig" and other generic paths');let i=await ts('ldconfig -p | sed "s/.*=>s*//" | sed "s|.*/||" | grep libssl | sort | grep -v "libssl.so.0"');if(i||(i=await e9(["/lib64","/usr/lib64","/lib","/usr/lib"])),i){e1(`Found libssl.so file using "ldconfig" or other generic paths: ${i}`);let e=e7(i);if(e1(`The parsed libssl version is: ${e}`),e)return{libssl:e,strategy:"ldconfig"}}let n=await ts("openssl version -v");if(n){e1(`Found openssl binary with version: ${n}`);let e=function(e){let t=/^OpenSSL\s(\d+\.\d+)\.\d+/.exec(e);if(t)return e6(`${t[1]}.x`)}(n);if(e1(`The parsed openssl version is: ${e}`),e)return{libssl:e,strategy:"openssl-binary"}}return e1("Couldn't find any version of libssl or OpenSSL in the system"),{}}async function e9(e){for(let t of e){let e=await e8(t);if(e)return e}}async function e8(e){try{return(await eI.default.readdir(e)).find(e=>e.startsWith("libssl.so.")&&!e.startsWith("libssl.so.0"))}catch(e){if("ENOENT"===e.code)return;throw e}}async function te(){let{binaryTarget:e}=await tn();return e}async function tt(){let{memoized:e,...t}=await tn();return t}var ti={};async function tn(){if(void 0!==ti.binaryTarget)return Promise.resolve({...ti,memoized:!0});let e=await e4(),t=function(e){let{platform:t,arch:i,archFromUname:n,libssl:r,targetDistro:s,familyDistro:a,originalDistro:l}=e;"linux"!==t||["x64","arm64"].includes(i)||eX(`Prisma only officially supports Linux on amd64 (x86_64) and arm64 (aarch64) system architectures (detected "${i}" instead). If you are using your own custom Prisma engines, you can ignore this warning, as long as you've compiled the engines for your system architecture "${n}".`);let o="1.1.x";if("linux"===t&&void 0===r){let e=eJ({familyDistro:a}).with({familyDistro:"debian"},()=>"Please manually install OpenSSL via `apt-get update -y && apt-get install -y openssl` and try installing Prisma again. If you're running Prisma on Docker, add this command to your Dockerfile, or switch to an image that already has OpenSSL installed.").otherwise(()=>"Please manually install OpenSSL and try installing Prisma again.");eX(`Prisma failed to detect the libssl/openssl version to use, and may not work as expected. Defaulting to "openssl-${o}".
${e}`)}let u="debian";if("linux"===t&&void 0===s&&e1(`Distro is "${l}". Falling back to Prisma engines built for "${u}".`),"darwin"===t&&"arm64"===i)return"darwin-arm64";if("darwin"===t)return"darwin";if("win32"===t)return"windows";if("freebsd"===t)return s;if("openbsd"===t)return"openbsd";if("netbsd"===t)return"netbsd";if("linux"===t&&"nixos"===s)return"linux-nixos";if("linux"===t&&"arm64"===i)return`${"musl"===s?"linux-musl-arm64":"linux-arm64"}-openssl-${r||o}`;if("linux"===t&&"arm"===i)return`linux-arm-openssl-${r||o}`;if("linux"===t&&"musl"===s){let e="linux-musl";return!r||tl(r)?e:`${e}-openssl-${r}`}return"linux"===t&&s&&r?`${s}-openssl-${r}`:("linux"!==t&&eX(`Prisma detected unknown OS "${t}" and may not work as expected. Defaulting to "linux".`),r?`${u}-openssl-${r}`:s?`${s}-openssl-${o}`:`${u}-openssl-${o}`)}(e);return{...ti={...e,binaryTarget:t},memoized:!1}}async function tr(e){try{return await e()}catch{return}}function ts(e){return tr(async()=>{let t=await e0(e);return e1(`Command "${e}" successfully returned "${t.stdout}"`),t.stdout})}async function ta(){return"function"==typeof ex.default.machine?ex.default.machine():(await ts("uname -m"))?.trim()}function tl(e){return e.startsWith("1.")}var to={};c(to,{beep:()=>tM,clearScreen:()=>tV,clearTerminal:()=>tC,cursorBackward:()=>tb,cursorDown:()=>tv,cursorForward:()=>tw,cursorGetPosition:()=>tT,cursorHide:()=>tx,cursorLeft:()=>tE,cursorMove:()=>tg,cursorNextLine:()=>tS,cursorPrevLine:()=>tI,cursorRestorePosition:()=>tR,cursorSavePosition:()=>tA,cursorShow:()=>tP,cursorTo:()=>tm,cursorUp:()=>ty,enterAlternativeScreen:()=>tL,eraseDown:()=>t_,eraseEndLine:()=>tk,eraseLine:()=>tN,eraseLines:()=>tq,eraseScreen:()=>tU,eraseStartLine:()=>tO,eraseUp:()=>tD,exitAlternativeScreen:()=>tj,iTerm:()=>tW,image:()=>tB,link:()=>tG,scrollDown:()=>tF,scrollUp:()=>t$});var tu=p(i(1708),1),td=globalThis.window?.document!==void 0,tc=(globalThis.process?.versions?.node,globalThis.process?.versions?.bun,globalThis.Deno?.version?.deno,globalThis.process?.versions?.electron,globalThis.navigator?.userAgent?.includes("jsdom"),"u">typeof WorkerGlobalScope&&WorkerGlobalScope,"u">typeof DedicatedWorkerGlobalScope&&DedicatedWorkerGlobalScope,"u">typeof SharedWorkerGlobalScope&&SharedWorkerGlobalScope,"u">typeof ServiceWorkerGlobalScope&&ServiceWorkerGlobalScope,globalThis.navigator?.userAgentData?.platform);"macOS"===tc||globalThis.navigator?.platform==="MacIntel"||globalThis.navigator?.userAgent?.includes(" Mac ")===!0||globalThis.process?.platform,"Windows"===tc||globalThis.navigator?.platform==="Win32"||globalThis.process?.platform,"Linux"===tc||globalThis.navigator?.platform?.startsWith("Linux")===!0||globalThis.navigator?.userAgent?.includes(" Linux ")===!0||globalThis.process?.platform,"iOS"===tc||globalThis.navigator?.platform==="MacIntel"&&globalThis.navigator?.maxTouchPoints>1||/iPad|iPhone|iPod/.test(globalThis.navigator?.platform),"Android"===tc||globalThis.navigator?.platform==="Android"||globalThis.navigator?.userAgent?.includes(" Android ")===!0||globalThis.process?.platform;var tf=!td&&"Apple_Terminal"===tu.default.env.TERM_PROGRAM,tp=!td&&"win32"===tu.default.platform,th=td?()=>{throw Error("`process.cwd()` only works in Node.js, not the browser.")}:tu.default.cwd,tm=(e,t)=>{if("number"!=typeof e)throw TypeError("The `x` argument is required");return"number"!=typeof t?"\x1b["+(e+1)+"G":"\x1b["+(t+1)+";"+(e+1)+"H"},tg=(e,t)=>{if("number"!=typeof e)throw TypeError("The `x` argument is required");let i="";return e<0?i+="\x1b["+-e+"D":e>0&&(i+="\x1b["+e+"C"),t<0?i+="\x1b["+-t+"A":t>0&&(i+="\x1b["+t+"B"),i},ty=(e=1)=>"\x1b["+e+"A",tv=(e=1)=>"\x1b["+e+"B",tw=(e=1)=>"\x1b["+e+"C",tb=(e=1)=>"\x1b["+e+"D",tE="\x1b[G",tA=tf?"\x1b7":"\x1b[s",tR=tf?"\x1b8":"\x1b[u",tT="\x1b[6n",tS="\x1b[E",tI="\x1b[F",tx="\x1b[?25l",tP="\x1b[?25h",tq=e=>{let t="";for(let i=0;i<e;i++)t+=tN+(i<e-1?ty():"");return e&&(t+=tE),t},tk="\x1b[K",tO="\x1b[1K",tN="\x1b[2K",t_="\x1b[J",tD="\x1b[1J",tU="\x1b[2J",t$="\x1b[S",tF="\x1b[T",tV="\x1bc",tC=tp?`${tU}\x1b[0f`:`${tU}\x1b[3J\x1b[H`,tL="\x1b[?1049h",tj="\x1b[?1049l",tM="\x07",tG=(e,t)=>["\x1b]","8",";",";",t,"\x07",e,"\x1b]","8",";",";","\x07"].join(""),tB=(e,t={})=>{let i=`\x1b]1337;File=inline=1`;return t.width&&(i+=`;width=${t.width}`),t.height&&(i+=`;height=${t.height}`),!1===t.preserveAspectRatio&&(i+=";preserveAspectRatio=0"),i+":"+Buffer.from(e).toString("base64")+"\x07"},tW={setCwd:(e=th())=>`\x1b]50;CurrentDir=${e}\x07`,annotation(e,t={}){let i=`\x1b]1337;`,n=void 0!==t.x,r=void 0!==t.y;if((n||r)&&!(n&&r&&void 0!==t.length))throw Error("`x`, `y` and `length` must be defined when `x` or `y` is defined");return e=e.replaceAll("|",""),i+=t.isHidden?"AddHiddenAnnotation=":"AddAnnotation=",t.length>0?i+=(n?[e,t.length,t.x,t.y]:[t.length,e]).join("|"):i+=e,i+"\x07"}},tH=p(g(),1);function tK(e,t,{target:i="stdout",...n}={}){return tH.default[i]?to.link(e,t):!1===n.fallback?e:"function"==typeof n.fallback?n.fallback(e,t):`${e} (\u200B${t}\u200B)`}tK.isSupported=tH.default.stdout,tK.stderr=(e,t,i={})=>tK(e,t,{target:"stderr",...i}),tK.stderr.isSupported=tH.default.stderr;var tJ=y().version;function tQ(e){var t;let i;return("library"===(i=process.env.PRISMA_CLIENT_ENGINE_TYPE)?"library":"binary"===i?"binary":"client"===i?"client":void 0)||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":(t=e,t?.previewFeatures.includes("queryCompiler")?"client":"library"))}p(w());var tY=p(i(76760));p(w()),eA("prisma:engines"),tY.default.join(__dirname,"../query-engine-darwin"),tY.default.join(__dirname,"../query-engine-darwin-arm64"),tY.default.join(__dirname,"../query-engine-debian-openssl-1.0.x"),tY.default.join(__dirname,"../query-engine-debian-openssl-1.1.x"),tY.default.join(__dirname,"../query-engine-debian-openssl-3.0.x"),tY.default.join(__dirname,"../query-engine-linux-static-x64"),tY.default.join(__dirname,"../query-engine-linux-static-arm64"),tY.default.join(__dirname,"../query-engine-rhel-openssl-1.0.x"),tY.default.join(__dirname,"../query-engine-rhel-openssl-1.1.x"),tY.default.join(__dirname,"../query-engine-rhel-openssl-3.0.x"),tY.default.join(__dirname,"../libquery_engine-darwin.dylib.node"),tY.default.join(__dirname,"../libquery_engine-darwin-arm64.dylib.node"),tY.default.join(__dirname,"../libquery_engine-debian-openssl-1.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-debian-openssl-1.1.x.so.node"),tY.default.join(__dirname,"../libquery_engine-debian-openssl-3.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-1.1.x.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-arm64-openssl-3.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-musl.so.node"),tY.default.join(__dirname,"../libquery_engine-linux-musl-openssl-3.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-rhel-openssl-1.0.x.so.node"),tY.default.join(__dirname,"../libquery_engine-rhel-openssl-1.1.x.so.node"),tY.default.join(__dirname,"../libquery_engine-rhel-openssl-3.0.x.so.node"),tY.default.join(__dirname,"../query_engine-windows.dll.node");var tz=p(i(73024)),tZ=eA("chmodPlusX"),tX=(p(b(),1),"prisma+postgres:");function t0(e){return e?.toString().startsWith(`${tX}//`)??!1}var t1=p(E()),t2=class{constructor(e){this.config=e}toString(){var e;let t,{config:i}=this,n=JSON.parse(JSON.stringify({provider:i.provider.fromEnvVar?`env("${i.provider.fromEnvVar}")`:i.provider.value,binaryTargets:function(e){let t;if(e.length>0){let i=e.find(e=>null!==e.fromEnvVar);t=i?`env("${i.fromEnvVar}")`:e.map(e=>e.native?"native":e.value)}else t=void 0;return t}(i.binaryTargets)}));return`generator ${i.name} {
${(0,t1.default)((t=Object.keys(e=n).reduce((e,t)=>Math.max(e,t.length),0),Object.entries(e).map(([e,i])=>`${e.padEnd(t)} = ${JSON.parse(JSON.stringify(i,(e,t)=>Array.isArray(t)?`[${t.map(e=>JSON.stringify(e)).join(", ")}]`:JSON.stringify(t)))}`).join(`
`)),2)}
}`}},t4={};c(t4,{error:()=>t8,info:()=>t9,log:()=>t6,query:()=>ie,should:()=>t7,tags:()=>t3,warn:()=>t5});var t3={error:X("prisma:error"),warn:et("prisma:warn"),info:er("prisma:info"),query:ei("prisma:query")},t7={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function t6(...e){console.log(...e)}function t5(e,...t){t7.warn()&&console.warn(`${t3.warn} ${e}`,...t)}function t9(e,...t){console.info(`${t3.info} ${e}`,...t)}function t8(e,...t){console.error(`${t3.error} ${e}`,...t)}function ie(e,...t){console.log(`${t3.query} ${e}`,...t)}function it(e,t){if(!e)throw Error(`${t}. This should never happen. If you see this error, please, open an issue at https://pris.ly/prisma-prisma-bug-report`)}function ii(e,t){throw Error(t)}var ir=p(i(76760)),is=p(S()),ia=p(i(73024)),il=p(i(76760)),io=eA("prisma:tryLoadEnv");function iu({rootEnvPath:e,schemaEnvPath:t},i={conflictCheck:"none"}){let n=id(e);"none"!==i.conflictCheck&&function(e,t,i){let n=e?.dotenvResult.parsed,r=!ic(e?.path,t);if(n&&t&&r&&ia.default.existsSync(t)){let r=is.default.parse(ia.default.readFileSync(t)),s=[];for(let e in r)n[e]===r[e]&&s.push(e);if(s.length>0){let n=il.default.relative(process.cwd(),e.path),r=il.default.relative(process.cwd(),t);if("error"===i)throw Error(`There is a conflict between env var${s.length>1?"s":""} in ${J(n)} and ${J(r)}
Conflicting env vars:
${s.map(e=>`  ${W(e)}`).join(`
`)}

We suggest to move the contents of ${J(r)} to ${J(n)} to consolidate your env vars.
`);if("warn"===i){let e=`Conflict for env var${s.length>1?"s":""} ${s.map(e=>W(e)).join(", ")} in ${J(n)} and ${J(r)}
Env vars from ${J(r)} overwrite the ones from ${J(n)}
      `;console.warn(`${et("warn(prisma)")} ${e}`)}}}}(n,t,i.conflictCheck);let r=null;return ic(n?.path,t)||(r=id(t)),n||r||io("No Environment variables loaded"),r?.dotenvResult.error?console.error(X(W("Schema Env Error: "))+r.dotenvResult.error):{message:[n?.message,r?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...r?.dotenvResult?.parsed}}}function id(e){var t;return(t=e)&&ia.default.existsSync(t)?(io(`Environment variables loaded from ${e}`),{dotenvResult:function(e){let t=e.ignoreProcessEnv?{}:process.env,i=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(n,r){let s=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(r);if(!s)return n;let a=s[1],l,o;if("\\"===a)l=(o=s[0]).replace("\\$","$");else{let n=s[2];o=s[0].substring(a.length),l=i(l=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n]||"")}return n.replace(o,l)},n)??n;for(let n in e.parsed){let r=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=i(r)}for(let i in e.parsed)t[i]=e.parsed[i];return e}(is.default.config({path:e,debug:!!process.env.DOTENV_CONFIG_DEBUG||void 0})),message:H(`Environment variables loaded from ${il.default.relative(process.cwd(),e)}`),path:e}):(io(`Environment variables not found at ${e}`),null)}function ic(e,t){return e&&t&&il.default.resolve(e)===il.default.resolve(t)}function ip(e,t){let i={};for(let n of Object.keys(e))i[n]=t(e[n],n);return i}function ih(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var im=new Set,ig=(e,t,...i)=>{im.has(e)||(im.add(e),t5(t,...i))},iy=class e extends Error{constructor(t,i,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=i,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};ih(iy,"PrismaClientInitializationError");var iv=class extends Error{constructor(e,{code:t,clientVersion:i,meta:n,batchRequestIdx:r}){super(e),this.name="PrismaClientKnownRequestError",this.code=t,this.clientVersion=i,this.meta=n,Object.defineProperty(this,"batchRequestIdx",{value:r,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};ih(iv,"PrismaClientKnownRequestError");var iw=class extends Error{constructor(e,t){super(e),this.name="PrismaClientRustPanicError",this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};ih(iw,"PrismaClientRustPanicError");var ib=class extends Error{constructor(e,{clientVersion:t,batchRequestIdx:i}){super(e),this.name="PrismaClientUnknownRequestError",this.clientVersion=t,Object.defineProperty(this,"batchRequestIdx",{value:i,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};ih(ib,"PrismaClientUnknownRequestError");var iE=class extends Error{constructor(e,{clientVersion:t}){super(e),this.name="PrismaClientValidationError",this.clientVersion=t}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};ih(iE,"PrismaClientValidationError");var iA,iR,iT="0123456789abcdef",iS="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",iI="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",ix={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-9e15,maxE:9e15,crypto:!1},iP=!0,iq="[DecimalError] ",ik=iq+"Invalid argument: ",iO=iq+"Precision limit exceeded",iN=iq+"crypto unavailable",i_="[object Decimal]",iD=Math.floor,iU=Math.pow,i$=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,iF=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,iV=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,iC=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,iL=iS.length-1,ij=iI.length-1,iM={toStringTag:i_};function iG(e){var t,i,n,r=e.length-1,s="",a=e[0];if(r>0){for(s+=a,t=1;t<r;t++)(i=7-(n=e[t]+"").length)&&(s+=i0(i)),s+=n;(i=7-(n=(a=e[t])+"").length)&&(s+=i0(i))}else if(0===a)return"0";for(;a%10==0;)a/=10;return s+a}function iB(e,t,i){if(e!==~~e||e<t||e>i)throw Error(ik+e)}function iW(e,t,i,n){var r,s,a,l;for(s=e[0];s>=10;s/=10)--t;return--t<0?(t+=7,r=0):(r=Math.ceil((t+1)/7),t%=7),s=iU(10,7-t),l=e[r]%s|0,null==n?t<3?(0==t?l=l/100|0:1==t&&(l=l/10|0),a=i<4&&99999==l||i>3&&49999==l||5e4==l||0==l):a=(i<4&&l+1==s||i>3&&l+1==s/2)&&(e[r+1]/s/100|0)==iU(10,t-2)-1||(l==s/2||0==l)&&(e[r+1]/s/100|0)==0:t<4?(0==t?l=l/1e3|0:1==t?l=l/100|0:2==t&&(l=l/10|0),a=(n||i<4)&&9999==l||!n&&i>3&&4999==l):a=((n||i<4)&&l+1==s||!n&&i>3&&l+1==s/2)&&(e[r+1]/s/1e3|0)==iU(10,t-3)-1,a}function iH(e,t,i){for(var n,r,s=[0],a=0,l=e.length;a<l;){for(r=s.length;r--;)s[r]*=t;for(s[0]+=iT.indexOf(e.charAt(a++)),n=0;n<s.length;n++)s[n]>i-1&&(void 0===s[n+1]&&(s[n+1]=0),s[n+1]+=s[n]/i|0,s[n]%=i)}return s.reverse()}iM.absoluteValue=iM.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),iJ(e)},iM.ceil=function(){return iJ(new this.constructor(this),this.e+1,2)},iM.clampedTo=iM.clamp=function(e,t){var i=this.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(ik+t);return 0>this.cmp(e)?e:this.cmp(t)>0?t:new i(this)},iM.comparedTo=iM.cmp=function(e){var t,i,n,r,s=this.d,a=(e=new this.constructor(e)).d,l=this.s,o=e.s;if(!s||!a)return l&&o?l!==o?l:s===a?0:!s^l<0?1:-1:NaN;if(!s[0]||!a[0])return s[0]?l:a[0]?-o:0;if(l!==o)return l;if(this.e!==e.e)return this.e>e.e^l<0?1:-1;for(n=s.length,r=a.length,t=0,i=n<r?n:r;t<i;++t)if(s[t]!==a[t])return s[t]>a[t]^l<0?1:-1;return n===r?0:n>r^l<0?1:-1},iM.cosine=iM.cos=function(){var e,t,i=this,n=i.constructor;return i.d?i.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(i.e,i.sd())+7,n.rounding=1,i=function(e,t){var i,n,r;if(t.isZero())return t;(n=t.d.length)<32?r=(1/i8(4,i=Math.ceil(n/3))).toString():(i=16,r="2.3283064365386962890625e-10"),e.precision+=i,t=i9(e,1,t.times(r),new e(1));for(var s=i;s--;){var a=t.times(t);t=a.times(a).minus(a).times(8).plus(1)}return e.precision-=i,t}(n,ne(n,i)),n.precision=e,n.rounding=t,iJ(2==iR||3==iR?i.neg():i,e,t,!0)):new n(1):new n(NaN)},iM.cubeRoot=iM.cbrt=function(){var e,t,i,n,r,s,a,l,o,u,d=this.constructor;if(!this.isFinite()||this.isZero())return new d(this);for(iP=!1,(s=this.s*iU(this.s*this,1/3))&&Math.abs(s)!=1/0?n=new d(s.toString()):(i=iG(this.d),(s=((e=this.e)-i.length+1)%3)&&(i+=1==s||-2==s?"0":"00"),s=iU(i,1/3),e=iD((e+1)/3)-(e%3==(e<0?-1:2)),(n=new d(i=s==1/0?"5e"+e:(i=s.toExponential()).slice(0,i.indexOf("e")+1)+e)).s=this.s),a=(e=d.precision)+3;;)if(n=iK((u=(o=(l=n).times(l).times(l)).plus(this)).plus(this).times(l),u.plus(o),a+2,1),iG(l.d).slice(0,a)===(i=iG(n.d)).slice(0,a))if("9999"!=(i=i.slice(a-3,a+1))&&(r||"4999"!=i)){+i&&(+i.slice(1)||"5"!=i.charAt(0))||(iJ(n,e+1,1),t=!n.times(n).times(n).eq(this));break}else{if(!r&&(iJ(l,e+1,0),l.times(l).times(l).eq(this))){n=l;break}a+=4,r=1}return iP=!0,iJ(n,e,d.rounding,t)},iM.decimalPlaces=iM.dp=function(){var e,t=this.d,i=NaN;if(t){if(i=((e=t.length-1)-iD(this.e/7))*7,e=t[e])for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i},iM.dividedBy=iM.div=function(e){return iK(this,new this.constructor(e))},iM.dividedToIntegerBy=iM.divToInt=function(e){var t=this.constructor;return iJ(iK(this,new t(e),0,1,1),t.precision,t.rounding)},iM.equals=iM.eq=function(e){return 0===this.cmp(e)},iM.floor=function(){return iJ(new this.constructor(this),this.e+1,3)},iM.greaterThan=iM.gt=function(e){return this.cmp(e)>0},iM.greaterThanOrEqualTo=iM.gte=function(e){var t=this.cmp(e);return 1==t||0===t},iM.hyperbolicCosine=iM.cosh=function(){var e,t,i,n,r,s=this,a=s.constructor,l=new a(1);if(!s.isFinite())return new a(s.s?1/0:NaN);if(s.isZero())return l;i=a.precision,n=a.rounding,a.precision=i+Math.max(s.e,s.sd())+4,a.rounding=1,(r=s.d.length)<32?t=(1/i8(4,e=Math.ceil(r/3))).toString():(e=16,t="2.3283064365386962890625e-10"),s=i9(a,1,s.times(t),new a(1),!0);for(var o,u=e,d=new a(8);u--;)o=s.times(s),s=l.minus(o.times(d.minus(o.times(d))));return iJ(s,a.precision=i,a.rounding=n,!0)},iM.hyperbolicSine=iM.sinh=function(){var e,t,i,n,r=this,s=r.constructor;if(!r.isFinite()||r.isZero())return new s(r);if(t=s.precision,i=s.rounding,s.precision=t+Math.max(r.e,r.sd())+4,s.rounding=1,(n=r.d.length)<3)r=i9(s,2,r,r,!0);else{e=(e=1.4*Math.sqrt(n))>16?16:0|e,r=i9(s,2,r=r.times(1/i8(5,e)),r,!0);for(var a,l=new s(5),o=new s(16),u=new s(20);e--;)a=r.times(r),r=r.times(l.plus(a.times(o.times(a).plus(u))))}return s.precision=t,s.rounding=i,iJ(r,t,i,!0)},iM.hyperbolicTangent=iM.tanh=function(){var e,t,i=this.constructor;return this.isFinite()?this.isZero()?new i(this):(e=i.precision,t=i.rounding,i.precision=e+7,i.rounding=1,iK(this.sinh(),this.cosh(),i.precision=e,i.rounding=t)):new i(this.s)},iM.inverseCosine=iM.acos=function(){var e=this,t=e.constructor,i=e.abs().cmp(1),n=t.precision,r=t.rounding;return -1!==i?0===i?e.isNeg()?iZ(t,n,r):new t(0):new t(NaN):e.isZero()?iZ(t,n+4,r).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=r,e.times(2))},iM.inverseHyperbolicCosine=iM.acosh=function(){var e,t,i=this,n=i.constructor;return i.lte(1)?new n(i.eq(1)?0:NaN):i.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(i.e),i.sd())+4,n.rounding=1,iP=!1,i=i.times(i).minus(1).sqrt().plus(i),iP=!0,n.precision=e,n.rounding=t,i.ln()):new n(i)},iM.inverseHyperbolicSine=iM.asinh=function(){var e,t,i=this,n=i.constructor;return!i.isFinite()||i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,n.rounding=1,iP=!1,i=i.times(i).plus(1).sqrt().plus(i),iP=!0,n.precision=e,n.rounding=t,i.ln())},iM.inverseHyperbolicTangent=iM.atanh=function(){var e,t,i,n,r=this,s=r.constructor;return r.isFinite()?r.e>=0?new s(r.abs().eq(1)?r.s/0:r.isZero()?r:NaN):(e=s.precision,t=s.rounding,Math.max(n=r.sd(),e)<-(2*r.e)-1?iJ(new s(r),e,t,!0):(s.precision=i=n-r.e,r=iK(r.plus(1),new s(1).minus(r),i+e,1),s.precision=e+4,s.rounding=1,r=r.ln(),s.precision=e,s.rounding=t,r.times(.5))):new s(NaN)},iM.inverseSine=iM.asin=function(){var e,t,i,n,r=this,s=r.constructor;return r.isZero()?new s(r):(t=r.abs().cmp(1),i=s.precision,n=s.rounding,-1!==t?0===t?((e=iZ(s,i+4,n).times(.5)).s=r.s,e):new s(NaN):(s.precision=i+6,s.rounding=1,r=r.div(new s(1).minus(r.times(r)).sqrt().plus(1)).atan(),s.precision=i,s.rounding=n,r.times(2)))},iM.inverseTangent=iM.atan=function(){var e,t,i,n,r,s,a,l,o,u=this,d=u.constructor,c=d.precision,f=d.rounding;if(u.isFinite()){if(u.isZero())return new d(u);if(u.abs().eq(1)&&c+4<=ij)return(a=iZ(d,c+4,f).times(.25)).s=u.s,a}else{if(!u.s)return new d(NaN);if(c+4<=ij)return(a=iZ(d,c+4,f).times(.5)).s=u.s,a}for(d.precision=l=c+10,d.rounding=1,e=i=Math.min(28,l/7+2|0);e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(iP=!1,t=Math.ceil(l/7),n=1,o=u.times(u),a=new d(u),r=u;-1!==e;)if(r=r.times(o),s=a.minus(r.div(n+=2)),r=r.times(o),void 0!==(a=s.plus(r.div(n+=2))).d[t])for(e=t;a.d[e]===s.d[e]&&e--;);return i&&(a=a.times(2<<i-1)),iP=!0,iJ(a,d.precision=c,d.rounding=f,!0)},iM.isFinite=function(){return!!this.d},iM.isInteger=iM.isInt=function(){return!!this.d&&iD(this.e/7)>this.d.length-2},iM.isNaN=function(){return!this.s},iM.isNegative=iM.isNeg=function(){return this.s<0},iM.isPositive=iM.isPos=function(){return this.s>0},iM.isZero=function(){return!!this.d&&0===this.d[0]},iM.lessThan=iM.lt=function(e){return 0>this.cmp(e)},iM.lessThanOrEqualTo=iM.lte=function(e){return 1>this.cmp(e)},iM.logarithm=iM.log=function(e){var t,i,n,r,s,a,l,o,u=this.constructor,d=u.precision,c=u.rounding;if(null==e)e=new u(10),t=!0;else{if(i=(e=new u(e)).d,e.s<0||!i||!i[0]||e.eq(1))return new u(NaN);t=e.eq(10)}if(i=this.d,this.s<0||!i||!i[0]||this.eq(1))return new u(i&&!i[0]?-1/0:1!=this.s?NaN:i?0:1/0);if(t)if(i.length>1)s=!0;else{for(r=i[0];r%10==0;)r/=10;s=1!==r}if(iP=!1,iW((o=iK(a=i7(this,l=d+5),t?iz(u,l+10):i7(e,l),l,1)).d,r=d,c))do if(l+=10,o=iK(a=i7(this,l),t?iz(u,l+10):i7(e,l),l,1),!s){+iG(o.d).slice(r+1,r+15)+1==1e14&&(o=iJ(o,d+1,0));break}while(iW(o.d,r+=10,c));return iP=!0,iJ(o,d,c)},iM.minus=iM.sub=function(e){var t,i,n,r,s,a,l,o,u,d,c,f,p=this.constructor;if(e=new p(e),!this.d||!e.d)return this.s&&e.s?this.d?e.s=-e.s:e=new p(e.d||this.s!==e.s?this:NaN):e=new p(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.plus(e);if(u=this.d,f=e.d,l=p.precision,o=p.rounding,!u[0]||!f[0]){if(f[0])e.s=-e.s;else{if(!u[0])return new p(3===o?-0:0);e=new p(this)}return iP?iJ(e,l,o):e}if(i=iD(e.e/7),d=iD(this.e/7),u=u.slice(),s=d-i){for((c=s<0)?(t=u,s=-s,a=f.length):(t=f,i=d,a=u.length),s>(n=Math.max(Math.ceil(l/7),a)+2)&&(s=n,t.length=1),t.reverse(),n=s;n--;)t.push(0);t.reverse()}else{for((c=(n=u.length)<(a=f.length))&&(a=n),n=0;n<a;n++)if(u[n]!=f[n]){c=u[n]<f[n];break}s=0}for(c&&(t=u,u=f,f=t,e.s=-e.s),a=u.length,n=f.length-a;n>0;--n)u[a++]=0;for(n=f.length;n>s;){if(u[--n]<f[n]){for(r=n;r&&0===u[--r];)u[r]=1e7-1;--u[r],u[n]+=1e7}u[n]-=f[n]}for(;0===u[--a];)u.pop();for(;0===u[0];u.shift())--i;return u[0]?(e.d=u,e.e=iY(u,i),iP?iJ(e,l,o):e):new p(3===o?-0:0)},iM.modulo=iM.mod=function(e){var t,i=this.constructor;return e=new i(e),this.d&&e.s&&(!e.d||e.d[0])?e.d&&(!this.d||this.d[0])?(iP=!1,9==i.modulo?(t=iK(this,e.abs(),0,3,1),t.s*=e.s):t=iK(this,e,0,i.modulo,1),t=t.times(e),iP=!0,this.minus(t)):iJ(new i(this),i.precision,i.rounding):new i(NaN)},iM.naturalExponential=iM.exp=function(){return i3(this)},iM.naturalLogarithm=iM.ln=function(){return i7(this)},iM.negated=iM.neg=function(){var e=new this.constructor(this);return e.s=-e.s,iJ(e)},iM.plus=iM.add=function(e){var t,i,n,r,s,a,l,o,u,d,c=this.constructor;if(e=new c(e),!this.d||!e.d)return this.s&&e.s?this.d||(e=new c(e.d||this.s===e.s?this:NaN)):e=new c(NaN),e;if(this.s!=e.s)return e.s=-e.s,this.minus(e);if(u=this.d,d=e.d,l=c.precision,o=c.rounding,!u[0]||!d[0])return d[0]||(e=new c(this)),iP?iJ(e,l,o):e;if(s=iD(this.e/7),n=iD(e.e/7),u=u.slice(),r=s-n){for(r<0?(i=u,r=-r,a=d.length):(i=d,n=s,a=u.length),r>(a=(s=Math.ceil(l/7))>a?s+1:a+1)&&(r=a,i.length=1),i.reverse();r--;)i.push(0);i.reverse()}for((a=u.length)-(r=d.length)<0&&(r=a,i=d,d=u,u=i),t=0;r;)t=(u[--r]=u[r]+d[r]+t)/1e7|0,u[r]%=1e7;for(t&&(u.unshift(t),++n),a=u.length;0==u[--a];)u.pop();return e.d=u,e.e=iY(u,n),iP?iJ(e,l,o):e},iM.precision=iM.sd=function(e){var t;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(ik+e);return this.d?(t=iX(this.d),e&&this.e+1>t&&(t=this.e+1)):t=NaN,t},iM.round=function(){var e=this.constructor;return iJ(new e(this),this.e+1,e.rounding)},iM.sine=iM.sin=function(){var e,t,i=this,n=i.constructor;return i.isFinite()?i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+Math.max(i.e,i.sd())+7,n.rounding=1,i=function(e,t){var i,n=t.d.length;if(n<3)return t.isZero()?t:i9(e,2,t,t);i=(i=1.4*Math.sqrt(n))>16?16:0|i,t=i9(e,2,t=t.times(1/i8(5,i)),t);for(var r,s=new e(5),a=new e(16),l=new e(20);i--;)r=t.times(t),t=t.times(s.plus(r.times(a.times(r).minus(l))));return t}(n,ne(n,i)),n.precision=e,n.rounding=t,iJ(iR>2?i.neg():i,e,t,!0)):new n(NaN)},iM.squareRoot=iM.sqrt=function(){var e,t,i,n,r,s,a=this.d,l=this.e,o=this.s,u=this.constructor;if(1!==o||!a||!a[0])return new u(!o||o<0&&(!a||a[0])?NaN:a?this:1/0);for(iP=!1,0==(o=Math.sqrt(+this))||o==1/0?(((t=iG(a)).length+l)%2==0&&(t+="0"),o=Math.sqrt(t),l=iD((l+1)/2)-(l<0||l%2),n=new u(t=o==1/0?"5e"+l:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+l)):n=new u(o.toString()),i=(l=u.precision)+3;;)if(n=(s=n).plus(iK(this,s,i+2,1)).times(.5),iG(s.d).slice(0,i)===(t=iG(n.d)).slice(0,i))if("9999"!=(t=t.slice(i-3,i+1))&&(r||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(iJ(n,l+1,1),e=!n.times(n).eq(this));break}else{if(!r&&(iJ(s,l+1,0),s.times(s).eq(this))){n=s;break}i+=4,r=1}return iP=!0,iJ(n,l,u.rounding,e)},iM.tangent=iM.tan=function(){var e,t,i=this,n=i.constructor;return i.isFinite()?i.isZero()?new n(i):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,(i=i.sin()).s=1,i=iK(i,new n(1).minus(i.times(i)).sqrt(),e+10,0),n.precision=e,n.rounding=t,iJ(2==iR||4==iR?i.neg():i,e,t,!0)):new n(NaN)},iM.times=iM.mul=function(e){var t,i,n,r,s,a,l,o,u,d=this.constructor,c=this.d,f=(e=new d(e)).d;if(e.s*=this.s,!c||!c[0]||!f||!f[0])return new d(!e.s||c&&!c[0]&&!f||f&&!f[0]&&!c?NaN:!c||!f?e.s/0:0*e.s);for(i=iD(this.e/7)+iD(e.e/7),(o=c.length)<(u=f.length)&&(s=c,c=f,f=s,a=o,o=u,u=a),s=[],n=a=o+u;n--;)s.push(0);for(n=u;--n>=0;){for(t=0,r=o+n;r>n;)l=s[r]+f[n]*c[r-n-1]+t,s[r--]=l%1e7|0,t=l/1e7|0;s[r]=(s[r]+t)%1e7|0}for(;!s[--a];)s.pop();return t?++i:s.shift(),e.d=s,e.e=iY(s,i),iP?iJ(e,d.precision,d.rounding):e},iM.toBinary=function(e,t){return nt(this,2,e,t)},iM.toDecimalPlaces=iM.toDP=function(e,t){var i=this,n=i.constructor;return i=new n(i),void 0===e?i:(iB(e,0,1e9),void 0===t?t=n.rounding:iB(t,0,8),iJ(i,e+i.e+1,t))},iM.toExponential=function(e,t){var i,n=this,r=n.constructor;return void 0===e?i=iQ(n,!0):(iB(e,0,1e9),void 0===t?t=r.rounding:iB(t,0,8),i=iQ(n=iJ(new r(n),e+1,t),!0,e+1)),n.isNeg()&&!n.isZero()?"-"+i:i},iM.toFixed=function(e,t){var i,n,r=this.constructor;return void 0===e?i=iQ(this):(iB(e,0,1e9),void 0===t?t=r.rounding:iB(t,0,8),i=iQ(n=iJ(new r(this),e+this.e+1,t),!1,e+n.e+1)),this.isNeg()&&!this.isZero()?"-"+i:i},iM.toFraction=function(e){var t,i,n,r,s,a,l,o,u,d,c,f,p=this.d,h=this.constructor;if(!p)return new h(this);if(u=i=new h(1),n=o=new h(0),a=(s=(t=new h(n)).e=iX(p)-this.e-1)%7,t.d[0]=iU(10,a<0?7+a:a),null==e)e=s>0?t:u;else{if(!(l=new h(e)).isInt()||l.lt(u))throw Error(ik+l);e=l.gt(t)?s>0?t:u:l}for(iP=!1,l=new h(iG(p)),d=h.precision,h.precision=s=7*p.length*2;c=iK(l,t,0,1,1),1!=(r=i.plus(c.times(n))).cmp(e);)i=n,n=r,r=u,u=o.plus(c.times(r)),o=r,r=t,t=l.minus(c.times(r)),l=r;return r=iK(e.minus(i),n,0,1,1),o=o.plus(r.times(u)),i=i.plus(r.times(n)),o.s=u.s=this.s,f=1>iK(u,n,s,1).minus(this).abs().cmp(iK(o,i,s,1).minus(this).abs())?[u,n]:[o,i],h.precision=d,iP=!0,f},iM.toHexadecimal=iM.toHex=function(e,t){return nt(this,16,e,t)},iM.toNearest=function(e,t){var i=this,n=i.constructor;if(i=new n(i),null==e){if(!i.d)return i;e=new n(1),t=n.rounding}else{if(e=new n(e),void 0===t?t=n.rounding:iB(t,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(iP=!1,i=iK(i,e,0,t,1).times(e),iP=!0,iJ(i)):(e.s=i.s,i=e),i},iM.toNumber=function(){return+this},iM.toOctal=function(e,t){return nt(this,8,e,t)},iM.toPower=iM.pow=function(e){var t,i,n,r,s,a,l=this,o=l.constructor,u=+(e=new o(e));if(!l.d||!e.d||!l.d[0]||!e.d[0])return new o(iU(+l,u));if((l=new o(l)).eq(1))return l;if(n=o.precision,s=o.rounding,e.eq(1))return iJ(l,n,s);if((t=iD(e.e/7))>=e.d.length-1&&(i=u<0?-u:u)<=0x1fffffffffffff)return r=i1(o,l,i,n),e.s<0?new o(1).div(r):iJ(r,n,s);if((a=l.s)<0){if(t<e.d.length-1)return new o(NaN);if((1&e.d[t])==0&&(a=1),0==l.e&&1==l.d[0]&&1==l.d.length)return l.s=a,l}return(t=0!=(i=iU(+l,u))&&isFinite(i)?new o(i+"").e:iD(u*(Math.log("0."+iG(l.d))/Math.LN10+l.e+1)))>o.maxE+1||t<o.minE-1?new o(t>0?a/0:0):(iP=!1,o.rounding=l.s=1,i=Math.min(12,(t+"").length),(r=i3(e.times(i7(l,n+i)),n)).d&&iW((r=iJ(r,n+5,1)).d,n,s)&&(t=n+10,+iG((r=iJ(i3(e.times(i7(l,t+i)),t),t+5,1)).d).slice(n+1,n+15)+1==1e14&&(r=iJ(r,n+1,0))),r.s=a,iP=!0,o.rounding=s,iJ(r,n,s))},iM.toPrecision=function(e,t){var i,n=this,r=n.constructor;return void 0===e?i=iQ(n,n.e<=r.toExpNeg||n.e>=r.toExpPos):(iB(e,1,1e9),void 0===t?t=r.rounding:iB(t,0,8),i=iQ(n=iJ(new r(n),e,t),e<=n.e||n.e<=r.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+i:i},iM.toSignificantDigits=iM.toSD=function(e,t){var i=this.constructor;return void 0===e?(e=i.precision,t=i.rounding):(iB(e,1,1e9),void 0===t?t=i.rounding:iB(t,0,8)),iJ(new i(this),e,t)},iM.toString=function(){var e=this.constructor,t=iQ(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()&&!this.isZero()?"-"+t:t},iM.truncated=iM.trunc=function(){return iJ(new this.constructor(this),this.e+1,1)},iM.valueOf=iM.toJSON=function(){var e=this.constructor,t=iQ(this,this.e<=e.toExpNeg||this.e>=e.toExpPos);return this.isNeg()?"-"+t:t};var iK=function(){function e(e,t,i){var n,r=0,s=e.length;for(e=e.slice();s--;)n=e[s]*t+r,e[s]=n%i|0,r=n/i|0;return r&&e.unshift(r),e}function t(e,t,i,n){var r,s;if(i!=n)s=i>n?1:-1;else for(r=s=0;r<i;r++)if(e[r]!=t[r]){s=e[r]>t[r]?1:-1;break}return s}function i(e,t,i,n){for(var r=0;i--;)e[i]-=r,r=+(e[i]<t[i]),e[i]=r*n+e[i]-t[i];for(;!e[0]&&e.length>1;)e.shift()}return function(n,r,s,a,l,o){var u,d,c,f,p,h,m,g,y,v,w,b,E,A,R,T,S,I,x,P,q=n.constructor,k=n.s==r.s?1:-1,O=n.d,N=r.d;if(!O||!O[0]||!N||!N[0])return new q(!n.s||!r.s||(O?N&&O[0]==N[0]:!N)?NaN:O&&0==O[0]||!N?0*k:k/0);for(o?(p=1,d=n.e-r.e):(o=1e7,p=7,d=iD(n.e/p)-iD(r.e/p)),x=N.length,S=O.length,v=(y=new q(k)).d=[],c=0;N[c]==(O[c]||0);c++);if(N[c]>(O[c]||0)&&d--,null==s?(A=s=q.precision,a=q.rounding):A=l?s+(n.e-r.e)+1:s,A<0)v.push(1),h=!0;else{if(A=A/p+2|0,c=0,1==x){for(f=0,N=N[0],A++;(c<S||f)&&A--;c++)R=f*o+(O[c]||0),v[c]=R/N|0,f=R%N|0;h=f||c<S}else{for((f=o/(N[0]+1)|0)>1&&(N=e(N,f,o),O=e(O,f,o),x=N.length,S=O.length),T=x,b=(w=O.slice(0,x)).length;b<x;)w[b++]=0;(P=N.slice()).unshift(0),I=N[0],N[1]>=o/2&&++I;do f=0,(u=t(N,w,x,b))<0?(E=w[0],x!=b&&(E=E*o+(w[1]||0)),(f=E/I|0)>1?(f>=o&&(f=o-1),g=(m=e(N,f,o)).length,b=w.length,1==(u=t(m,w,g,b))&&(f--,i(m,x<g?P:N,g,o))):(0==f&&(u=f=1),m=N.slice()),(g=m.length)<b&&m.unshift(0),i(w,m,b,o),-1==u&&(b=w.length,(u=t(N,w,x,b))<1&&(f++,i(w,x<b?P:N,b,o))),b=w.length):0===u&&(f++,w=[0]),v[c++]=f,u&&w[0]?w[b++]=O[T]||0:(w=[O[T]],b=1);while((T++<S||void 0!==w[0])&&A--);h=void 0!==w[0]}v[0]||v.shift()}if(1==p)y.e=d,iA=h;else{for(c=1,f=v[0];f>=10;f/=10)c++;y.e=c+d*p-1,iJ(y,l?s+y.e+1:s,a,h)}return y}}();function iJ(e,t,i,n){var r,s,a,l,o,u,d,c,f,p=e.constructor;e:if(null!=t){if(!(c=e.d))return e;for(r=1,l=c[0];l>=10;l/=10)r++;if((s=t-r)<0)s+=7,a=t,o=(d=c[f=0])/iU(10,r-a-1)%10|0;else if((f=Math.ceil((s+1)/7))>=(l=c.length))if(n){for(;l++<=f;)c.push(0);d=o=0,r=1,s%=7,a=s-7+1}else break e;else{for(d=l=c[f],r=1;l>=10;l/=10)r++;s%=7,o=(a=s-7+r)<0?0:d/iU(10,r-a-1)%10|0}if(n=n||t<0||void 0!==c[f+1]||(a<0?d:d%iU(10,r-a-1)),u=i<4?(o||n)&&(0==i||i==(e.s<0?3:2)):o>5||5==o&&(4==i||n||6==i&&(s>0?a>0?d/iU(10,r-a):0:c[f-1])%10&1||i==(e.s<0?8:7)),t<1||!c[0])return c.length=0,u?(t-=e.e+1,c[0]=iU(10,(7-t%7)%7),e.e=-t||0):c[0]=e.e=0,e;if(0==s?(c.length=f,l=1,f--):(c.length=f+1,l=iU(10,7-s),c[f]=a>0?(d/iU(10,r-a)%iU(10,a)|0)*l:0),u)for(;;)if(0==f){for(s=1,a=c[0];a>=10;a/=10)s++;for(a=c[0]+=l,l=1;a>=10;a/=10)l++;s!=l&&(e.e++,1e7==c[0]&&(c[0]=1));break}else{if(c[f]+=l,1e7!=c[f])break;c[f--]=0,l=1}for(s=c.length;0===c[--s];)c.pop()}return iP&&(e.e>p.maxE?(e.d=null,e.e=NaN):e.e<p.minE&&(e.e=0,e.d=[0])),e}function iQ(e,t,i){if(!e.isFinite())return i6(e);var n,r=e.e,s=iG(e.d),a=s.length;return t?(i&&(n=i-a)>0?s=s.charAt(0)+"."+s.slice(1)+i0(n):a>1&&(s=s.charAt(0)+"."+s.slice(1)),s=s+(e.e<0?"e":"e+")+e.e):r<0?(s="0."+i0(-r-1)+s,i&&(n=i-a)>0&&(s+=i0(n))):r>=a?(s+=i0(r+1-a),i&&(n=i-r-1)>0&&(s=s+"."+i0(n))):((n=r+1)<a&&(s=s.slice(0,n)+"."+s.slice(n)),i&&(n=i-a)>0&&(r+1===a&&(s+="."),s+=i0(n))),s}function iY(e,t){var i=e[0];for(t*=7;i>=10;i/=10)t++;return t}function iz(e,t,i){if(t>iL)throw iP=!0,i&&(e.precision=i),Error(iO);return iJ(new e(iS),t,1,!0)}function iZ(e,t,i){if(t>ij)throw Error(iO);return iJ(new e(iI),t,i,!0)}function iX(e){var t=e.length-1,i=7*t+1;if(t=e[t]){for(;t%10==0;t/=10)i--;for(t=e[0];t>=10;t/=10)i++}return i}function i0(e){for(var t="";e--;)t+="0";return t}function i1(e,t,i,n){var r,s=new e(1),a=Math.ceil(n/7+4);for(iP=!1;;){if(i%2&&ni((s=s.times(t)).d,a)&&(r=!0),0===(i=iD(i/2))){i=s.d.length-1,r&&0===s.d[i]&&++s.d[i];break}ni((t=t.times(t)).d,a)}return iP=!0,s}function i2(e){return 1&e.d[e.d.length-1]}function i4(e,t,i){for(var n,r,s=new e(t[0]),a=0;++a<t.length;){if(!(r=new e(t[a])).s){s=r;break}((n=s.cmp(r))===i||0===n&&s.s===i)&&(s=r)}return s}function i3(e,t){var i,n,r,s,a,l,o,u=0,d=0,c=0,f=e.constructor,p=f.rounding,h=f.precision;if(!e.d||!e.d[0]||e.e>17)return new f(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(null==t?(iP=!1,o=h):o=t,l=new f(.03125);e.e>-2;)e=e.times(l),c+=5;for(o+=n=Math.log(iU(2,c))/Math.LN10*2+5|0,i=s=a=new f(1),f.precision=o;;){if(s=iJ(s.times(e),o,1),i=i.times(++d),iG((l=a.plus(iK(s,i,o,1))).d).slice(0,o)===iG(a.d).slice(0,o)){for(r=c;r--;)a=iJ(a.times(a),o,1);if(null!=t)return f.precision=h,a;if(!(u<3&&iW(a.d,o-n,p,u)))return iJ(a,f.precision=h,p,iP=!0);f.precision=o+=10,i=s=l=new f(1),d=0,u++}a=l}}function i7(e,t){var i,n,r,s,a,l,o,u,d,c,f,p=1,h=e,m=h.d,g=h.constructor,y=g.rounding,v=g.precision;if(h.s<0||!m||!m[0]||!h.e&&1==m[0]&&1==m.length)return new g(m&&!m[0]?-1/0:1!=h.s?NaN:m?0:h);if(null==t?(iP=!1,d=v):d=t,g.precision=d+=10,n=(i=iG(m)).charAt(0),!(15e14>Math.abs(s=h.e)))return u=iz(g,d+2,v).times(s+""),h=i7(new g(n+"."+i.slice(1)),d-10).plus(u),g.precision=v,null==t?iJ(h,v,y,iP=!0):h;for(;n<7&&1!=n||1==n&&i.charAt(1)>3;)n=(i=iG((h=h.times(e)).d)).charAt(0),p++;for(s=h.e,n>1?(h=new g("0."+i),s++):h=new g(n+"."+i.slice(1)),c=h,o=a=h=iK(h.minus(1),h.plus(1),d,1),f=iJ(h.times(h),d,1),r=3;;){if(a=iJ(a.times(f),d,1),iG((u=o.plus(iK(a,new g(r),d,1))).d).slice(0,d)===iG(o.d).slice(0,d))if(o=o.times(2),0!==s&&(o=o.plus(iz(g,d+2,v).times(s+""))),o=iK(o,new g(p),d,1),null!=t)return g.precision=v,o;else{if(!iW(o.d,d-10,y,l))return iJ(o,g.precision=v,y,iP=!0);g.precision=d+=10,u=a=h=iK(c.minus(1),c.plus(1),d,1),f=iJ(h.times(h),d,1),r=l=1}o=u,r+=2}}function i6(e){return String(e.s*e.s/0)}function i5(e,t){var i,n,r;for((i=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(i<0&&(i=n),i+=+t.slice(n+1),t=t.substring(0,n)):i<0&&(i=t.length),n=0;48===t.charCodeAt(n);n++);for(r=t.length;48===t.charCodeAt(r-1);--r);if(t=t.slice(n,r)){if(r-=n,e.e=i=i-n-1,e.d=[],n=(i+1)%7,i<0&&(n+=7),n<r){for(n&&e.d.push(+t.slice(0,n)),r-=7;n<r;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=r;for(;n--;)t+="0";e.d.push(+t),iP&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function i9(e,t,i,n,r){var s,a,l,o,u=e.precision,d=Math.ceil(u/7);for(iP=!1,o=i.times(i),l=new e(n);;){if(a=iK(l.times(o),new e(t++*t++),u,1),l=r?n.plus(a):n.minus(a),n=iK(a.times(o),new e(t++*t++),u,1),void 0!==(a=l.plus(n)).d[d]){for(s=d;a.d[s]===l.d[s]&&s--;);if(-1==s)break}s=l,l=n,n=a,a=s}return iP=!0,a.d.length=d+1,a}function i8(e,t){for(var i=e;--t;)i*=e;return i}function ne(e,t){var i,n=t.s<0,r=iZ(e,e.precision,1),s=r.times(.5);if((t=t.abs()).lte(s))return iR=n?4:1,t;if((i=t.divToInt(r)).isZero())iR=n?3:2;else{if((t=t.minus(i.times(r))).lte(s))return iR=i2(i)?n?2:3:n?4:1,t;iR=i2(i)?n?1:4:n?3:2}return t.minus(r).abs()}function nt(e,t,i,n){var r,s,a,l,o,u,d,c,f,p=e.constructor,h=void 0!==i;if(h?(iB(i,1,1e9),void 0===n?n=p.rounding:iB(n,0,8)):(i=p.precision,n=p.rounding),e.isFinite()){for(a=(d=iQ(e)).indexOf("."),h?(r=2,16==t?i=4*i-3:8==t&&(i=3*i-2)):r=t,a>=0&&(d=d.replace(".",""),(f=new p(1)).e=d.length-a,f.d=iH(iQ(f),10,r),f.e=f.d.length),s=o=(c=iH(d,10,r)).length;0==c[--o];)c.pop();if(c[0]){if(a<0?s--:((e=new p(e)).d=c,e.e=s,c=(e=iK(e,f,i,n,0,r)).d,s=e.e,u=iA),a=c[i],l=r/2,u=u||void 0!==c[i+1],u=n<4?(void 0!==a||u)&&(0===n||n===(e.s<0?3:2)):a>l||a===l&&(4===n||u||6===n&&1&c[i-1]||n===(e.s<0?8:7)),c.length=i,u)for(;++c[--i]>r-1;)c[i]=0,i||(++s,c.unshift(1));for(o=c.length;!c[o-1];--o);for(a=0,d="";a<o;a++)d+=iT.charAt(c[a]);if(h){if(o>1)if(16==t||8==t){for(a=16==t?4:3,--o;o%a;o++)d+="0";for(o=(c=iH(d,r,t)).length;!c[o-1];--o);for(a=1,d="1.";a<o;a++)d+=iT.charAt(c[a])}else d=d.charAt(0)+"."+d.slice(1);d=d+(s<0?"p":"p+")+s}else if(s<0){for(;++s;)d="0"+d;d="0."+d}else if(++s>o)for(s-=o;s--;)d+="0";else s<o&&(d=d.slice(0,s)+"."+d.slice(s))}else d=h?"0p+0":"0";d=(16==t?"0x":2==t?"0b":8==t?"0o":"")+d}else d=i6(e);return e.s<0?"-"+d:d}function ni(e,t){if(e.length>t)return e.length=t,!0}function nn(e){return new this(e).abs()}function nr(e){return new this(e).acos()}function ns(e){return new this(e).acosh()}function na(e,t){return new this(e).plus(t)}function nl(e){return new this(e).asin()}function no(e){return new this(e).asinh()}function nu(e){return new this(e).atan()}function nd(e){return new this(e).atanh()}function nc(e,t){e=new this(e),t=new this(t);var i,n=this.precision,r=this.rounding,s=n+4;return e.s&&t.s?e.d||t.d?!t.d||e.isZero()?(i=t.s<0?iZ(this,n,r):new this(0)).s=e.s:!e.d||t.isZero()?(i=iZ(this,s,1).times(.5)).s=e.s:t.s<0?(this.precision=s,this.rounding=1,i=this.atan(iK(e,t,s,1)),t=iZ(this,s,1),this.precision=n,this.rounding=r,i=e.s<0?i.minus(t):i.plus(t)):i=this.atan(iK(e,t,s,1)):(i=iZ(this,s,1).times(t.s>0?.25:.75)).s=e.s:i=new this(NaN),i}function nf(e){return new this(e).cbrt()}function np(e){return iJ(e=new this(e),e.e+1,2)}function nh(e,t,i){return new this(e).clamp(t,i)}function nm(e){if(!e||"object"!=typeof e)throw Error(iq+"Object expected");var t,i,n,r=!0===e.defaults,s=["precision",1,1e9,"rounding",0,8,"toExpNeg",-9e15,0,"toExpPos",0,9e15,"maxE",0,9e15,"minE",-9e15,0,"modulo",0,9];for(t=0;t<s.length;t+=3)if(i=s[t],r&&(this[i]=ix[i]),void 0!==(n=e[i]))if(iD(n)===n&&n>=s[t+1]&&n<=s[t+2])this[i]=n;else throw Error(ik+i+": "+n);if(i="crypto",r&&(this[i]=ix[i]),void 0!==(n=e[i]))if(!0===n||!1===n||0===n||1===n)if(n)if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(iN);else this[i]=!1;else throw Error(ik+i+": "+n);return this}function ng(e){return new this(e).cos()}function ny(e){return new this(e).cosh()}function nv(e,t){return new this(e).div(t)}function nw(e){return new this(e).exp()}function nb(e){return iJ(e=new this(e),e.e+1,3)}function nE(){var e,t,i=new this(0);for(iP=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)i.d&&(i=i.plus(t.times(t)));else{if(t.s)return iP=!0,new this(1/0);i=t}return iP=!0,i.sqrt()}function nA(e){return e instanceof nG||e&&e.toStringTag===i_||!1}function nR(e){return new this(e).ln()}function nT(e,t){return new this(e).log(t)}function nS(e){return new this(e).log(2)}function nI(e){return new this(e).log(10)}function nx(){return i4(this,arguments,-1)}function nP(){return i4(this,arguments,1)}function nq(e,t){return new this(e).mod(t)}function nk(e,t){return new this(e).mul(t)}function nO(e,t){return new this(e).pow(t)}function nN(e){var t,i,n,r,s=0,a=new this(1),l=[];if(void 0===e?e=this.precision:iB(e,1,1e9),n=Math.ceil(e/7),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));s<n;)(r=t[s])>=429e7?t[s]=crypto.getRandomValues(new Uint32Array(1))[0]:l[s++]=r%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);s<n;)(r=t[s]+(t[s+1]<<8)+(t[s+2]<<16)+((127&t[s+3])<<24))>=214e7?crypto.randomBytes(4).copy(t,s):(l.push(r%1e7),s+=4);s=n/4}else throw Error(iN);else for(;s<n;)l[s++]=1e7*Math.random()|0;for(n=l[--s],e%=7,n&&e&&(r=iU(10,7-e),l[s]=(n/r|0)*r);0===l[s];s--)l.pop();if(s<0)i=0,l=[0];else{for(i=-1;0===l[0];i-=7)l.shift();for(n=1,r=l[0];r>=10;r/=10)n++;n<7&&(i-=7-n)}return a.e=i,a.d=l,a}function n_(e){return iJ(e=new this(e),e.e+1,this.rounding)}function nD(e){return(e=new this(e)).d?e.d[0]?e.s:0*e.s:e.s||NaN}function nU(e){return new this(e).sin()}function n$(e){return new this(e).sinh()}function nF(e){return new this(e).sqrt()}function nV(e,t){return new this(e).sub(t)}function nC(){var e=0,t=arguments,i=new this(t[0]);for(iP=!1;i.s&&++e<t.length;)i=i.plus(t[e]);return iP=!0,iJ(i,this.precision,this.rounding)}function nL(e){return new this(e).tan()}function nj(e){return new this(e).tanh()}function nM(e){return iJ(e=new this(e),e.e+1,1)}iM[Symbol.for("nodejs.util.inspect.custom")]=iM.toString,iM[Symbol.toStringTag]="Decimal";var nG=iM.constructor=function e(t){var i,n,r;function s(e){var t,i,n;if(!(this instanceof s))return new s(e);if(this.constructor=s,nA(e)){this.s=e.s,iP?!e.d||e.e>s.maxE?(this.e=NaN,this.d=null):e.e<s.minE?(this.e=0,this.d=[0]):(this.e=e.e,this.d=e.d.slice()):(this.e=e.e,this.d=e.d?e.d.slice():e.d);return}if("number"==(n=typeof e)){if(0===e){this.s=1/e<0?-1:1,this.e=0,this.d=[0];return}if(e<0?(e=-e,this.s=-1):this.s=1,e===~~e&&e<1e7){for(t=0,i=e;i>=10;i/=10)t++;iP?t>s.maxE?(this.e=NaN,this.d=null):t<s.minE?(this.e=0,this.d=[0]):(this.e=t,this.d=[e]):(this.e=t,this.d=[e]);return}if(0*e!=0){e||(this.s=NaN),this.e=NaN,this.d=null;return}return i5(this,e.toString())}if("string"===n)return 45===(i=e.charCodeAt(0))?(e=e.slice(1),this.s=-1):(43===i&&(e=e.slice(1)),this.s=1),iC.test(e)?i5(this,e):function(e,t){var i,n,r,s,a,l,o,u,d;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),iC.test(t))return i5(e,t)}else if("Infinity"===t||"NaN"===t)return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(iF.test(t))i=16,t=t.toLowerCase();else if(i$.test(t))i=2;else if(iV.test(t))i=8;else throw Error(ik+t);for((s=t.search(/p/i))>0?(o=+t.slice(s+1),t=t.substring(2,s)):t=t.slice(2),a=(s=t.indexOf("."))>=0,n=e.constructor,a&&(s=(l=(t=t.replace(".","")).length)-s,r=i1(n,new n(i),s,2*s)),s=d=(u=iH(t,i,1e7)).length-1;0===u[s];--s)u.pop();return s<0?new n(0*e.s):(e.e=iY(u,d),e.d=u,iP=!1,a&&(e=iK(e,r,4*l)),o&&(e=e.times(54>Math.abs(o)?iU(2,o):nG.pow(2,o))),iP=!0,e)}(this,e);if("bigint"===n)return e<0?(e=-e,this.s=-1):this.s=1,i5(this,e.toString());throw Error(ik+e)}if(s.prototype=iM,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=nm,s.clone=e,s.isDecimal=nA,s.abs=nn,s.acos=nr,s.acosh=ns,s.add=na,s.asin=nl,s.asinh=no,s.atan=nu,s.atanh=nd,s.atan2=nc,s.cbrt=nf,s.ceil=np,s.clamp=nh,s.cos=ng,s.cosh=ny,s.div=nv,s.exp=nw,s.floor=nb,s.hypot=nE,s.ln=nR,s.log=nT,s.log10=nI,s.log2=nS,s.max=nx,s.min=nP,s.mod=nq,s.mul=nk,s.pow=nO,s.random=nN,s.round=n_,s.sign=nD,s.sin=nU,s.sinh=n$,s.sqrt=nF,s.sub=nV,s.sum=nC,s.tan=nL,s.tanh=nj,s.trunc=nM,void 0===t&&(t={}),t&&!0!==t.defaults)for(r=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],i=0;i<r.length;)t.hasOwnProperty(n=r[i++])||(t[n]=this[n]);return s.config(t),s}(ix);iS=new nG(iS),iI=new nG(iI);var nB=nG;function nW(e){var t;return null===e?e:Array.isArray(e)?e.map(nW):"object"==typeof e?null!==(t=e)&&"object"==typeof t&&"string"==typeof t.$type?function({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:e,byteOffset:i,byteLength:n}=Buffer.from(t,"base64");return new Uint8Array(e,i,n)}case"DateTime":return new Date(t);case"Decimal":return new nB(t);case"Json":return JSON.parse(t);default:ii(t,"Unknown tagged value")}}(e):"bigint"==typeof e||e instanceof Date||e instanceof Uint8Array||e instanceof nB?e:ip(e,nW):e}var nH=class{get(e){return this._map.get(e)?.value}set(e,t){this._map.set(e,{value:t})}getOrCreate(e,t){let i=this._map.get(e);if(i)return i.value;let n=t();return this.set(e,n),n}constructor(){this._map=new Map}};function nK(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function nJ(e){let t;return{get:()=>(t||(t={value:e()}),t.value)}}function nQ(e){return{models:nY(e.models),enums:nY(e.enums),types:nY(e.types)}}function nY(e){let t={};for(let{name:i,...n}of e)t[i]=n;return t}function nz(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function nZ(e){return"Invalid Date"!==e.toString()}function nX(e){return!!nG.isDecimal(e)||null!==e&&"object"==typeof e&&"number"==typeof e.s&&"number"==typeof e.e&&"function"==typeof e.toFixed&&Array.isArray(e.d)}var n0={};function n1(e){return{name:e.name,values:e.values.map(e=>e.name)}}c(n0,{ModelAction:()=>n2,datamodelEnumToSchemaEnum:()=>n1});var n2=(e=>(e.findUnique="findUnique",e.findUniqueOrThrow="findUniqueOrThrow",e.findFirst="findFirst",e.findFirstOrThrow="findFirstOrThrow",e.findMany="findMany",e.create="create",e.createMany="createMany",e.createManyAndReturn="createManyAndReturn",e.update="update",e.updateMany="updateMany",e.updateManyAndReturn="updateManyAndReturn",e.upsert="upsert",e.delete="delete",e.deleteMany="deleteMany",e.groupBy="groupBy",e.count="count",e.aggregate="aggregate",e.findRaw="findRaw",e.aggregateRaw="aggregateRaw",e))(n2||{});p(E()),p(i(73024));var n4={keyword:er,entity:er,value:e=>W(ei(e)),punctuation:ei,directive:er,function:er,variable:e=>W(ei(e)),string:e=>W(ee(e)),boolean:et,number:er,comment:ea},n3=e=>e,n7={},n6=0,n5={manual:n7.Prism&&n7.Prism.manual,disableWorkerMessageHandler:n7.Prism&&n7.Prism.disableWorkerMessageHandler,util:{encode:function(e){return e instanceof n9?new n9(e.type,n5.util.encode(e.content),e.alias):Array.isArray(e)?e.map(n5.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n6}),e.__id},clone:function e(t,i){let n,r,s=n5.util.type(t);switch(i=i||{},s){case"Object":if(i[r=n5.util.objId(t)])return i[r];for(let s in n={},i[r]=n,t)t.hasOwnProperty(s)&&(n[s]=e(t[s],i));return n;case"Array":return i[r=n5.util.objId(t)]?i[r]:(n=[],i[r]=n,t.forEach(function(t,r){n[r]=e(t,i)}),n);default:return t}}},languages:{extend:function(e,t){let i=n5.util.clone(n5.languages[e]);for(let e in t)i[e]=t[e];return i},insertBefore:function(e,t,i,n){let r=(n=n||n5.languages)[e],s={};for(let e in r)if(r.hasOwnProperty(e)){if(e==t)for(let e in i)i.hasOwnProperty(e)&&(s[e]=i[e]);i.hasOwnProperty(e)||(s[e]=r[e])}let a=n[e];return n[e]=s,n5.languages.DFS(n5.languages,function(t,i){i===a&&t!=e&&(this[t]=s)}),s},DFS:function e(t,i,n,r){r=r||{};let s=n5.util.objId;for(let a in t)if(t.hasOwnProperty(a)){i.call(t,a,t[a],n||a);let l=t[a],o=n5.util.type(l);"Object"!==o||r[s(l)]?"Array"!==o||r[s(l)]||(r[s(l)]=!0,e(l,i,a,r)):(r[s(l)]=!0,e(l,i,null,r))}}},plugins:{},highlight:function(e,t,i){let n={code:e,grammar:t,language:i};return n5.hooks.run("before-tokenize",n),n.tokens=n5.tokenize(n.code,n.grammar),n5.hooks.run("after-tokenize",n),n9.stringify(n5.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,i,n,r,s,a){for(let m in i){if(!i.hasOwnProperty(m)||!i[m])continue;if(m==a)return;let g=i[m];g="Array"===n5.util.type(g)?g:[g];for(let a=0;a<g.length;++a){let y=g[a],v=y.inside,w=!!y.lookbehind,b=!!y.greedy,E=0,A=y.alias;if(b&&!y.pattern.global){let e=y.pattern.toString().match(/[imuy]*$/)[0];y.pattern=RegExp(y.pattern.source,e+"g")}y=y.pattern||y;for(let a=n,g=r;a<t.length;g+=t[a].length,++a){let n=t[a];if(t.length>e.length)return;if(n instanceof n9)continue;if(b&&a!=t.length-1){y.lastIndex=g;var l=y.exec(e);if(!l)break;var o=l.index+(w?l[1].length:0),u=l.index+l[0].length,d=a,c=g;for(let e=t.length;d<e&&(c<u||!t[d].type&&!t[d-1].greedy);++d)o>=(c+=t[d].length)&&(++a,g=c);if(t[a]instanceof n9)continue;f=d-a,n=e.slice(g,c),l.index-=g}else{y.lastIndex=0;var l=y.exec(n),f=1}if(!l){if(s)break;continue}w&&(E=l[1]?l[1].length:0);var o=l.index+E,l=l[0].slice(E),u=o+l.length,p=n.slice(0,o),h=n.slice(u);let r=[a,f];p&&(++a,g+=p.length,r.push(p));let R=new n9(m,v?n5.tokenize(l,v):l,A,l,b);if(r.push(R),h&&r.push(h),Array.prototype.splice.apply(t,r),1!=f&&n5.matchGrammar(e,t,i,a,g,!0,m),s)break}}}},tokenize:function(e,t){let i=[e],n=t.rest;if(n){for(let e in n)t[e]=n[e];delete t.rest}return n5.matchGrammar(e,i,t,0,0,!1),i},hooks:{all:{},add:function(e,t){let i=n5.hooks.all;i[e]=i[e]||[],i[e].push(t)},run:function(e,t){let i=n5.hooks.all[e];if(!(!i||!i.length))for(var n,r=0;n=i[r++];)n(t)}},Token:n9};function n9(e,t,i,n,r){this.type=e,this.content=t,this.alias=i,this.length=0|(n||"").length,this.greedy=!!r}n5.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/},n5.languages.javascript=n5.languages.extend("clike",{"class-name":[n5.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/}),n5.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/,n5.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:n5.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:n5.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:n5.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:n5.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),n5.languages.markup&&n5.languages.markup.tag.addInlined("script","javascript"),n5.languages.js=n5.languages.javascript,n5.languages.typescript=n5.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/}),n5.languages.ts=n5.languages.typescript,n9.stringify=function(e,t){return"string"==typeof e?e:Array.isArray(e)?e.map(function(e){return n9.stringify(e,t)}).join(""):(n4[e.type]||n3)(e.content)};var n8={red:X,gray:ea,dim:H,bold:W,underline:J,highlightSource:e=>e.highlight()},re={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function rt(e){let t=e.showColors?n8:re;return function({functionName:e,location:t,message:i,isPanic:n,contextLines:r,callArguments:s},a){var l;let o,u=[""],d=t?" in":":";if(n?(u.push(a.red(`Oops, an unknown error occurred! This is ${a.bold("on us")}, you did nothing wrong.`)),u.push(a.red(`It occurred in the ${a.bold(`\`${e}\``)} invocation${d}`))):u.push(a.red(`Invalid ${a.bold(`\`${e}\``)} invocation${d}`)),t&&u.push(a.underline((o=[(l=t).fileName],l.lineNumber&&o.push(String(l.lineNumber)),l.columnNumber&&o.push(String(l.columnNumber)),o.join(":")))),r){u.push("");let e=[r.toString()];s&&(e.push(s),e.push(a.dim(")"))),u.push(e.join("")),s&&u.push("")}else u.push(""),s&&u.push(s),u.push("");return u.push(i),u.join(`
`)}(function({callsite:e,message:t,originalMethod:i,isPanic:n,callArguments:r},s){return function({message:e,originalMethod:t,isPanic:i,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:i??!1,callArguments:n}}({message:t,originalMethod:i,isPanic:n,callArguments:r})}(e,0),t)}var ri=p(x());function rn(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function rr(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return -10;default:return 0}}var rs=class{constructor(e,t){this.isRequired=!1,this.name=e,this.value=t}makeRequired(){return this.isRequired=!0,this}write(e){let{colors:{green:t}}=e.context;e.addMarginSymbol(t(this.isRequired?"+":"?")),e.write(t(this.name)),this.isRequired||e.write(t("?")),e.write(t(": ")),"string"==typeof this.value?e.write(t(this.value)):e.write(this.value)}};q();var ra=class{constructor(e=0,t){this.lines=[],this.currentLine="",this.currentIndent=0,this.context=t,this.currentIndent=e}write(e){return"string"==typeof e?this.currentLine+=e:e.write(this),this}writeJoined(e,t,i=(e,t)=>t.write(e)){let n=t.length-1;for(let r=0;r<t.length;r++)i(t[r],this),r!==n&&this.write(e);return this}writeLine(e){return this.write(e).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let e=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,e?.(),this}withIndent(e){return this.indent(),e(this),this.unindent(),this}afterNextNewline(e){return this.afterNextNewLineCallback=e,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(e){return this.marginSymbol=e,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let e=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+e.slice(1):e}};P();var rl=class{constructor(e){this.value=e}write(e){e.write(this.value)}markAsError(){this.value.markAsError()}},ro=e=>e,ru={bold:ro,red:ro,green:ro,dim:ro,enabled:!1},rd={bold:W,red:X,green:ee,dim:H,enabled:!0},rc={write(e){e.writeLine(",")}},rf=class{constructor(e){this.isUnderlined=!1,this.color=e=>e,this.contents=e}underline(){return this.isUnderlined=!0,this}setColor(e){return this.color=e,this}write(e){let t=e.getCurrentLineLength();e.write(this.color(this.contents)),this.isUnderlined&&e.afterNextNewline(()=>{e.write(" ".repeat(t)).writeLine(this.color("~".repeat(this.contents.length)))})}},rp=class{markAsError(){return this.hasError=!0,this}constructor(){this.hasError=!1}},rh=class extends rp{addItem(e){return this.items.push(new rl(e)),this}getField(e){return this.items[e]}getPrintWidth(){return 0===this.items.length?2:Math.max(...this.items.map(e=>e.value.getPrintWidth()))+2}write(e){if(0===this.items.length)return void this.writeEmpty(e);this.writeWithItems(e)}writeEmpty(e){let t=new rf("[]");this.hasError&&t.setColor(e.context.colors.red).underline(),e.write(t)}writeWithItems(e){let{colors:t}=e.context;e.writeLine("[").withIndent(()=>e.writeJoined(rc,this.items).newLine()).write("]"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(t.red("~".repeat(this.getPrintWidth())))})}asObject(){}constructor(...e){super(...e),this.items=[]}},rm=class e extends rp{addField(e){this.fields[e.name]=e}addSuggestion(e){this.suggestions.push(e)}getField(e){return this.fields[e]}getDeepField(t){let[i,...n]=t,r=this.getField(i);if(!r)return;let s=r;for(let t of n){let i;if(s.value instanceof e?i=s.value.getField(t):s.value instanceof rh&&(i=s.value.getField(Number(t))),!i)return;s=i}return s}getDeepFieldValue(e){return 0===e.length?this:this.getDeepField(e)?.value}hasField(e){return!!this.getField(e)}removeAllFields(){this.fields={}}removeField(e){delete this.fields[e]}getFields(){return this.fields}isEmpty(){return 0===Object.keys(this.fields).length}getFieldValue(e){return this.getField(e)?.value}getDeepSubSelectionValue(t){let i=this;for(let n of t){if(!(i instanceof e))return;let t=i.getSubSelectionValue(n);if(!t)return;i=t}return i}getDeepSelectionParent(t){let i=this.getSelectionParent();if(!i)return;let n=i;for(let i of t){let t=n.value.getFieldValue(i);if(!t||!(t instanceof e))return;let r=t.getSelectionParent();if(!r)return;n=r}return n}getSelectionParent(){let e=this.getField("select")?.value.asObject();if(e)return{kind:"select",value:e};let t=this.getField("include")?.value.asObject();if(t)return{kind:"include",value:t}}getSubSelectionValue(e){return this.getSelectionParent()?.value.fields[e].value}getPrintWidth(){let e=Object.values(this.fields);return 0==e.length?2:Math.max(...e.map(e=>e.getPrintWidth()))+2}write(e){let t=Object.values(this.fields);if(0===t.length&&0===this.suggestions.length)return void this.writeEmpty(e);this.writeWithContents(e,t)}asObject(){return this}writeEmpty(e){let t=new rf("{}");this.hasError&&t.setColor(e.context.colors.red).underline(),e.write(t)}writeWithContents(e,t){e.writeLine("{").withIndent(()=>{e.writeJoined(rc,[...t,...this.suggestions]).newLine()}),e.write("}"),this.hasError&&e.afterNextNewline(()=>{e.writeLine(e.context.colors.red("~".repeat(this.getPrintWidth())))})}constructor(...e){super(...e),this.fields={},this.suggestions=[]}},rg=class extends rp{constructor(e){super(),this.text=e}getPrintWidth(){return this.text.length}write(e){let t=new rf(this.text);this.hasError&&t.underline().setColor(e.context.colors.red),e.write(t)}asObject(){}},ry=class{addField(e,t){return this.fields.push({write(i){let{green:n,dim:r}=i.context.colors;i.write(n(r(`${e}: ${t}`))).addMarginSymbol(n(r("+")))}}),this}write(e){let{colors:{green:t}}=e.context;e.writeLine(t("{")).withIndent(()=>{e.writeJoined(rc,this.fields).newLine()}).write(t("}")).addMarginSymbol(t("+"))}constructor(){this.fields=[]}};function rv(e,t,i){let n=[`Unknown argument \`${e.red(t)}\`.`],r=function(e,t){let i=1/0,n;for(let r of t){let t=(0,ri.default)(e,r);t>3||t<i&&(i=t,n=r)}return n}(t,i);return r&&n.push(`Did you mean \`${e.green(r)}\`?`),i.length>0&&n.push(rR(e)),n.join(" ")}function rw(e,t){for(let i of t.fields)e.hasField(i.name)||e.addSuggestion(new rs(i.name,"true"))}function rb(e,t){let[i,n]=rA(e),r=t.arguments.getDeepSubSelectionValue(i)?.asObject();if(!r)return{parentKind:"unknown",fieldName:n};let s=r.getFieldValue("select")?.asObject(),a=r.getFieldValue("include")?.asObject(),l=r.getFieldValue("omit")?.asObject(),o=s?.getField(n);return s&&o?{parentKind:"select",parent:s,field:o,fieldName:n}:(o=a?.getField(n),a&&o?{parentKind:"include",field:o,parent:a,fieldName:n}:(o=l?.getField(n),l&&o?{parentKind:"omit",field:o,parent:l,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function rE(e,t){if("object"===t.kind)for(let i of t.fields)e.hasField(i.name)||e.addSuggestion(new rs(i.name,i.typeNames.join(" | ")))}function rA(e){let t=[...e],i=t.pop();if(!i)throw Error("unexpected empty path");return[t,i]}function rR({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function rT(e,t){if(1===t.length)return t[0];let i=[...t],n=i.pop();return`${i.join(", ")} ${e} ${n}`}var rS=class{constructor(e,t,i,n,r){this.modelName=e,this.name=t,this.typeName=i,this.isList=n,this.isEnum=r}_toGraphQLInputType(){let e=this.isList?"List":"",t=this.isEnum?"Enum":"";return`${e}${t}${this.typeName}FieldRefInput<${this.modelName}>`}};function rI(e){return e instanceof rS}var rx=Symbol(),rP=new WeakMap,rq=class{constructor(e){e===rx?rP.set(this,`Prisma.${this._getName()}`):rP.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return rP.get(this)}},rk=class extends rq{_getNamespace(){return"NullTypes"}},rO=class extends rk{#e};rU(rO,"DbNull");var rN=class extends rk{#e};rU(rN,"JsonNull");var r_=class extends rk{#e};rU(r_,"AnyNull");var rD={classes:{DbNull:rO,JsonNull:rN,AnyNull:r_},instances:{DbNull:new rO(rx),JsonNull:new rN(rx),AnyNull:new r_(rx)}};function rU(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var r$=class{constructor(e,t){this.hasError=!1,this.name=e,this.value=t}markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+2}write(e){let t=new rf(this.name);this.hasError&&t.underline().setColor(e.context.colors.red),e.write(t).write(": ").write(this.value)}},rF=class{constructor(e){this.errorMessages=[],this.arguments=e}write(e){e.write(this.arguments)}addErrorMessage(e){this.errorMessages.push(e)}renderAllMessages(e){return this.errorMessages.map(t=>t(e)).join(`
`)}};function rV(e){return new rF(rC(e))}function rC(e){let t=new rm;for(let[i,n]of Object.entries(e)){let e=new r$(i,function e(t){if("string"==typeof t)return new rg(JSON.stringify(t));if("number"==typeof t||"boolean"==typeof t)return new rg(String(t));if("bigint"==typeof t)return new rg(`${t}n`);if(null===t)return new rg("null");if(void 0===t)return new rg("undefined");if(nX(t))return new rg(`new Prisma.Decimal("${t.toFixed()}")`);if(t instanceof Uint8Array)return Buffer.isBuffer(t)?new rg(`Buffer.alloc(${t.byteLength})`):new rg(`new Uint8Array(${t.byteLength})`);if(t instanceof Date){let e=nZ(t)?t.toISOString():"Invalid Date";return new rg(`new Date("${e}")`)}return t instanceof rq?new rg(`Prisma.${t._getName()}`):rI(t)?new rg(`prisma.${nK(t.modelName)}.$fields.${t.name}`):Array.isArray(t)?function(t){let i=new rh;for(let n of t)i.addItem(e(n));return i}(t):"object"==typeof t?rC(t):new rg(Object.prototype.toString.call(t))}(n));t.addField(e)}return t}function rL(e,t){let i="pretty"===t?rd:ru;return{message:e.renderAllMessages(i),args:new ra(0,{colors:i}).write(e).toString()}}function rj({args:e,errors:t,errorFormat:i,callsite:n,originalMethod:r,clientVersion:s,globalOmit:a}){let l=rV(e);for(let e of t)!function e(t,i,n){switch(t.kind){case"MutuallyExclusiveFields":let r;f=t,p=i,(r=p.arguments.getDeepSubSelectionValue(f.selectionPath)?.asObject())&&(r.getField(f.firstField)?.markAsError(),r.getField(f.secondField)?.markAsError()),p.addErrorMessage(e=>`Please ${e.bold("either")} use ${e.green(`\`${f.firstField}\``)} or ${e.green(`\`${f.secondField}\``)}, but ${e.red("not both")} at the same time.`);break;case"IncludeOnScalar":!function(e,t){let[i,n]=rA(e.selectionPath),r=e.outputType,s=t.arguments.getDeepSelectionParent(i)?.value;if(s&&(s.getField(n)?.markAsError(),r))for(let e of r.fields)e.isRelation&&s.addSuggestion(new rs(e.name,"true"));t.addErrorMessage(e=>{let t=`Invalid scalar field ${e.red(`\`${n}\``)} for ${e.bold("include")} statement`;return r?t+=` on model ${e.bold(r.name)}. ${rR(e)}`:t+=".",t+=`
Note that ${e.bold("include")} statements only accept relation fields.`})}(t,i);break;case"EmptySelection":!function(e,t,i){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){var r,s,a=e,l=t,o=i;for(let e of(o.removeAllFields(),a.outputType.fields))o.addSuggestion(new rs(e.name,"false"));l.addErrorMessage(e=>`The ${e.red("omit")} statement includes every field of the model ${e.bold(a.outputType.name)}. At least one field must be included in the result`);return}if(n.hasField("select")){let i,n,a;return r=e,s=t,i=r.outputType,n=s.arguments.getDeepSelectionParent(r.selectionPath)?.value,a=n?.isEmpty()??!1,n&&(n.removeAllFields(),rw(n,i)),s.addErrorMessage(e=>a?`The ${e.red("`select`")} statement for type ${e.bold(i.name)} must not be empty. ${rR(e)}`:`The ${e.red("`select`")} statement for type ${e.bold(i.name)} needs ${e.bold("at least one truthy value")}.`)}}if(i?.[nK(e.outputType.name)])return function(e,t){let i=new ry;for(let t of e.outputType.fields)t.isRelation||i.addField(t.name,"false");let n=new rs("omit",i).makeRequired();if(0===e.selectionPath.length)t.arguments.addSuggestion(n);else{let[i,r]=rA(e.selectionPath),s=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(r);if(s){let e=s?.value.asObject()??new rm;e.addSuggestion(n),s.value=e}}t.addErrorMessage(t=>`The global ${t.red("omit")} configuration excludes every field of the model ${t.bold(e.outputType.name)}. At least one field must be included in the result`)}(e,t);t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}(t,i,n);break;case"UnknownSelectionField":!function(e,t){let i=rb(e.selectionPath,t);if("unknown"!==i.parentKind){i.field.markAsError();let t=i.parent;switch(i.parentKind){case"select":rw(t,e.outputType);break;case"include":var n=t,r=e.outputType;for(let e of r.fields)e.isRelation&&!n.hasField(e.name)&&n.addSuggestion(new rs(e.name,"true"));break;case"omit":var s=t,a=e.outputType;for(let e of a.fields)s.hasField(e.name)||e.isRelation||s.addSuggestion(new rs(e.name,"true"))}}t.addErrorMessage(t=>{let n=[`Unknown field ${t.red(`\`${i.fieldName}\``)}`];return"unknown"!==i.parentKind&&n.push(`for ${t.bold(i.parentKind)} statement`),n.push(`on model ${t.bold(`\`${e.outputType.name}\``)}.`),n.push(rR(t)),n.join(" ")})}(t,i);break;case"InvalidSelectionValue":let s;h=t,m=i,"unknown"!==(s=rb(h.selectionPath,m)).parentKind&&s.field.value.markAsError(),m.addErrorMessage(e=>`Invalid value for selection field \`${e.red(s.fieldName)}\`: ${h.underlyingError}`);break;case"UnknownArgument":let a,l;g=t,y=i,a=g.argumentPath[0],(l=y.arguments.getDeepSubSelectionValue(g.selectionPath)?.asObject())&&(l.getField(a)?.markAsError(),function(e,t){for(let i of t)e.hasField(i.name)||e.addSuggestion(new rs(i.name,i.typeNames.join(" | ")))}(l,g.arguments)),y.addErrorMessage(e=>rv(e,a,g.arguments.map(e=>e.name)));break;case"UnknownInputField":!function(e,t){let[i,n]=rA(e.argumentPath),r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(r){r.getDeepField(e.argumentPath)?.markAsError();let t=r.getDeepFieldValue(i)?.asObject();t&&rE(t,e.inputType)}t.addErrorMessage(t=>rv(t,n,e.inputType.fields.map(e=>e.name)))}(t,i);break;case"RequiredArgumentMissing":!function(e,t){let i;t.addErrorMessage(e=>i?.value instanceof rg&&"null"===i.value.text?`Argument \`${e.green(s)}\` must not be ${e.red("null")}.`:`Argument \`${e.green(s)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[r,s]=rA(e.argumentPath),a=new ry,l=n.getDeepFieldValue(r)?.asObject();if(l)if((i=l.getField(s))&&l.removeField(s),1===e.inputTypes.length&&"object"===e.inputTypes[0].kind){for(let t of e.inputTypes[0].fields)a.addField(t.name,t.typeNames.join(" | "));l.addSuggestion(new rs(s,a).makeRequired())}else{let t=e.inputTypes.map(function e(t){return"list"===t.kind?`${e(t.elementType)}[]`:t.name}).join(" | ");l.addSuggestion(new rs(s,t).makeRequired())}}(t,i);break;case"InvalidArgumentType":let o,u;v=t,w=i,o=v.argument.name,(u=w.arguments.getDeepSubSelectionValue(v.selectionPath)?.asObject())&&u.getDeepFieldValue(v.argumentPath)?.markAsError(),w.addErrorMessage(e=>{let t=rT("or",v.argument.typeNames.map(t=>e.green(t)));return`Argument \`${e.bold(o)}\`: Invalid value provided. Expected ${t}, provided ${e.red(v.inferredType)}.`});break;case"InvalidArgumentValue":let d,c;b=t,E=i,d=b.argument.name,(c=E.arguments.getDeepSubSelectionValue(b.selectionPath)?.asObject())&&c.getDeepFieldValue(b.argumentPath)?.markAsError(),E.addErrorMessage(e=>{let t=[`Invalid value for argument \`${e.bold(d)}\``];if(b.underlyingError&&t.push(`: ${b.underlyingError}`),t.push("."),b.argument.typeNames.length>0){let i=rT("or",b.argument.typeNames.map(t=>e.green(t)));t.push(` Expected ${i}.`)}return t.join("")});break;case"ValueTooLarge":var f,p,h,m,g,y,v,w,b,E,A=t,R=i;let T=A.argument.name,S=R.arguments.getDeepSubSelectionValue(A.selectionPath)?.asObject(),I;if(S){let e=S.getDeepField(A.argumentPath)?.value;e?.markAsError(),e instanceof rg&&(I=e.text)}R.addErrorMessage(e=>{let t=["Unable to fit value"];return I&&t.push(e.red(I)),t.push(`into a 64-bit signed integer for field \`${e.bold(T)}\``),t.join(" ")});break;case"SomeFieldsMissing":var x=t,P=i;let q=x.argumentPath[x.argumentPath.length-1],k=P.arguments.getDeepSubSelectionValue(x.selectionPath)?.asObject();if(k){let e=k.getDeepFieldValue(x.argumentPath)?.asObject();e&&rE(e,x.inputType)}P.addErrorMessage(e=>{let t=[`Argument \`${e.bold(q)}\` of type ${e.bold(x.inputType.name)} needs`];return 1===x.constraints.minFieldCount?x.constraints.requiredFields?t.push(`${e.green("at least one of")} ${rT("or",x.constraints.requiredFields.map(t=>`\`${e.bold(t)}\``))} arguments.`):t.push(`${e.green("at least one")} argument.`):t.push(`${e.green(`at least ${x.constraints.minFieldCount}`)} arguments.`),t.push(rR(e)),t.join(" ")});break;case"TooManyFieldsGiven":var O=t,N=i;let _=O.argumentPath[O.argumentPath.length-1],D=N.arguments.getDeepSubSelectionValue(O.selectionPath)?.asObject(),U=[];if(D){let e=D.getDeepFieldValue(O.argumentPath)?.asObject();e&&(e.markAsError(),U=Object.keys(e.getFields()))}N.addErrorMessage(e=>{let t=[`Argument \`${e.bold(_)}\` of type ${e.bold(O.inputType.name)} needs`];return 1===O.constraints.minFieldCount&&1==O.constraints.maxFieldCount?t.push(`${e.green("exactly one")} argument,`):1==O.constraints.maxFieldCount?t.push(`${e.green("at most one")} argument,`):t.push(`${e.green(`at most ${O.constraints.maxFieldCount}`)} arguments,`),t.push(`but you provided ${rT("and",U.map(t=>e.red(t)))}. Please choose`),1===O.constraints.maxFieldCount?t.push("one."):t.push(`${O.constraints.maxFieldCount}.`),t.join(" ")});break;case"Union":let $;($=function(e){var t=(e,t)=>{let i=rn(e),n=rn(t);return i!==n?i-n:rr(e)-rr(t)};if(0===e.length)return;let i=e[0];for(let n=1;n<e.length;n++)0>t(i,e[n])&&(i=e[n]);return i}(function(e){let t=new Map,i=[];for(let s of e){var n,r;if("InvalidArgumentType"!==s.kind){i.push(s);continue}let e=`${s.selectionPath.join(".")}:${s.argumentPath.join(".")}`,a=t.get(e);a?t.set(e,{...s,argument:{...s.argument,typeNames:(n=a.argument.typeNames,r=s.argument.typeNames,[...new Set(n.concat(r))])}}):t.set(e,s)}return i.push(...t.values()),i}(function e(t){return t.errors.flatMap(t=>"Union"===t.kind?e(t):[t])}(t))))?e($,i,n):i.addErrorMessage(()=>"Unknown error");break;default:throw Error("not implemented: "+t.kind)}}(e,l,a);let{message:o,args:u}=rL(l,i);throw new iE(rt({message:o,callsite:n,originalMethod:r,showColors:"pretty"===i,callArguments:u}),{clientVersion:s})}function rM(e){return e.replace(/^./,e=>e.toLowerCase())}function rG(e,t,i){return i?ip(i,({needs:e,compute:i},n)=>{var r,s,a;let l;return{name:n,needs:e?Object.keys(e).filter(t=>e[t]):[],compute:(r=t,s=n,a=i,(l=r?.[s]?.compute)?e=>a({...e,[s]:l(e)}):a)}}):{}}var rB=class{constructor(e,t){this.computedFieldsCache=new nH,this.modelExtensionsCache=new nH,this.queryCallbacksCache=new nH,this.clientExtensions=nJ(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions()),this.batchCallbacks=nJ(()=>{let e=this.previous?.getAllBatchQueryCallbacks()??[],t=this.extension.query?.$__internalBatch;return t?e.concat(t):e}),this.extension=e,this.previous=t}getAllComputedFields(e){return this.computedFieldsCache.getOrCreate(e,()=>{var t,i,n;let r,s,a;return t=this.previous?.getAllComputedFields(e),i=this.extension,r=rM(e),i.result&&(i.result.$allModels||i.result[r])?(n={...t,...rG(i.name,t,i.result.$allModels),...rG(i.name,t,i.result[r])},s=new nH,a=(e,t)=>s.getOrCreate(e,()=>t.has(e)?[e]:(t.add(e),n[e]?n[e].needs.flatMap(e=>a(e,t)):[e])),ip(n,e=>({...e,needs:a(e.name,new Set)}))):t})}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(e){return this.modelExtensionsCache.getOrCreate(e,()=>{let t=rM(e);return this.extension.model&&(this.extension.model[t]||this.extension.model.$allModels)?{...this.previous?.getAllModelExtensions(e),...this.extension.model.$allModels,...this.extension.model[t]}:this.previous?.getAllModelExtensions(e)})}getAllQueryCallbacks(e,t){return this.queryCallbacksCache.getOrCreate(`${e}:${t}`,()=>{let i=this.previous?.getAllQueryCallbacks(e,t)??[],n=[],r=this.extension.query;return r&&(r[e]||r.$allModels||r[t]||r.$allOperations)?(void 0!==r[e]&&(void 0!==r[e][t]&&n.push(r[e][t]),void 0!==r[e].$allOperations&&n.push(r[e].$allOperations)),"$none"!==e&&void 0!==r.$allModels&&(void 0!==r.$allModels[t]&&n.push(r.$allModels[t]),void 0!==r.$allModels.$allOperations&&n.push(r.$allModels.$allOperations)),void 0!==r[t]&&n.push(r[t]),void 0!==r.$allOperations&&n.push(r.$allOperations),i.concat(n)):i})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},rW=class e{constructor(e){this.head=e}static empty(){return new e}static single(t){return new e(new rB(t))}isEmpty(){return void 0===this.head}append(t){return new e(new rB(t,this.head))}getAllComputedFields(e){return this.head?.getAllComputedFields(e)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(e){return this.head?.getAllModelExtensions(e)}getAllQueryCallbacks(e,t){return this.head?.getAllQueryCallbacks(e,t)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}},rH=class{constructor(e){this.name=e}};function rK(e){return new rH(e)}var rJ=Symbol(),rQ=class{constructor(e){if(e!==rJ)throw Error("Skip instance can not be constructed directly")}ifUndefined(e){return void 0===e?rY:e}},rY=new rQ(rJ);function rz(e){return e instanceof rQ}var rZ={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},rX="explicitly `undefined` values are not allowed";function r0({modelName:e,action:t,args:i,runtimeDataModel:n,extensions:r=rW.empty(),callsite:s,clientMethod:a,errorFormat:l,clientVersion:o,previewFeatures:u,globalOmit:d}){let c=new r4({runtimeDataModel:n,modelName:e,action:t,rootArgs:i,callsite:s,extensions:r,selectionPath:[],argumentPath:[],originalMethod:a,errorFormat:l,clientVersion:o,previewFeatures:u,globalOmit:d});return{modelName:e,action:rZ[t],query:function e({select:t,include:i,...n}={},r){var s,a,l,o,u,d,c;let f,p=n.omit;return delete n.omit,{arguments:r1(n,r),selection:(s=t,a=i,l=p,o=r,s?(a?o.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:o.getSelectionPath()}):l&&o.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:o.getSelectionPath()}),function(t,i){let n={},r=i.getComputedFields();for(let[s,a]of Object.entries(function(e,t){if(!t)return e;let i={...e};for(let n of Object.values(t))if(e[n.name])for(let e of n.needs)i[e]=!0;return i}(t,r))){if(rz(a))continue;let t=i.nestSelection(s);r2(a,t);let l=i.findField(s);if(!(r?.[s]&&!l)){if(!1===a||void 0===a||rz(a)){n[s]=!1;continue}if(!0===a){l?.kind==="object"?n[s]=e({},t):n[s]=!0;continue}n[s]=e(a,t)}}return n}(s,o)):(u=o,d=a,c=l,f={},u.modelOrType&&!u.isRawAction()&&(f.$composites=!0,f.$scalars=!0),d&&function(t,i,n){for(let[r,s]of Object.entries(i)){if(rz(s))continue;let i=n.nestSelection(r);if(r2(s,i),!1===s||void 0===s){t[r]=!1;continue}let a=n.findField(r);if(a&&"object"!==a.kind&&n.throwValidationError({kind:"IncludeOnScalar",selectionPath:n.getSelectionPath().concat(r),outputType:n.getOutputTypeDescription()}),a){t[r]=e(!0===s?{}:s,i);continue}if(!0===s){t[r]=!0;continue}t[r]=e(s,i)}}(f,d,u),function(e,t,i){let n=i.getComputedFields();for(let[r,s]of Object.entries(function(e,t){if(!t)return e;let i={...e};for(let n of Object.values(t))if(!e[n.name])for(let e of n.needs)delete i[e];return i}({...i.getGlobalOmit(),...t},n))){if(rz(s))continue;r2(s,i.nestSelection(r));let t=i.findField(r);n?.[r]&&!t||(e[r]=!s)}}(f,c,u),f))}}(i,c)}}function r1(e,t){if(e.$type)return{$type:"Raw",value:e};let i={};for(let n in e){let r=e[n],s=t.nestArgument(n);rz(r)||(void 0!==r?i[n]=function e(t,i){var n,r;if(null===t)return null;if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return t;if("bigint"==typeof t)return{$type:"BigInt",value:String(t)};if(nz(t)){if(nZ(t))return{$type:"DateTime",value:t.toISOString()};i.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:i.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(t instanceof rH)return{$type:"Param",value:t.name};if(rI(t))return{$type:"FieldRef",value:{_ref:t.name,_container:t.modelName}};if(Array.isArray(t))return function(t,i){let n=[];for(let r=0;r<t.length;r++){let s=i.nestArgument(String(r)),a=t[r];if(void 0===a||rz(a)){let e=void 0===a?"undefined":"Prisma.skip";i.throwValidationError({kind:"InvalidArgumentValue",selectionPath:s.getSelectionPath(),argumentPath:s.getArgumentPath(),argument:{name:`${i.getArgumentName()}[${r}]`,typeNames:[]},underlyingError:`Can not use \`${e}\` value within array. Use \`null\` or filter out \`${e}\` values`})}n.push(e(a,s))}return n}(t,i);if(ArrayBuffer.isView(t)){let{buffer:e,byteOffset:i,byteLength:n}=t;return{$type:"Bytes",value:Buffer.from(e,i,n).toString("base64")}}if("object"==typeof(n=t)&&null!==n&&!0===n.__prismaRawParameters__)return t.values;if(nX(t))return{$type:"Decimal",value:t.toFixed()};if(t instanceof rq){if(t!==rD.instances[t._getName()])throw Error("Invalid ObjectEnumValue");return{$type:"Enum",value:t._getName()}}return"object"==typeof(r=t)&&null!==r&&"function"==typeof r.toJSON?t.toJSON():"object"==typeof t?r1(t,i):void i.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:i.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(t)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}(r,s):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:s.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:rX}))}return i}function r2(e,t){void 0===e&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:rX})}var r4=class e{constructor(e){this.params=e,this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}throwValidationError(e){rj({errors:[e],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(e=>({name:e.name,typeName:"boolean",isRelation:"object"===e.kind}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(e){return this.params.previewFeatures.includes(e)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(e){return this.modelOrType?.fields.find(t=>t.name===e)}nestSelection(t){let i=this.findField(t),n=i?.kind==="object"?i.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[nK(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:ii(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function r3(e){if(!e._hasPreviewFlag("metrics"))throw new iE("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var r7=class{constructor(e){this._client=e}prometheus(e){return r3(this._client),this._client._engine.metrics({format:"prometheus",...e})}json(e){return r3(this._client),this._client._engine.metrics({format:"json",...e})}};function r6(e,t){let i=nJ(()=>{var e;return{datamodel:{models:r5((e=t).models),enums:r5(e.enums),types:r5(e.types)}}});Object.defineProperty(e,"dmmf",{get:()=>i.get()})}function r5(e){return Object.entries(e).map(([e,t])=>({name:e,...t}))}var r9=new WeakMap,r8="$$PrismaTypedSql",se=class{constructor(e,t){r9.set(this,{sql:e,values:t}),Object.defineProperty(this,r8,{value:r8})}get sql(){return r9.get(this).sql}get values(){return r9.get(this).values}};function st(e){return(...t)=>new se(e,t)}function si(e){return null!=e&&e[r8]===r8}var sn=p(v()),sr=i(16698),ss=i(78474),sa=p(i(73024)),sl=p(i(76760)),so=class e{constructor(t,i){if(t.length-1!==i.length)throw 0===t.length?TypeError("Expected at least 1 string"):TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=i.reduce((t,i)=>t+(i instanceof e?i.values.length:1),0);this.values=Array(n),this.strings=Array(n+1),this.strings[0]=t[0];let r=0,s=0;for(;r<i.length;){let n=i[r++],a=t[r];if(n instanceof e){this.strings[s]+=n.strings[0];let e=0;for(;e<n.values.length;)this.values[s++]=n.values[e++],this.strings[s]=n.strings[e];this.strings[s]+=a}else this.values[s++]=n,this.strings[s]=a}}get sql(){let e=this.strings.length,t=1,i=this.strings[0];for(;t<e;)i+=`?${this.strings[t++]}`;return i}get statement(){let e=this.strings.length,t=1,i=this.strings[0];for(;t<e;)i+=`:${t}${this.strings[t++]}`;return i}get text(){let e=this.strings.length,t=1,i=this.strings[0];for(;t<e;)i+=`$${t}${this.strings[t++]}`;return i}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function su(e,t=",",i="",n=""){if(0===e.length)throw TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new so([i,...Array(e.length-1).fill(t),n],e)}function sd(e){return new so([e],[])}var sc=sd("");function sf(e,...t){return new so(e,t)}function sp(e){return{getKeys:()=>Object.keys(e),getPropertyValue:t=>e[t]}}function sh(e,t){return{getKeys:()=>[e],getPropertyValue:()=>t()}}function sm(e){let t=new nH;return{getKeys:()=>e.getKeys(),getPropertyValue:i=>t.getOrCreate(i,()=>e.getPropertyValue(i)),getPropertyDescriptor:t=>e.getPropertyDescriptor?.(t)}}var sg={enumerable:!0,configurable:!0,writable:!0};function sy(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>sg,has:(e,i)=>t.has(i),set:(e,i,n)=>t.add(i)&&Reflect.set(e,i,n),ownKeys:()=>[...t]}}var sv=Symbol.for("nodejs.util.inspect.custom");function sw(e,t){let i=function(e){let t=new Map;for(let i of e)for(let e of i.getKeys())t.set(e,i);return t}(t),n=new Set,r=new Proxy(e,{get(e,t){if(n.has(t))return e[t];let r=i.get(t);return r?r.getPropertyValue(t):e[t]},has(e,t){if(n.has(t))return!0;let r=i.get(t);return r?r.has?.(t)??!0:Reflect.has(e,t)},ownKeys:e=>[...new Set([...sb(Reflect.ownKeys(e),i),...sb(Array.from(i.keys()),i),...n])],set:(e,t,r)=>i.get(t)?.getPropertyDescriptor?.(t)?.writable!==!1&&(n.add(t),Reflect.set(e,t,r)),getOwnPropertyDescriptor(e,t){let n=Reflect.getOwnPropertyDescriptor(e,t);if(n&&!n.configurable)return n;let r=i.get(t);return r?r.getPropertyDescriptor?{...sg,...r?.getPropertyDescriptor(t)}:sg:n},defineProperty:(e,t,i)=>(n.add(t),Reflect.defineProperty(e,t,i)),getPrototypeOf:()=>Object.prototype});return r[sv]=function(){let e={...this};return delete e[sv],e},r}function sb(e,t){return e.filter(e=>t.get(e)?.has?.(e)??!0)}function sE(e){return{getKeys:()=>e,has:()=>!1,getPropertyValue(){}}}function sA(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function sR({error:e,user_facing_error:t},i,n){var r,s;let a;return t.error_code?new iv((r=t,s=n,a=r.message,("postgresql"===s||"postgres"===s||"mysql"===s)&&"P2037"===r.error_code&&(a+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),a),{code:t.error_code,clientVersion:i,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new ib(e,{clientVersion:i,batchRequestIdx:t.batch_request_idx})}var sT="<unknown>",sS=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,sI=/\((\S*)(?::(\d+))(?::(\d+))\)/,sx=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,sP=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,sq=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,sk=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i,sO=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i,sN=class{getLocation(){return null}},s_=class{constructor(){this._error=Error()}getLocation(){let e=this._error.stack;if(!e)return null;let t=e.split(`
`).reduce(function(e,t){var i,n,r,s,a,l,o=function(e){var t=sS.exec(e);if(!t)return null;var i=t[2]&&0===t[2].indexOf("native"),n=t[2]&&0===t[2].indexOf("eval"),r=sI.exec(t[2]);return n&&null!=r&&(t[2]=r[1],t[3]=r[2],t[4]=r[3]),{file:i?null:t[2],methodName:t[1]||sT,arguments:i?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}(t)||(i=t,(n=sx.exec(i))?{file:n[2],methodName:n[1]||sT,arguments:[],lineNumber:+n[3],column:n[4]?+n[4]:null}:null)||function(e){var t=sP.exec(e);if(!t)return null;var i=t[3]&&t[3].indexOf(" > eval")>-1,n=sq.exec(t[3]);return i&&null!=n&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||sT,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}(t)||(r=t,(s=sO.exec(r))?{file:s[2],methodName:s[1]||sT,arguments:[],lineNumber:+s[3],column:s[4]?+s[4]:null}:null)||(a=t,(l=sk.exec(a))?{file:l[3],methodName:l[1]||sT,arguments:[],lineNumber:+l[4],column:l[5]?+l[5]:null}:null);return o&&e.push(o),e},[]).find(e=>{var t;if(!e.file)return!1;let i=(t=e.file,ir.default.sep===ir.default.posix.sep?t:t.split(ir.default.sep).join(ir.default.posix.sep));return"<anonymous>"!==i&&!i.includes("@prisma")&&!i.includes("/packages/client/src/runtime/")&&!i.endsWith("/runtime/binary.js")&&!i.endsWith("/runtime/library.js")&&!i.endsWith("/runtime/edge.js")&&!i.endsWith("/runtime/edge-esm.js")&&!i.startsWith("internal/")&&!e.methodName.includes("new ")&&!e.methodName.includes("getCallSite")&&!e.methodName.includes("Proxy.")&&e.methodName.split(".").length<4});return t&&t.file?{fileName:t.file,lineNumber:t.lineNumber,columnNumber:t.column}:null}};function sD(e){return"minimal"===e?"function"==typeof $EnabledCallSite&&"minimal"!==e?new $EnabledCallSite:new sN:new s_}var sU={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function s$(e={}){return Object.entries(function(e={}){return"boolean"==typeof e._count?{...e,_count:{_all:e._count}}:e}(e)).reduce((e,[t,i])=>(void 0!==sU[t]?e.select[t]={select:i}:e[t]=i,e),{select:{}})}function sF(e={}){return t=>("boolean"==typeof e._count&&(t._count=t._count._all),t)}function sV(e={}){let{select:t,...i}=e;return"object"==typeof t?s$({...i,_count:t}):s$({...i,_count:{_all:!0}})}function sC(e={}){let t=s$(e);if(Array.isArray(t.by))for(let e of t.by)"string"==typeof e&&(t.select[e]=!0);else"string"==typeof t.by&&(t.select[t.by]=!0);return t}var sL=e=>Array.isArray(e)?e:e.split("."),sj=(e,t)=>sL(t).reduce((e,t)=>e&&e[t],e),sM=(e,t,i)=>sL(t).reduceRight((t,i,n,r)=>Object.assign({},sj(e,r.slice(0,n)),{[i]:t}),i),sG=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],sB=["aggregate","count","groupBy"];function sW(e,t){var i,n,r,s;let a,l,o=e._extensions.getAllModelExtensions(t)??{};return sw({},[(i=e,a=rM(n=t),l=Object.keys(n2).concat("count"),{getKeys:()=>l,getPropertyValue(e){var t;let r=t=>r=>{let s=sD(i._errorFormat);return i._createPrismaPromise(l=>{let o={args:r,dataPath:[],action:e,model:n,clientMethod:`${a}.${e}`,jsModelName:a,transaction:l,callsite:s};return i._request({...o,...t})},{action:e,args:r,model:n})};return sG.includes(e)?function e(t,i,n,r,s,a){let l=t._runtimeDataModel.models[i].fields.reduce((e,t)=>({...e,[t.name]:t}),{});return o=>{var u,d;let c=sD(t._errorFormat),f=void 0===r||void 0===s?[]:[...s,"select",r],p=void 0===a?o??{}:sM(a,f,o||!0),h=n({dataPath:f,callsite:c})(p),m=(u=t,d=i,u._runtimeDataModel.models[d].fields.filter(e=>"object"===e.kind).map(e=>e.name));return new Proxy(h,{get:(i,r)=>m.includes(r)?e(t,l[r].type,n,r,f,p):i[r],...sy([...m,...Object.getOwnPropertyNames(h)])})}}(i,n,r):(t=e,sB.includes(t))?"aggregate"===e?e=>r({action:"aggregate",unpacker:sF(e),argsMapper:s$})(e):"count"===e?e=>r({action:"count",unpacker:function(e={}){return"object"==typeof e.select?t=>sF(e)(t)._count:t=>sF(e)(t)._count._all}(e),argsMapper:sV})(e):"groupBy"===e?e=>r({action:"groupBy",unpacker:function(e={}){return t=>("boolean"==typeof e?._count&&t.forEach(e=>{e._count=e._count._all}),t)}(e),argsMapper:sC})(e):void 0:r({})}}),(r=e,s=t,sm(sh("fields",()=>{let e,t=r._runtimeDataModel.models[s];return new Proxy({},{get(t,i){if(i in t||"symbol"==typeof i)return t[i];let n=e[i];if(n)return new rS(s,i,n.type,n.isList,"enum"===n.kind)},...sy(Object.keys(e=function(e,t){let i={};for(let n of e)i[n[t]]=n;return i}(t.fields.filter(e=>!e.relationName),"name")))})}))),sp(o),sh("name",()=>t),sh("$name",()=>t),sh("$parent",()=>e._appliedParent)])}var sH=Symbol();function sK(e){var t,i;let n,r,s,a,l=[(n=[...new Set(Object.getOwnPropertyNames(Object.getPrototypeOf((t=e)._originalClient)))],{getKeys:()=>n,getPropertyValue:e=>t[e]}),(s=(r=Object.keys((i=e)._runtimeDataModel.models)).map(rM),a=[...new Set(r.concat(s))],sm({getKeys:()=>a,getPropertyValue(e){let t=e.replace(/^./,e=>e.toUpperCase());return void 0!==i._runtimeDataModel.models[t]?sW(i,t):void 0!==i._runtimeDataModel.models[e]?sW(i,e):void 0},getPropertyDescriptor(e){if(!s.includes(e))return{enumerable:!1}}})),sh(sH,()=>e),sh("$parent",()=>e._appliedParent)],o=e._extensions.getAllClientExtensions();return o&&l.push(sp(o)),sw(e,l)}function sJ(e){if("function"==typeof e)return e(this);if(e.client?.__AccelerateEngine){let t=e.client.__AccelerateEngine;this._originalClient._engine=new t(this._originalClient._accelerateEngineConfig)}return sK(Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}}))}function sQ({visitor:e,result:t,args:i,runtimeDataModel:n,modelName:r}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=sQ({result:t[s],args:i,modelName:r,runtimeDataModel:n,visitor:e});return t}let s=e(t,r,i)??t;return i.include&&sY({includeOrSelect:i.include,result:s,parentModelName:r,runtimeDataModel:n,visitor:e}),i.select&&sY({includeOrSelect:i.select,result:s,parentModelName:r,runtimeDataModel:n,visitor:e}),s}function sY({includeOrSelect:e,result:t,parentModelName:i,runtimeDataModel:n,visitor:r}){for(let[s,a]of Object.entries(e)){if(!a||null==t[s]||rz(a))continue;let e=n.models[i].fields.find(e=>e.name===s);if(!e||"object"!==e.kind||!e.relationName)continue;let l="object"==typeof a?a:{};t[s]=sQ({visitor:r,result:t[s],args:l,modelName:e.type,runtimeDataModel:n})}}var sz=["$connect","$disconnect","$on","$transaction","$use","$extends"];function sZ(e){if("object"!=typeof e||null==e||e instanceof rq||rI(e))return e;if(nX(e))return new nB(e.toFixed());if(nz(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,i;for(i=Array(t);t--;)i[t]=sZ(e[t]);return i}if("object"==typeof e){let t={};for(let i in e)"__proto__"===i?Object.defineProperty(t,i,{value:sZ(e[i]),configurable:!0,enumerable:!0,writable:!0}):t[i]=sZ(e[i]);return t}ii(e,"Unknown value")}var sX=e=>e;function s0(e=sX,t=sX){return i=>e(t(i))}var s1=eA("prisma:client"),s2={Vercel:"vercel","Netlify CI":"netlify"},s4=()=>globalThis.process?.release?.name==="node",s3=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,s7=()=>!!globalThis.Deno,s6=()=>"object"==typeof globalThis.Netlify,s5=()=>"object"==typeof globalThis.EdgeRuntime,s9=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers",s8={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function ae(){let e=[[s6,"netlify"],[s5,"edge-light"],[s9,"workerd"],[s7,"deno"],[s3,"bun"],[s4,"node"]].flatMap(e=>e[0]()?[e[1]]:[]).at(0)??"";return{id:e,prettyName:s8[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var at=p(i(73024)),ai=p(i(76760));function an(e){let{runtimeBinaryTarget:t}=e;return`Add "${t}" to \`binaryTargets\` in the "schema.prisma" file and run \`prisma generate\` after saving it:

${function(e){let{generator:t,generatorBinaryTargets:i,runtimeBinaryTarget:n}=e,r=[...i,{fromEnvVar:null,value:n}];return String(new t2({...t,binaryTargets:r}))}(e)}`}function ar(e){let{runtimeBinaryTarget:t}=e;return`Prisma Client could not locate the Query Engine for runtime "${t}".`}function as(e){let{searchedLocations:t}=e;return`The following locations have been searched:
${[...new Set(t)].map(e=>`  ${e}`).join(`
`)}`}function aa(e){return`We would appreciate if you could take the time to share some information with us.
Please help us by answering a few questions: https://pris.ly/${e}`}function al(e){let{errorStack:t}=e;return t?.match(/\/\.next|\/next@|\/next\//)?`

We detected that you are using Next.js, learn how to fix this: https://pris.ly/d/engine-not-found-nextjs.`:""}var ao=eA("prisma:client:engines:resolveEnginePath"),au=()=>RegExp("runtime[\\\\/]library\\.m?js$");async function ad(e,t){let i={binary:process.env.PRISMA_QUERY_ENGINE_BINARY,library:process.env.PRISMA_QUERY_ENGINE_LIBRARY}[e]??t.prismaPath;if(void 0!==i)return i;let{enginePath:n,searchedLocations:r}=await ac(e,t);if(ao("enginePath",n),void 0!==n&&"binary"===e&&function(e){if("win32"===process.platform)return;let t=tz.default.statSync(e),i=64|t.mode|9;if(t.mode===i)return tZ(`Execution permissions of ${e} are fine`);let n=i.toString(8).slice(-3);tZ(`Have to call chmodPlusX on ${e}`),tz.default.chmodSync(e,n)}(n),void 0!==n)return t.prismaPath=n;let s=await te(),a=t.generator?.binaryTargets??[],l=a.some(e=>e.native),o=!a.some(e=>e.value===s),u=null===__filename.match(au()),d={searchedLocations:r,generatorBinaryTargets:a,generator:t.generator,runtimeBinaryTarget:s,queryEngineName:af(e,s),expectedLocation:ai.default.relative(process.cwd(),t.dirname),errorStack:Error().stack},c;throw new iy(l&&o?function(e){let{runtimeBinaryTarget:t,generatorBinaryTargets:i}=e,n=i.find(e=>e.native);return`${ar(e)}

This happened because Prisma Client was generated for "${n?.value??"unknown"}", but the actual deployment required "${t}".
${an(e)}

${as(e)}`}(d):o?function(e){let{runtimeBinaryTarget:t}=e;return`${ar(e)}

This happened because \`binaryTargets\` have been pinned, but the actual deployment also required "${t}".
${an(e)}

${as(e)}`}(d):u?function(e){let{queryEngineName:t}=e;return`${ar(e)}${al(e)}

This is likely caused by a bundler that has not copied "${t}" next to the resulting bundle.
Ensure that "${t}" has been copied next to the bundle or in "${e.expectedLocation}".

${aa("engine-not-found-bundler-investigation")}

${as(e)}`}(d):function(e){let{queryEngineName:t}=e;return`${ar(e)}${al(e)}

This is likely caused by tooling that has not copied "${t}" to the deployment folder.
Ensure that you ran \`prisma generate\` and that "${t}" has been copied to "${e.expectedLocation}".

${aa("engine-not-found-tooling-investigation")}

${as(e)}`}(d),t.clientVersion)}async function ac(e,t){let i=await te(),n=[],r=[t.dirname,ai.default.resolve(__dirname,".."),t.generator?.output?.value??__dirname,ai.default.resolve(__dirname,"../../../.prisma/client"),"/tmp/prisma-engines",t.cwd];for(let t of(__filename.includes("resolveEnginePath")&&r.push(tY.default.join(__dirname,"../")),r)){let r=af(e,i),s=ai.default.join(t,r);if(n.push(t),at.default.existsSync(s))return{enginePath:s,searchedLocations:n}}return{enginePath:void 0,searchedLocations:n}}function af(e,t){return"library"===e?t.includes("windows")?`query_engine-${t}.dll.node`:t.includes("darwin")?`${eT}-${t}.dylib.node`:`${eT}-${t}.so.node`:`query-engine-${t}${"windows"===t?".exe":""}`}var ap=p(R()),ah=p(I());function am(e){return"DriverAdapterError"===e.name&&"object"==typeof e.cause}function ag(e){return{ok:!0,value:e,map:t=>ag(t(e)),flatMap:t=>t(e)}}function ay(e){return{ok:!1,error:e,map:()=>ay(e),flatMap:()=>ay(e)}}var av=eA("driver-adapter-utils"),aw=class{consumeError(e){return this.registeredErrors[e]}registerNewError(e){let t=0;for(;void 0!==this.registeredErrors[t];)t++;return this.registeredErrors[t]={error:e},t}constructor(){this.registeredErrors=[]}},ab=(e,t=new aw)=>{var i,n;let r={adapterName:e.adapterName,errorRegistry:t,queryRaw:aA(t,e.queryRaw.bind(e)),executeRaw:aA(t,e.executeRaw.bind(e)),executeScript:aA(t,e.executeScript.bind(e)),dispose:aA(t,e.dispose.bind(e)),provider:e.provider,startTransaction:async(...i)=>(await aA(t,e.startTransaction.bind(e))(...i)).map(e=>aE(t,e))};return e.getConnectionInfo&&(i=t,n=e.getConnectionInfo.bind(e),r.getConnectionInfo=(...e)=>{try{return ag(n(...e))}catch(e){if(av("[error@wrapSync]",e),am(e))return ay(e.cause);return ay({kind:"GenericJs",id:i.registerNewError(e)})}}),r},aE=(e,t)=>({adapterName:t.adapterName,provider:t.provider,options:t.options,queryRaw:aA(e,t.queryRaw.bind(t)),executeRaw:aA(e,t.executeRaw.bind(t)),commit:aA(e,t.commit.bind(t)),rollback:aA(e,t.rollback.bind(t))});function aA(e,t){return async(...i)=>{try{return ag(await t(...i))}catch(t){if(av("[error@wrapAsync]",t),am(t))return ay(t.cause);return ay({kind:"GenericJs",id:e.registerNewError(t)})}}}function aR({inlineDatasources:e,overrideDatasources:t,env:i,clientVersion:n}){let r,s=Object.keys(e)[0],a=e[s]?.url,l=t[s]?.url;if(void 0===s?r=void 0:l?r=l:a?.value?r=a.value:a?.fromEnvVar&&(r=i[a.fromEnvVar]),a?.fromEnvVar!==void 0&&void 0===r)throw new iy(`error: Environment variable not found: ${a.fromEnvVar}.`,n);if(void 0===r)throw new iy("error: Missing URL environment variable, value, or override.",n);return r}var aT=class extends Error{constructor(e,t){super(e),this.clientVersion=t.clientVersion,this.cause=t.cause}get[Symbol.toStringTag](){return this.name}},aS=class extends aT{constructor(e,t){super(e,t),this.isRetryable=t.isRetryable??!0}};function aI(e,t){return{...e,isRetryable:t}}var ax=class extends aS{constructor(e){super("This request must be retried",aI(e,!0)),this.name="ForcedRetryError",this.code="P5001"}};ih(ax,"ForcedRetryError");var aP=class extends aS{constructor(e,t){super(e,aI(t,!1)),this.name="InvalidDatasourceError",this.code="P6001"}};ih(aP,"InvalidDatasourceError");var aq=class extends aS{constructor(e,t){super(e,aI(t,!1)),this.name="NotImplementedYetError",this.code="P5004"}};ih(aq,"NotImplementedYetError");var ak=class extends aS{constructor(e,t){super(e,t),this.response=t.response;let i=this.response.headers.get("prisma-request-id");if(i){let e=`(The request id was: ${i})`;this.message=this.message+" "+e}}},aO=class extends ak{constructor(e){super("Schema needs to be uploaded",aI(e,!0)),this.name="SchemaMissingError",this.code="P5005"}};ih(aO,"SchemaMissingError");var aN="This request could not be understood by the server",a_=class extends ak{constructor(e,t,i){super(t||aN,aI(e,!1)),this.name="BadRequestError",this.code="P5000",i&&(this.code=i)}};ih(a_,"BadRequestError");var aD=class extends ak{constructor(e,t){super("Engine not started: healthcheck timeout",aI(e,!0)),this.name="HealthcheckTimeoutError",this.code="P5013",this.logs=t}};ih(aD,"HealthcheckTimeoutError");var aU=class extends ak{constructor(e,t,i){super(t,aI(e,!0)),this.name="EngineStartupError",this.code="P5014",this.logs=i}};ih(aU,"EngineStartupError");var a$=class extends ak{constructor(e){super("Engine version is not supported",aI(e,!1)),this.name="EngineVersionNotSupportedError",this.code="P5012"}};ih(a$,"EngineVersionNotSupportedError");var aF="Request timed out",aV=class extends ak{constructor(e,t=aF){super(t,aI(e,!1)),this.name="GatewayTimeoutError",this.code="P5009"}};ih(aV,"GatewayTimeoutError");var aC=class extends ak{constructor(e,t="Interactive transaction error"){super(t,aI(e,!1)),this.name="InteractiveTransactionError",this.code="P5015"}};ih(aC,"InteractiveTransactionError");var aL=class extends ak{constructor(e,t="Request parameters are invalid"){super(t,aI(e,!1)),this.name="InvalidRequestError",this.code="P5011"}};ih(aL,"InvalidRequestError");var aj="Requested resource does not exist",aM=class extends ak{constructor(e,t=aj){super(t,aI(e,!1)),this.name="NotFoundError",this.code="P5003"}};ih(aM,"NotFoundError");var aG="Unknown server error",aB=class extends ak{constructor(e,t,i){super(t||aG,aI(e,!0)),this.name="ServerError",this.code="P5006",this.logs=i}};ih(aB,"ServerError");var aW="Unauthorized, check your connection string",aH=class extends ak{constructor(e,t=aW){super(t,aI(e,!1)),this.name="UnauthorizedError",this.code="P5007"}};ih(aH,"UnauthorizedError");var aK="Usage exceeded, retry again later",aJ=class extends ak{constructor(e,t=aK){super(t,aI(e,!0)),this.name="UsageExceededError",this.code="P5008"}};async function aQ(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let e=JSON.parse(t);if("string"==typeof e)if("InternalDataProxyError"===e)return{type:"DataProxyError",body:e};else return{type:"UnknownTextError",body:e};if("object"==typeof e&&null!==e){if("is_panic"in e&&"message"in e&&"error_code"in e)return{type:"QueryEngineError",body:e};if("EngineNotStarted"in e||"InteractiveTransactionMisrouted"in e||"InvalidRequestError"in e){let t=Object.values(e)[0].reason;return"string"!=typeof t||["SchemaMissing","EngineVersionNotSupported"].includes(t)?{type:"DataProxyError",body:e}:{type:"UnknownJsonError",body:e}}}return{type:"UnknownJsonError",body:e}}catch{return""===t?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function aY(e,t){if(e.ok)return;let i={clientVersion:t,response:e},n=await aQ(e);if("QueryEngineError"===n.type)throw new iv(n.body.message,{code:n.body.error_code,clientVersion:t});if("DataProxyError"===n.type){if("InternalDataProxyError"===n.body)throw new aB(i,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if("SchemaMissing"===n.body.EngineNotStarted.reason)return new aO(i);if("EngineVersionNotSupported"===n.body.EngineNotStarted.reason)throw new a$(i);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:e,logs:t}=n.body.EngineNotStarted.reason.EngineStartupError;throw new aU(i,e,t)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:e,error_code:i}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new iy(e,t,i)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:e}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new aD(i,e)}}if("InteractiveTransactionMisrouted"in n.body)throw new aC(i,{IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"}[n.body.InteractiveTransactionMisrouted.reason]);if("InvalidRequestError"in n.body)throw new aL(i,n.body.InvalidRequestError.reason)}if(401===e.status||403===e.status)throw new aH(i,az(aW,n));if(404===e.status)return new aM(i,az(aj,n));if(429===e.status)throw new aJ(i,az(aK,n));if(504===e.status)throw new aV(i,az(aF,n));if(e.status>=500)throw new aB(i,az(aG,n));if(e.status>=400)throw new a_(i,az(aN,n))}function az(e,t){return"EmptyError"===t.type?e:`${e}: ${JSON.stringify(t)}`}ih(aJ,"UsageExceededError");var aZ="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function aX(e){return new Date(1e3*e[0]+e[1]/1e6)}var a0={"@prisma/engines-version":"6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e"},a1=class extends aS{constructor(e,t){super(`Cannot fetch data from service:
${e}`,aI(t,!0)),this.name="RequestError",this.code="P5010"}};async function a2(e,t,i=e=>e){let{clientVersion:n,...r}=t,s=i(fetch);try{return await s(e,r)}catch(e){throw new a1(e.message??"Unknown error",{clientVersion:n,cause:e})}}ih(a1,"RequestError");var a4=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,a3=eA("prisma:client:dataproxyEngine");async function a7(e,t){let i=a0["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&"0.0.0"!==n&&"in-memory"!==n)return n;let[r,s]=n?.split("-")??[];if(void 0===s&&a4.test(r))return r;if(void 0!==s||"0.0.0"===n||"in-memory"===n){var a;let e,[t]=i.split("-")??[],[r,s,l]=t.split("."),o=(a=`<=${r}.${s}.${l}`,encodeURI(`https://unpkg.com/prisma@${a}/package.json`)),u=await a2(o,{clientVersion:n});if(!u.ok)throw Error(`Failed to fetch stable Prisma version, unpkg.com status ${u.status} ${u.statusText}, response body: ${await u.text()||"<empty body>"}`);let d=await u.text();a3("length of body fetched from unpkg.com",d.length);try{e=JSON.parse(d)}catch(e){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),e}return e.version}throw new aq("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function a6(e,t){let i=await a7(e,t);return a3("version",i),i}var a5,a9=eA("prisma:client:dataproxyEngine"),a8=class{constructor({apiKey:e,tracingHelper:t,logLevel:i,logQueries:n,engineHash:r}){this.apiKey=e,this.tracingHelper=t,this.logLevel=i,this.logQueries=n,this.engineHash=r}build({traceparent:e,interactiveTransaction:t}={}){let i={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(i.traceparent=e??this.tracingHelper.getTraceParent()),t&&(i["X-transaction-id"]=t.id);let n=this.buildCaptureSettings();return n.length>0&&(i["X-capture-telemetry"]=n.join(", ")),i}buildCaptureSettings(){let e=[];return this.tracingHelper.isEnabled()&&e.push("tracing"),this.logLevel&&e.push(this.logLevel),this.logQueries&&e.push("query"),e}},le=class{constructor(e){this.name="DataProxyEngine",function(e){if(e.generator?.previewFeatures.some(e=>e.toLowerCase().includes("metrics")))throw new iy("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}(e),this.config=e,this.env={...e.env,..."u">typeof process?process.env:{}},this.inlineSchema=function(e){let t=new TextEncoder().encode(e),i="",n=t.byteLength,r=n%3,s=n-r,a,l,o,u,d;for(let e=0;e<s;e+=3)a=(0xfc0000&(d=t[e]<<16|t[e+1]<<8|t[e+2]))>>18,l=(258048&d)>>12,o=(4032&d)>>6,u=63&d,i+=aZ[a]+aZ[l]+aZ[o]+aZ[u];return 1==r?(a=(252&(d=t[s]))>>2,l=(3&d)<<4,i+=aZ[a]+aZ[l]+"=="):2==r&&(a=(64512&(d=t[s]<<8|t[s+1]))>>10,l=(1008&d)>>4,o=(15&d)<<2,i+=aZ[a]+aZ[l]+aZ[o]+"="),i}(e.inlineSchema),this.inlineDatasources=e.inlineDatasources,this.inlineSchemaHash=e.inlineSchemaHash,this.clientVersion=e.clientVersion,this.engineHash=e.engineVersion,this.logEmitter=e.logEmitter,this.tracingHelper=e.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){void 0!==this.startPromise&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:e,url:t}=this.getURLAndAPIKey();this.host=t.host,this.headerBuilder=new a8({apiKey:e,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.protocol=!function(e){if(!t0(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")}(t)?"https":"http",this.remoteClientVersion=await a6(this.host,this.config),a9("host",this.host),a9("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(e){e?.logs?.length&&e.logs.forEach(e=>{switch(e.level){case"debug":case"trace":a9(e);break;case"error":case"warn":case"info":this.logEmitter.emit(e.level,{timestamp:aX(e.timestamp),message:e.attributes.message??"",target:e.target});break;case"query":this.logEmitter.emit("query",{query:e.attributes.query??"",timestamp:aX(e.timestamp),duration:e.attributes.duration_ms??0,params:e.attributes.params??"",target:e.target});break;default:e.level}}),e?.traces?.length&&this.tracingHelper.dispatchEngineSpans(e.traces)}onBeforeExit(){throw Error('"beforeExit" hook is not applicable to the remote query engine')}async url(e){return await this.start(),`${this.protocol}://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${e}`}async uploadSchema(){return this.tracingHelper.runInChildSpan({name:"schemaUpload",internal:!0},async()=>{let e=await a2(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});e.ok||a9("schema response status",e.status);let t=await aY(e,this.clientVersion);if(t)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${t.message}`,timestamp:new Date,target:""}),t;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(e,{traceparent:t,interactiveTransaction:i,customDataProxyFetch:n}){return this.requestInternal({body:e,traceparent:t,interactiveTransaction:i,customDataProxyFetch:n})}async requestBatch(e,{traceparent:t,transaction:i,customDataProxyFetch:n}){let r=i?.kind==="itx"?i.options:void 0,s=sA(e,i);return(await this.requestInternal({body:s,customDataProxyFetch:n,interactiveTransaction:r,traceparent:t})).map(e=>(e.extensions&&this.propagateResponseExtensions(e.extensions),"errors"in e?this.convertProtocolErrorsToClientError(e.errors):e))}requestInternal({body:e,traceparent:t,customDataProxyFetch:i,interactiveTransaction:n}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:r})=>{let s=n?`${n.payload.endpoint}/graphql`:await this.url("graphql");r(s);let a=await a2(s,{method:"POST",headers:this.headerBuilder.build({traceparent:t,interactiveTransaction:n}),body:JSON.stringify(e),clientVersion:this.clientVersion},i);a.ok||a9("graphql response status",a.status),await this.handleError(await aY(a,this.clientVersion));let l=await a.json();if(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l)throw this.convertProtocolErrorsToClientError(l.errors);return"batchResult"in l?l.batchResult:l}})}async transaction(e,t,i){return this.withRetry({actionGerund:`${{start:"starting",commit:"committing",rollback:"rolling back"}[e]} transaction`,callback:async({logHttpCall:n})=>{if("start"===e){let e=JSON.stringify({max_wait:i.maxWait,timeout:i.timeout,isolation_level:i.isolationLevel}),r=await this.url("transaction/start");n(r);let s=await a2(r,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),body:e,clientVersion:this.clientVersion});await this.handleError(await aY(s,this.clientVersion));let a=await s.json(),{extensions:l}=a;return l&&this.propagateResponseExtensions(l),{id:a.id,payload:{endpoint:a["data-proxy"].endpoint}}}{let r=`${i.payload.endpoint}/${e}`;n(r);let s=await a2(r,{method:"POST",headers:this.headerBuilder.build({traceparent:t.traceparent}),clientVersion:this.clientVersion});await this.handleError(await aY(s,this.clientVersion));let{extensions:a}=await s.json();a&&this.propagateResponseExtensions(a);return}}})}getURLAndAPIKey(){let e={clientVersion:this.clientVersion},t=Object.keys(this.inlineDatasources)[0],i=aR({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),n;try{n=new URL(i)}catch{throw new aP(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\``,e)}let{protocol:r,searchParams:s}=n;if("prisma:"!==r&&r!==tX)throw new aP(`Error validating datasource \`${t}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,e);let a=s.get("api_key");if(null===a||a.length<1)throw new aP(`Error validating datasource \`${t}\`: the URL must contain a valid API key`,e);return{apiKey:a,url:n}}metrics(){throw new aq("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(e){for(let t=0;;t++){let i=e=>{this.logEmitter.emit("info",{message:`Calling ${e} (n=${t})`,timestamp:new Date,target:""})};try{return await e.callback({logHttpCall:i})}catch(n){if(!(n instanceof aS)||!n.isRetryable)throw n;if(t>=3)throw n instanceof ax?n.cause:n;this.logEmitter.emit("warn",{message:`Attempt ${t+1}/3 failed for ${e.actionGerund}: ${n.message??"(unknown)"}`,timestamp:new Date,target:""});let i=await function(e){let t=50*Math.pow(2,e),i=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+i;return new Promise(e=>setTimeout(()=>e(n),n))}(t);this.logEmitter.emit("warn",{message:`Retrying after ${i}ms`,timestamp:new Date,target:""})}}}async handleError(e){if(e instanceof aO)throw await this.uploadSchema(),new ax({clientVersion:this.clientVersion,cause:e});if(e)throw e}convertProtocolErrorsToClientError(e){return 1===e.length?sR(e[0],this.config.clientVersion,this.config.activeProvider):new ib(JSON.stringify(e),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw Error("Method not implemented.")}},lt=p(i(48161)),li=p(i(76760)),ln=Symbol("PrismaLibraryEngineCache"),lr={async loadLibrary(e){let t=await tt(),i=await ad("library",e);try{return e.tracingHelper.runInChildSpan({name:"loadLibrary",internal:!0},()=>(function(e){let t,i=(void 0===(t=globalThis)[ln]&&(t[ln]={}),t[ln]);if(void 0!==i[e])return i[e];let n=li.default.toNamespacedPath(e),r={exports:{}},s=0;return"win32"!==process.platform&&(s=lt.default.constants.dlopen.RTLD_LAZY|lt.default.constants.dlopen.RTLD_DEEPBIND),process.dlopen(r,n,s),i[e]=r.exports,r.exports})(i))}catch(c){var n,r;let s,a,l,o,u,d;throw new iy((s=(n={e:c,platformInfo:t,id:i}).e,a=e=>`Prisma cannot find the required \`${e}\` system library in your system`,l=s.message.includes("cannot open shared object file"),o=`Please refer to the documentation about Prisma's system requirements: ${tK(r="https://pris.ly/d/system-requirements",r,{fallback:J})}`,u=`Unable to require(\`${H(n.id)}\`).`,d=eJ({message:s.message,code:s.code}).with({code:"ENOENT"},()=>"File does not exist.").when(({message:e})=>l&&e.includes("libz"),()=>`${a("libz")}. Please install it and try again.`).when(({message:e})=>l&&e.includes("libgcc_s"),()=>`${a("libgcc_s")}. Please install it and try again.`).when(({message:e})=>l&&e.includes("libssl"),()=>{let e=n.platformInfo.libssl?`openssl-${n.platformInfo.libssl}`:"openssl";return`${a("libssl")}. Please install ${e} and try again.`}).when(({message:e})=>e.includes("GLIBC"),()=>`Prisma has detected an incompatible version of the \`glibc\` C standard library installed in your system. This probably means your system may be too old to run Prisma. ${o}`).when(({message:e})=>"linux"===n.platformInfo.platform&&e.includes("symbol not found"),()=>`The Prisma engines are not compatible with your system ${n.platformInfo.originalDistro} on (${n.platformInfo.archFromUname}) which uses the \`${n.platformInfo.binaryTarget}\` binaryTarget by default. ${o}`).otherwise(()=>`The Prisma engines do not seem to be compatible with your system. ${o}`),`${u}
${d}

Details: ${s.message}`),e.clientVersion)}}},ls={async loadLibrary(e){let{clientVersion:t,adapter:i,engineWasm:n}=e;if(void 0===i)throw new iy(`The \`adapter\` option for \`PrismaClient\` is required in this context (${ae().prettyName})`,t);if(void 0===n)throw new iy("WASM engine was unexpectedly `undefined`",t);return void 0===a5&&(a5=(async()=>{let e=await n.getRuntime(),i=await n.getQueryEngineWasmModule();if(null==i)throw new iy("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let r=new WebAssembly.Instance(i,{"./query_engine_bg.js":e}),s=r.exports.__wbindgen_start;return e.__wbg_set_wasm(r.exports),s(),e.QueryEngine})()),{debugPanic:()=>Promise.reject("{}"),dmmf:()=>Promise.resolve("{}"),version:()=>({commit:"unknown",version:"unknown"}),QueryEngine:await a5}}},la=eA("prisma:client:libraryEngine"),ll=["darwin","darwin-arm64","debian-openssl-1.0.x","debian-openssl-1.1.x","debian-openssl-3.0.x","rhel-openssl-1.0.x","rhel-openssl-1.1.x","rhel-openssl-3.0.x","linux-arm64-openssl-1.1.x","linux-arm64-openssl-1.0.x","linux-arm64-openssl-3.0.x","linux-arm-openssl-1.1.x","linux-arm-openssl-1.0.x","linux-arm-openssl-3.0.x","linux-musl","linux-musl-openssl-3.0.x","linux-musl-arm64-openssl-1.1.x","linux-musl-arm64-openssl-3.0.x","linux-nixos","linux-static-x64","linux-static-arm64","windows","freebsd11","freebsd12","freebsd13","freebsd14","freebsd15","openbsd","netbsd","arm","native"],lo=1n,lu=class{constructor(e,t){this.name="LibraryEngine",this.libraryLoader=t??lr,void 0!==e.engineWasm&&(this.libraryLoader=t??ls),this.config=e,this.libraryStarted=!1,this.logQueries=e.logQueries??!1,this.logLevel=e.logLevel??"error",this.logEmitter=e.logEmitter,this.datamodel=e.inlineSchema,this.tracingHelper=e.tracingHelper,e.enableDebugLogs&&(this.logLevel="debug");let i=Object.keys(e.overrideDatasources)[0],n=e.overrideDatasources[i]?.url;void 0!==i&&void 0!==n&&(this.datasourceOverrides={[i]:n}),this.libraryInstantiationPromise=this.instantiateLibrary()}wrapEngine(e){return{applyPendingMigrations:e.applyPendingMigrations?.bind(e),commitTransaction:this.withRequestId(e.commitTransaction.bind(e)),connect:this.withRequestId(e.connect.bind(e)),disconnect:this.withRequestId(e.disconnect.bind(e)),metrics:e.metrics?.bind(e),query:this.withRequestId(e.query.bind(e)),rollbackTransaction:this.withRequestId(e.rollbackTransaction.bind(e)),sdlSchema:e.sdlSchema?.bind(e),startTransaction:this.withRequestId(e.startTransaction.bind(e)),trace:e.trace.bind(e)}}withRequestId(e){return async(...t)=>{let i,n=(i=lo++,lo>0xffffffffffffffffn&&(lo=1n),i).toString();try{return await e(...t,n)}finally{if(this.tracingHelper.isEnabled()){let e=await this.engine?.trace(n);if(e){let t=JSON.parse(e);this.tracingHelper.dispatchEngineSpans(t.spans)}}}}}async applyPendingMigrations(){throw Error("Cannot call this method from this type of engine instance")}async transaction(e,t,i){var n;await this.start();let r=await this.adapterPromise,s=JSON.stringify(t),a;if("start"===e){let e=JSON.stringify({max_wait:i.maxWait,timeout:i.timeout,isolation_level:i.isolationLevel});a=await this.engine?.startTransaction(e,s)}else"commit"===e?a=await this.engine?.commitTransaction(i.id,s):"rollback"===e&&(a=await this.engine?.rollbackTransaction(i.id,s));let l=this.parseEngineResponse(a);if("object"==typeof(n=l)&&null!==n&&void 0!==n.error_code){let e=this.getExternalAdapterError(l,r?.errorRegistry);throw e?e.error:new iv(l.message,{code:l.error_code,clientVersion:this.config.clientVersion,meta:l.meta})}if("string"==typeof l.message)throw new ib(l.message,{clientVersion:this.config.clientVersion});return l}async instantiateLibrary(){if(la("internalSetup"),this.libraryInstantiationPromise)return this.libraryInstantiationPromise;(function(){let e=process.env.PRISMA_QUERY_ENGINE_LIBRARY;if(!(e&&eR.default.existsSync(e))&&"ia32"===process.arch)throw Error('The default query engine type (Node-API, "library") is currently not supported for 32bit Node. Please set `engineType = "binary"` in the "generator" block of your "schema.prisma" file (or use the environment variables "PRISMA_CLIENT_ENGINE_TYPE=binary" and/or "PRISMA_CLI_QUERY_ENGINE_TYPE=binary".)')})(),this.binaryTarget=await this.getCurrentBinaryTarget(),await this.tracingHelper.runInChildSpan("load_engine",()=>this.loadEngine()),this.version()}async getCurrentBinaryTarget(){{if(this.binaryTarget)return this.binaryTarget;let e=await this.tracingHelper.runInChildSpan("detect_platform",()=>te());if(!ll.includes(e))throw new iy(`Unknown ${X("PRISMA_QUERY_ENGINE_LIBRARY")} ${X(W(e))}. Possible binaryTargets: ${ee(ll.join(", "))} or a path to the query engine library.
You may have to run ${ee("prisma generate")} for your changes to take effect.`,this.config.clientVersion);return e}}parseEngineResponse(e){if(!e)throw new ib("Response from the Engine was empty",{clientVersion:this.config.clientVersion});try{return JSON.parse(e)}catch{throw new ib("Unable to JSON.parse response from engine",{clientVersion:this.config.clientVersion})}}async loadEngine(){if(!this.engine){this.QueryEngineConstructor||(this.library=await this.libraryLoader.loadLibrary(this.config),this.QueryEngineConstructor=this.library.QueryEngine);try{let e=new WeakRef(this);this.adapterPromise||(this.adapterPromise=this.config.adapter?.connect()?.then(ab));let t=await this.adapterPromise;t&&la("Using driver adapter: %O",t),this.engine=this.wrapEngine(new this.QueryEngineConstructor({datamodel:this.datamodel,env:process.env,logQueries:this.config.logQueries??!1,ignoreEnvVarErrors:!0,datasourceOverrides:this.datasourceOverrides??{},logLevel:this.logLevel,configDir:this.config.cwd,engineProtocol:"json",enableTracing:this.tracingHelper.isEnabled()},t=>{e.deref()?.logger(t)},t))}catch(t){let e=this.parseInitError(t.message);throw"string"==typeof e?t:new iy(e.message,this.config.clientVersion,e.error_code)}}}logger(e){let t=this.parseEngineResponse(e);t&&(t.level=t?.level.toLowerCase()??"unknown","query"===t.item_type&&"query"in t?this.logEmitter.emit("query",{timestamp:new Date,query:t.query,params:t.params,duration:Number(t.duration_ms),target:t.module_path}):"level"in t&&"error"===t.level&&"PANIC"===t.message?this.loggerRustPanic=new iw(ld(this,`${t.message}: ${t.reason} in ${t.file}:${t.line}:${t.column}`),this.config.clientVersion):this.logEmitter.emit(t.level,{timestamp:new Date,message:t.message,target:t.module_path}))}parseInitError(e){try{return JSON.parse(e)}catch{}return e}parseRequestError(e){try{return JSON.parse(e)}catch{}return e}onBeforeExit(){throw Error('"beforeExit" hook is not applicable to the library engine since Prisma 5.0.0, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){if(await this.libraryInstantiationPromise,await this.libraryStoppingPromise,this.libraryStartingPromise)return la(`library already starting, this.libraryStarted: ${this.libraryStarted}`),this.libraryStartingPromise;if(this.libraryStarted)return;let e=async()=>{la("library starting");try{let e={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.connect(JSON.stringify(e)),this.libraryStarted=!0,la("library started")}catch(t){let e=this.parseInitError(t.message);throw"string"==typeof e?t:new iy(e.message,this.config.clientVersion,e.error_code)}finally{this.libraryStartingPromise=void 0}};return this.libraryStartingPromise=this.tracingHelper.runInChildSpan("connect",e),this.libraryStartingPromise}async stop(){if(await this.libraryInstantiationPromise,await this.libraryStartingPromise,await this.executingQueryPromise,this.libraryStoppingPromise)return la("library is already stopping"),this.libraryStoppingPromise;if(!this.libraryStarted)return;let e=async()=>{await new Promise(e=>setTimeout(e,5)),la("library stopping");let e={traceparent:this.tracingHelper.getTraceParent()};await this.engine?.disconnect(JSON.stringify(e)),this.libraryStarted=!1,this.libraryStoppingPromise=void 0,await (await this.adapterPromise)?.dispose(),this.adapterPromise=void 0,la("library stopped")};return this.libraryStoppingPromise=this.tracingHelper.runInChildSpan("disconnect",e),this.libraryStoppingPromise}version(){return this.versionInfo=this.library?.version(),this.versionInfo?.version??"unknown"}debugPanic(e){return this.library?.debugPanic(e)}async request(e,{traceparent:t,interactiveTransaction:i}){la(`sending request, this.libraryStarted: ${this.libraryStarted}`);let n=JSON.stringify({traceparent:t}),r=JSON.stringify(e);try{await this.start();let e=await this.adapterPromise;this.executingQueryPromise=this.engine?.query(r,n,i?.id),this.lastQuery=r;let t=this.parseEngineResponse(await this.executingQueryPromise);if(t.errors)throw 1===t.errors.length?this.buildQueryError(t.errors[0],e?.errorRegistry):new ib(JSON.stringify(t.errors),{clientVersion:this.config.clientVersion});if(this.loggerRustPanic)throw this.loggerRustPanic;return{data:t}}catch(t){if(t instanceof iy)throw t;if("GenericFailure"===t.code&&t.message?.startsWith("PANIC:"))throw new iw(ld(this,t.message),this.config.clientVersion);let e=this.parseRequestError(t.message);throw"string"==typeof e?t:new ib(`${e.message}
${e.backtrace}`,{clientVersion:this.config.clientVersion})}}async requestBatch(e,{transaction:t,traceparent:i}){la("requestBatch");let n=sA(e,t);await this.start();let r=await this.adapterPromise;this.lastQuery=JSON.stringify(n),this.executingQueryPromise=this.engine.query(this.lastQuery,JSON.stringify({traceparent:i}),function(e){if(e?.kind==="itx")return e.options.id}(t));let s=await this.executingQueryPromise,a=this.parseEngineResponse(s);if(a.errors)throw 1===a.errors.length?this.buildQueryError(a.errors[0],r?.errorRegistry):new ib(JSON.stringify(a.errors),{clientVersion:this.config.clientVersion});let{batchResult:l,errors:o}=a;if(Array.isArray(l))return l.map(e=>e.errors&&e.errors.length>0?this.loggerRustPanic??this.buildQueryError(e.errors[0],r?.errorRegistry):{data:e});throw o&&1===o.length?Error(o[0].error):Error(JSON.stringify(a))}buildQueryError(e,t){if(e.user_facing_error.is_panic)return new iw(ld(this,e.user_facing_error.message),this.config.clientVersion);let i=this.getExternalAdapterError(e.user_facing_error,t);return i?i.error:sR(e,this.config.clientVersion,this.config.activeProvider)}getExternalAdapterError(e,t){if("P2036"===e.error_code&&t){let i=e.meta?.id;it("number"==typeof i,"Malformed external JS error received from the engine");let n=t.consumeError(i);return it(n,"External error with reported id was not registered"),n}}async metrics(e){await this.start();let t=await this.engine.metrics(JSON.stringify(e));return"prometheus"===e.format?t:this.parseEngineResponse(t)}};function ld(e,t){return function({version:e,binaryTarget:t,title:i,description:n,engineVersion:r,database:s,query:a}){var l;let o=function(e=7500){let t=ey.map(([e,...t])=>`${e} ${t.map(e=>"string"==typeof e?e:JSON.stringify(e)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}(6e3-(a?.length??0)),u=(0,ap.default)(o).split(`
`).map(e=>e.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`),d=n?`# Description
\`\`\`
${n}
\`\`\``:"",c=function({title:e,user:t="prisma",repo:i="prisma",template:n="bug_report.yml",body:r}){return(0,ah.default)({user:t,repo:i,template:n,title:e,body:r})}({title:i,body:(0,ap.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${r?.padEnd(19)}|
| Database        | ${s?.padEnd(19)}|

${d}

## Logs
\`\`\`
${u}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${a&&(l=a)?l.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,e=>`${e[0]}5`):""}
\`\`\`
`)});return`${i}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${J(c)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}({binaryTarget:e.binaryTarget,title:t,version:e.config.clientVersion,engineVersion:e.versionInfo?.commit,database:e.config.activeProvider,query:e.lastQuery})}function lc({generator:e}){return e?.previewFeatures??[]}var lf=e=>({command:e}),lp=e=>e.strings.reduce((e,t,i)=>`${e}@P${i}${t}`);function lh(e){try{return lm(e,"fast")}catch{return lm(e,"slow")}}function lm(e,t){return JSON.stringify(e.map(e=>(function e(t,i){var n;if(Array.isArray(t))return t.map(t=>e(t,i));if("bigint"==typeof t)return{prisma__type:"bigint",prisma__value:t.toString()};if(nz(t))return{prisma__type:"date",prisma__value:t.toJSON()};if(nB.isDecimal(t))return{prisma__type:"decimal",prisma__value:t.toJSON()};if(Buffer.isBuffer(t))return{prisma__type:"bytes",prisma__value:t.toString("base64")};if((n=t)instanceof ArrayBuffer||n instanceof SharedArrayBuffer||"object"==typeof n&&null!==n&&("ArrayBuffer"===n[Symbol.toStringTag]||"SharedArrayBuffer"===n[Symbol.toStringTag]))return{prisma__type:"bytes",prisma__value:Buffer.from(t).toString("base64")};if(ArrayBuffer.isView(t)){let{buffer:e,byteOffset:i,byteLength:n}=t;return{prisma__type:"bytes",prisma__value:Buffer.from(e,i,n).toString("base64")}}return"object"==typeof t&&"slow"===i?lg(t):t})(e,t)))}function lg(e){if("object"!=typeof e||null===e)return e;if("function"==typeof e.toJSON)return e.toJSON();if(Array.isArray(e))return e.map(ly);let t={};for(let i of Object.keys(e))t[i]=ly(e[i]);return t}function ly(e){return"bigint"==typeof e?e.toString():lg(e)}var lv=/^(\s*alter\s)/i,lw=eA("prisma:client");function lb(e,t,i,n){if(("postgresql"===e||"cockroachdb"===e)&&i.length>0&&lv.exec(t))throw Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var lE=({clientMethod:e,activeProvider:t})=>i=>{let n="",r;if(si(i))n=i.sql,r={values:lh(i.values),__prismaRawParameters__:!0};else if(Array.isArray(i)){let[e,...t]=i;n=e,r={values:lh(t||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":n=i.sql,r={values:lh(i.values),__prismaRawParameters__:!0};break;case"cockroachdb":case"postgresql":case"postgres":n=i.text,r={values:lh(i.values),__prismaRawParameters__:!0};break;case"sqlserver":n=lp(i),r={values:lh(i.values),__prismaRawParameters__:!0};break;default:throw Error(`The ${t} provider does not support ${e}`)}return r?.values?lw(`prisma.${e}(${n}, ${r.values})`):lw(`prisma.${e}(${n})`),{query:n,parameters:r}},lA={requestArgsToMiddlewareArgs:e=>[e.strings,...e.values],middlewareArgsToRequestArgs(e){let[t,...i]=e;return new so(t,i)}},lR={requestArgsToMiddlewareArgs:e=>[e],middlewareArgsToRequestArgs:e=>e[0]};function lT(e){return function(t,i){let n,r=(i=e)=>{try{return void 0===i||i?.kind==="itx"?n??=lS(t(i)):lS(t(i))}catch(e){return Promise.reject(e)}};return{get spec(){return i},then:(e,t)=>r().then(e,t),catch:e=>r().catch(e),finally:e=>r().finally(e),requestTransaction(e){let t=r(e);return t.requestTransaction?t.requestTransaction(e):t},[Symbol.toStringTag]:"PrismaPromise"}}}function lS(e){return"function"==typeof e.then?e:Promise.resolve(e)}var lI=tJ.split(".")[0],lx={isEnabled:()=>!1,getTraceParent:()=>"00-10-10-00",dispatchEngineSpans(){},getActiveContext(){},runInChildSpan:(e,t)=>t()},lP=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(e){return this.getGlobalTracingHelper().getTraceParent(e)}dispatchEngineSpans(e){return this.getGlobalTracingHelper().dispatchEngineSpans(e)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(e,t){return this.getGlobalTracingHelper().runInChildSpan(e,t)}getGlobalTracingHelper(){let e=globalThis[`V${lI}_PRISMA_INSTRUMENTATION`],t=globalThis.PRISMA_INSTRUMENTATION;return e?.helper??t?.helper??lx}},lq=class{use(e){this._middlewares.push(e)}get(e){return this._middlewares[e]}has(e){return!!this._middlewares[e]}length(){return this._middlewares.length}constructor(){this._middlewares=[]}},lk=p(R());function lO(e){return"number"==typeof e.batchRequestIdx}function lN(e){return`(${Object.keys(e).sort().map(t=>{let i=e[t];return"object"==typeof i&&null!==i?`(${t} ${lN(i)})`:t}).join(" ")})`}var l_={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0},lD=class{constructor(e){this.tickActive=!1,this.options=e,this.batches={}}request(e){let t=this.options.batchBy(e);return t?(this.batches[t]||(this.batches[t]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((i,n)=>{this.batches[t].push({request:e,resolve:i,reject:n})})):this.options.singleLoader(e)}dispatchBatches(){for(let e in this.batches){let t=this.batches[e];delete this.batches[e],1===t.length?this.options.singleLoader(t[0].request).then(e=>{e instanceof Error?t[0].reject(e):t[0].resolve(e)}).catch(e=>{t[0].reject(e)}):(t.sort((e,t)=>this.options.batchOrder(e.request,t.request)),this.options.batchLoader(t.map(e=>e.request)).then(e=>{if(e instanceof Error)for(let i=0;i<t.length;i++)t[i].reject(e);else for(let i=0;i<t.length;i++){let n=e[i];n instanceof Error?t[i].reject(n):t[i].resolve(n)}}).catch(e=>{for(let i=0;i<t.length;i++)t[i].reject(e)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function lU(e){let t=[],i=function(e){let t={};for(let i=0;i<e.columns.length;i++)t[e.columns[i]]=null;return t}(e);for(let n=0;n<e.rows.length;n++){let r=e.rows[n],s={...i};for(let t=0;t<r.length;t++)s[e.columns[t]]=function e(t,i){if(null===i)return i;switch(t){case"bigint":return BigInt(i);case"bytes":{let{buffer:e,byteOffset:t,byteLength:n}=Buffer.from(i,"base64");return new Uint8Array(e,t,n)}case"decimal":return new nB(i);case"datetime":case"date":return new Date(i);case"time":return new Date(`1970-01-01T${i}Z`);case"bigint-array":return i.map(t=>e("bigint",t));case"bytes-array":return i.map(t=>e("bytes",t));case"decimal-array":return i.map(t=>e("decimal",t));case"datetime-array":return i.map(t=>e("datetime",t));case"date-array":return i.map(t=>e("date",t));case"time-array":return i.map(t=>e("time",t));default:return i}}(e.types[t],r[t]);t.push(s)}return t}var l$=eA("prisma:client:request_handler"),lF=class{constructor(e,t){this.logEmitter=t,this.client=e,this.dataloader=new lD({batchLoader:function(e){return t=>{let i={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?function e(t,i,n,r){if(n===i.length)return r(t);let s=t.customDataProxyFetch,a=t.requests[0].transaction;return i[n]({args:{queries:t.requests.map(e=>({model:e.modelName,operation:e.action,args:e.args})),transaction:a?{isolationLevel:"batch"===a.kind?a.isolationLevel:void 0}:void 0},__internalParams:t,query(a,l=t){let o=l.customDataProxyFetch;return l.customDataProxyFetch=s0(s,o),e(l,i,n+1,r)}})}(i,n,0,e):e(i)}}(async({requests:e,customDataProxyFetch:t})=>{let{transaction:i,otelParentCtx:n}=e[0],r=e.map(e=>e.protocolQuery),s=this.client._tracingHelper.getTraceParent(n),a=e.some(e=>l_[e.protocolQuery.action]);return(await this.client._engine.requestBatch(r,{traceparent:s,transaction:function(e){if(e){if("batch"===e.kind)return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if("itx"===e.kind)return{kind:"itx",options:lV(e)};ii(e,"Unknown transaction kind")}}(i),containsWrite:a,customDataProxyFetch:t})).map((t,i)=>{if(t instanceof Error)return t;try{return this.mapQueryEngineResult(e[i],t)}catch(e){return e}})}),singleLoader:async e=>{let t=e.transaction?.kind==="itx"?lV(e.transaction):void 0,i=await this.client._engine.request(e.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:t,isWrite:l_[e.protocolQuery.action],customDataProxyFetch:e.customDataProxyFetch});return this.mapQueryEngineResult(e,i)},batchBy:e=>e.transaction?.id?`transaction-${e.transaction.id}`:function(e){if("findUnique"!==e.action&&"findUniqueOrThrow"!==e.action)return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(lN(e.query.arguments)),t.push(lN(e.query.selection)),t.join("")}(e.protocolQuery),batchOrder:(e,t)=>e.transaction?.kind==="batch"&&t.transaction?.kind==="batch"?e.transaction.index-t.transaction.index:0})}async request(e){try{return await this.dataloader.request(e)}catch(a){let{clientMethod:t,callsite:i,transaction:n,args:r,modelName:s}=e;this.handleAndLogRequestError({error:a,clientMethod:t,callsite:i,transaction:n,args:r,modelName:s,globalOmit:e.globalOmit})}}mapQueryEngineResult({dataPath:e,unpacker:t},i){let n=i?.data,r=this.unpack(n,e,t);return process.env.PRISMA_CLIENT_GET_TIME?{data:r}:r}handleAndLogRequestError(e){try{this.handleRequestError(e)}catch(t){throw this.logEmitter&&this.logEmitter.emit("error",{message:t.message,target:e.clientMethod,timestamp:new Date}),t}}handleRequestError({error:e,clientMethod:t,callsite:i,transaction:n,args:r,modelName:s,globalOmit:a}){var l,o,u;if(l$(e),l=e,o=n,lO(l)&&o?.kind==="batch"&&l.batchRequestIdx!==o.index)throw e;e instanceof iv&&("P2009"===(u=e).code||"P2012"===u.code)&&rj({args:r,errors:[function e(t){if("Union"===t.kind)return{kind:"Union",errors:t.errors.map(e)};if(Array.isArray(t.selectionPath)){let[,...e]=t.selectionPath;return{...t,selectionPath:e}}return t}(e.meta)],callsite:i,errorFormat:this.client._errorFormat,originalMethod:t,clientVersion:this.client._clientVersion,globalOmit:a});let d=e.message;if(i&&(d=rt({callsite:i,originalMethod:t,isPanic:e.isPanic,showColors:"pretty"===this.client._errorFormat,message:d})),d=this.sanitizeMessage(d),e.code){let t=s?{modelName:s,...e.meta}:e.meta;throw new iv(d,{code:e.code,clientVersion:this.client._clientVersion,meta:t,batchRequestIdx:e.batchRequestIdx})}if(e.isPanic)throw new iw(d,this.client._clientVersion);if(e instanceof ib)throw new ib(d,{clientVersion:this.client._clientVersion,batchRequestIdx:e.batchRequestIdx});if(e instanceof iy)throw new iy(d,this.client._clientVersion);if(e instanceof iw)throw new iw(d,this.client._clientVersion);throw e.clientVersion=this.client._clientVersion,e}sanitizeMessage(e){return this.client._errorFormat&&"pretty"!==this.client._errorFormat?(0,lk.default)(e):e}unpack(e,t,i){if(!e||(e.data&&(e=e.data),!e))return e;let n=Object.keys(e)[0],r=sj(Object.values(e)[0],t.filter(e=>"select"!==e&&"include"!==e)),s="queryRaw"===n?lU(r):nW(r);return i?i(s):s}get[Symbol.toStringTag](){return"RequestHandler"}};function lV(e){return{id:e.id,payload:e.payload}}var lC=p(x()),lL=class extends Error{constructor(e){super(e+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};ih(lL,"PrismaClientConstructorValidationError");var lj=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],lM=["pretty","colorless","minimal"],lG=["info","query","warn","error"],lB={datasources:(e,{datasourceNames:t})=>{if(e){if("object"!=typeof e||Array.isArray(e))throw new lL(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[i,n]of Object.entries(e)){if(!t.includes(i)){let e=lW(i,t)||` Available datasources: ${t.join(", ")}`;throw new lL(`Unknown datasource ${i} provided to PrismaClient constructor.${e}`)}if("object"!=typeof n||Array.isArray(n))throw new lL(`Invalid value ${JSON.stringify(e)} for datasource "${i}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&"object"==typeof n)for(let[t,r]of Object.entries(n)){if("url"!==t)throw new lL(`Invalid value ${JSON.stringify(e)} for datasource "${i}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if("string"!=typeof r)throw new lL(`Invalid value ${JSON.stringify(r)} for datasource "${i}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&"client"===tQ(t.generator))throw new lL('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(null!==e){if(void 0===e)throw new lL('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!lc(t).includes("driverAdapters"))throw new lL('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if("binary"===tQ(t.generator))throw new lL('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')}},datasourceUrl:e=>{if("u">typeof e&&"string"!=typeof e)throw new lL(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if("string"!=typeof e)throw new lL(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!lM.includes(e)){let t=lW(e,lM);throw new lL(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(e){if(!Array.isArray(e))throw new lL(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);for(let i of e){t(i);let e={level:t,emit:e=>{let t=["stdout","event"];if(!t.includes(e)){let i=lW(e,t);throw new lL(`Invalid value ${JSON.stringify(e)} for "emit" in logLevel provided to PrismaClient constructor.${i}`)}}};if(i&&"object"==typeof i)for(let[t,n]of Object.entries(i))if(e[t])e[t](n);else throw new lL(`Invalid property ${t} for "log" provided to PrismaClient constructor`)}}function t(e){if("string"==typeof e&&!lG.includes(e)){let t=lW(e,lG);throw new lL(`Invalid log level "${e}" provided to PrismaClient constructor.${t}`)}}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(null!=t&&t<=0)throw new lL(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let i=e.timeout;if(null!=i&&i<=0)throw new lL(`Invalid value ${i} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if("object"!=typeof e)throw new lL('"omit" option is expected to be an object.');if(null===e)throw new lL('"omit" option can not be `null`');let i=[];for(let[n,r]of Object.entries(e)){let e=function(e,t){return lH(t.models,e)??lH(t.types,e)}(n,t.runtimeDataModel);if(!e){i.push({kind:"UnknownModel",modelKey:n});continue}for(let[t,s]of Object.entries(r)){let r=e.fields.find(e=>e.name===t);if(!r){i.push({kind:"UnknownField",modelKey:n,fieldName:t});continue}if(r.relationName){i.push({kind:"RelationInOmit",modelKey:n,fieldName:t});continue}"boolean"!=typeof s&&i.push({kind:"InvalidFieldValue",modelKey:n,fieldName:t})}}if(i.length>0)throw new lL(function(e,t){let i=rV(e);for(let e of t)switch(e.kind){case"UnknownModel":i.arguments.getField(e.modelKey)?.markAsError(),i.addErrorMessage(()=>`Unknown model name: ${e.modelKey}.`);break;case"UnknownField":i.arguments.getDeepField([e.modelKey,e.fieldName])?.markAsError(),i.addErrorMessage(()=>`Model "${e.modelKey}" does not have a field named "${e.fieldName}".`);break;case"RelationInOmit":i.arguments.getDeepField([e.modelKey,e.fieldName])?.markAsError(),i.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":i.arguments.getDeepFieldValue([e.modelKey,e.fieldName])?.markAsError(),i.addErrorMessage(()=>"Omit field option value must be a boolean.")}let{message:n,args:r}=rL(i,"colorless");return`Error validating "omit" option:

${r}

${n}`}(e,i))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if("object"!=typeof e)throw new lL(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[i]of Object.entries(e))if(!t.includes(i)){let e=lW(i,t);throw new lL(`Invalid property ${JSON.stringify(i)} for "__internal" provided to PrismaClient constructor.${e}`)}}};function lW(e,t){if(0===t.length||"string"!=typeof e)return"";let i=function(e,t){if(0===t.length)return null;let i=t.map(t=>({value:t,distance:(0,lC.default)(e,t)}));i.sort((e,t)=>e.distance<t.distance?-1:1);let n=i[0];return n.distance<3?n.value:null}(e,t);return i?` Did you mean "${i}"?`:""}function lH(e,t){let i=Object.keys(e).find(e=>nK(e)===t);if(i)return e[i]}var lK=eA("prisma:client");"object"==typeof globalThis&&(globalThis.NODE_CLIENT=!0);var lJ={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},lQ=Symbol.for("prisma.client.transaction.id"),lY={id:0,nextId(){return++this.id}};function lz(e){class t{constructor(t){this._originalClient=this,this._middlewares=new lq,this._createPrismaPromise=lT(),this.$metrics=new r7(this),this.$extends=sJ,function({postinstall:e,ciName:t,clientVersion:i}){if(s1("checkPlatformCaching:postinstall",e),s1("checkPlatformCaching:ciName",t),!0===e&&t&&t in s2){let e=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${s2[t]}-build`;throw console.error(e),new iy(e,i)}}(e=t?.__internal?.configOverride?.(e)??e),t&&function(e,t){for(let[i,n]of Object.entries(e)){if(!lj.includes(i)){let e=lW(i,lj);throw new lL(`Unknown property ${i} provided to PrismaClient constructor.${e}`)}lB[i](n,t)}if(e.datasourceUrl&&e.datasources)throw new lL('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}(t,e);let i=new ss.EventEmitter().on("error",()=>{});this._extensions=rW.empty(),this._previewFeatures=lc(e),this._clientVersion=e.clientVersion??"6.8.2",this._activeProvider=e.activeProvider,this._globalOmit=t?.omit,this._tracingHelper=new lP;let n=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&sl.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&sl.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},r;if(t?.adapter){r=t.adapter;let i="postgresql"===e.activeProvider?"postgres":e.activeProvider;if(r.provider!==i)throw new iy(`The Driver Adapter \`${r.adapterName}\`, based on \`${r.provider}\`, is not compatible with the provider \`${i}\` specified in the Prisma schema.`,this._clientVersion);if(t.datasources||void 0!==t.datasourceUrl)throw new iy("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let s=!r&&n&&iu(n,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{var a,l;let n=t??{},o=n.__internal??{},u=!0===o.debug;u&&eA.enable("prisma:client");let d=sl.default.resolve(e.dirname,e.relativePath);sa.default.existsSync(d)||(d=e.dirname),lK("dirname",e.dirname),lK("relativePath",e.relativePath),lK("cwd",d);let c=o.engine||{};if(n.errorFormat?this._errorFormat=n.errorFormat:this._errorFormat="minimal",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:d,dirname:e.dirname,enableDebugLogs:u,allowTriggerPanic:c.allowTriggerPanic,prismaPath:c.binaryPath??void 0,engineEndpoint:c.endpoint,generator:e.generator,showColors:"pretty"===this._errorFormat,logLevel:n.log&&(a=n.log,"string"==typeof a?a:a.reduce((e,t)=>{let i="string"==typeof t?t:t.level;return"query"===i?e:e&&("info"===t||"info"===e)?"info":i},void 0)),logQueries:n.log&&!!("string"==typeof n.log?"query"===n.log:n.log.find(e=>"string"==typeof e?"query"===e:"query"===e.level)),env:s?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:(l=e.datasourceNames,n?n.datasources?n.datasources:n.datasourceUrl?{[l[0]]:{url:n.datasourceUrl}}:{}:{}),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:n.transactionOptions?.maxWait??2e3,timeout:n.transactionOptions?.timeout??5e3,isolationLevel:n.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:r},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:aR,getBatchRequestPayload:sA,prismaGraphQLToJSError:sR,PrismaClientUnknownRequestError:ib,PrismaClientInitializationError:iy,PrismaClientKnownRequestError:iv,debug:eA("prisma:client:accelerateEngine"),engineVersion:sn.version,clientVersion:e.clientVersion}},lK("clientVersion",e.clientVersion),this._engine=function({copyEngine:e=!0},t){let i;try{i=aR({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let n=!!(i?.startsWith("prisma://")||t0(i));e&&n&&ig("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"),tQ(t.generator);let r=n||!e,s=!!t.adapter;if(r&&s){let n;throw new iE((e?i?.startsWith("prisma://")?["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]).join(`
`),{clientVersion:t.clientVersion})}return r?new le(t):new lu(t)}(e,this._engineConfig),this._requestHandler=new lF(this,i),n.log)for(let e of n.log){let t="string"==typeof e?e:"stdout"===e.emit?e.level:null;t&&this.$on(t,e=>{t4.log(`${t4.tags[t]??""}`,e.message||e.query)})}}catch(e){throw e.clientVersion=this._clientVersion,e}return this._appliedParent=sK(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(e){this._middlewares.use(e)}$on(e,t){return"beforeExit"===e?this._engine.onBeforeExit(t):e&&this._engineConfig.logEmitter.on(e,t),this}$connect(){try{return this._engine.start()}catch(e){throw e.clientVersion=this._clientVersion,e}}async $disconnect(){try{await this._engine.stop()}catch(e){throw e.clientVersion=this._clientVersion,e}finally{ey.length=0}}$executeRawInternal(e,t,i,n){let r=this._activeProvider;return this._request({action:"executeRaw",args:i,transaction:e,clientMethod:t,argsMapper:lE({clientMethod:t,activeProvider:r}),callsite:sD(this._errorFormat),dataPath:[],middlewareArgsMapper:n})}$executeRaw(e,...t){return this._createPrismaPromise(i=>{if(void 0!==e.raw||void 0!==e.sql){let[n,r]=lZ(e,t);return lb(this._activeProvider,n.text,n.values,Array.isArray(e)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(i,"$executeRaw",n,r)}throw new iE("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(e,...t){return this._createPrismaPromise(i=>(lb(this._activeProvider,e,t,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(i,"$executeRawUnsafe",[e,...t])))}$runCommandRaw(t){if("mongodb"!==e.activeProvider)throw new iE(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(e=>this._request({args:t,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:lf,callsite:sD(this._errorFormat),transaction:e}))}async $queryRawInternal(e,t,i,n){let r=this._activeProvider;return this._request({action:"queryRaw",args:i,transaction:e,clientMethod:t,argsMapper:lE({clientMethod:t,activeProvider:r}),callsite:sD(this._errorFormat),dataPath:[],middlewareArgsMapper:n})}$queryRaw(e,...t){return this._createPrismaPromise(i=>{if(void 0!==e.raw||void 0!==e.sql)return this.$queryRawInternal(i,"$queryRaw",...lZ(e,t));throw new iE("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(e){return this._createPrismaPromise(t=>{if(!this._hasPreviewFlag("typedSql"))throw new iE("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(t,"$queryRawTyped",e)})}$queryRawUnsafe(e,...t){return this._createPrismaPromise(i=>this.$queryRawInternal(i,"$queryRawUnsafe",[e,...t]))}_transactionWithArray({promises:e,options:t}){var i;let n=lY.nextId(),r=function(e,t=()=>{}){let i,n=new Promise(e=>i=e);return{then:r=>(0==--e&&i(t()),r?.(n))}}(e.length);return 0===(i=e.map((e,i)=>{if(e?.[Symbol.toStringTag]!=="PrismaPromise")throw Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let s=t?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel;return e.requestTransaction?.({kind:"batch",id:n,index:i,isolationLevel:s,lock:r})??e})).length?Promise.resolve([]):new Promise((e,t)=>{let n=Array(i.length),r=null,s=!1,a=0,l=()=>{s||++a===i.length&&(s=!0,r?t(r):e(n))},o=e=>{s||(s=!0,t(e))};for(let e=0;e<i.length;e++)i[e].then(t=>{n[e]=t,l()},t=>{if(!lO(t))return void o(t);t.batchRequestIdx===e?o(t):(r||(r=t),l())})})}async _transactionWithCallback({callback:e,options:t}){let i={traceparent:this._tracingHelper.getTraceParent()},n={maxWait:t?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:t?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:t?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},r=await this._engine.transaction("start",i,n),s;try{let t={kind:"itx",...r};s=await e(this._createItxClient(t)),await this._engine.transaction("commit",i,r)}catch(e){throw await this._engine.transaction("rollback",i,r).catch(()=>{}),e}return s}_createItxClient(e){return sw(sK(sw(this[sH]?this[sH]:this,[sh("_appliedParent",()=>this._appliedParent._createItxClient(e)),sh("_createPrismaPromise",()=>lT(e)),sh(lQ,()=>e.id)])),[sE(sz)])}$transaction(e,t){let i;return i="function"==typeof e?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?()=>{throw Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:()=>this._transactionWithCallback({callback:e,options:t}):()=>this._transactionWithArray({promises:e,options:t}),this._tracingHelper.runInChildSpan({name:"transaction",attributes:{method:"$transaction"}},i)}_request(e){e.otelParentCtx=this._tracingHelper.getActiveContext();let t=e.middlewareArgsMapper??lJ,i={args:t.requestArgsToMiddlewareArgs(e.args),dataPath:e.dataPath,runInTransaction:!!e.transaction,action:e.action,model:e.model},n={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:i.action,model:i.model,name:i.model?`${i.model}.${i.action}`:i.action}}},r=-1,s=async i=>{let a=this._middlewares.get(++r);if(a)return this._tracingHelper.runInChildSpan(n.middleware,e=>a(i,t=>(e?.end(),s(t))));let{runInTransaction:l,args:o,...u}=i,d={...e,...u};o&&(d.args=t.middlewareArgsToRequestArgs(o)),void 0!==e.transaction&&!1===l&&delete d.transaction;let c=await function(e,t){let{jsModelName:i,action:n,clientMethod:r}=t;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(i??"$none",i?n:r);return function e(t,i,n,r=0){return t._createPrismaPromise(s=>{let a=i.customDataProxyFetch;return"transaction"in i&&void 0!==s&&(i.transaction?.kind==="batch"&&i.transaction.lock.then(),i.transaction=s),r===n.length?t._executeRequest(i):n[r]({model:i.model,operation:i.model?i.action:i.clientMethod,args:function(e){var t,i;if(e instanceof so){return new so((t=e).strings,t.values)}if(si(e)){return new se((i=e).sql,i.values)}if(Array.isArray(e)){let t=[e[0]];for(let i=1;i<e.length;i++)t[i]=sZ(e[i]);return t}let n={};for(let t in e)n[t]=sZ(e[t]);return n}(i.args??{}),__internalParams:i,query:(s,l=i)=>{let o=l.customDataProxyFetch;return l.customDataProxyFetch=s0(a,o),l.args=s,e(t,l,n,r+1)}})})}(e,t,s)}(this,d);return d.model?function({result:e,modelName:t,args:i,extensions:n,runtimeDataModel:r,globalOmit:s}){return n.isEmpty()||null==e||"object"!=typeof e||!r.models[t]?e:sQ({result:e,args:i??{},modelName:t,runtimeDataModel:r,visitor:(e,t,i)=>{let r=rM(t);return function({result:e,modelName:t,select:i,omit:n,extensions:r}){let s=r.getAllComputedFields(t);if(!s)return e;let a=[],l=[];for(let t of Object.values(s)){if(n){if(n[t.name])continue;let e=t.needs.filter(e=>n[e]);e.length>0&&l.push(sE(e))}else if(i){if(!i[t.name])continue;let e=t.needs.filter(e=>!i[e]);e.length>0&&l.push(sE(e))}(function(e,t){return t.every(t=>Object.prototype.hasOwnProperty.call(e,t))})(e,t.needs)&&a.push(function(e,t){return sm(sh(e.name,()=>e.compute(t)))}(t,sw(e,a)))}return a.length>0||l.length>0?sw(e,[...a,...l]):e}({result:e,modelName:r,select:i.select,omit:i.select?void 0:{...s?.[r],...i.omit},extensions:n})}})}({result:c,modelName:d.model,args:d.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):c};return this._tracingHelper.runInChildSpan(n.operation,()=>new sr.AsyncResource("prisma-client-request").runInAsyncScope(()=>s(i)))}async _executeRequest({args:e,clientMethod:t,dataPath:i,callsite:n,action:r,model:s,argsMapper:a,transaction:l,unpacker:o,otelParentCtx:u,customDataProxyFetch:d}){try{e=a?a(e):e;let c=this._tracingHelper.runInChildSpan({name:"serialize"},()=>r0({modelName:s,runtimeDataModel:this._runtimeDataModel,action:r,args:e,clientMethod:t,callsite:n,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return eA.enabled("prisma:client")&&(lK("Prisma Client call:"),lK(`prisma.${t}(${function(e){if(void 0===e)return"";let t=rV(e);return new ra(0,{colors:ru}).write(t).toString()}(e)})`),lK("Generated request:"),lK(JSON.stringify(c,null,2)+`
`)),l?.kind==="batch"&&await l.lock,this._requestHandler.request({protocolQuery:c,modelName:s,action:r,clientMethod:t,dataPath:i,callsite:n,args:e,extensions:this._extensions,transaction:l,unpacker:o,otelParentCtx:u,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:d})}catch(e){throw e.clientVersion=this._clientVersion,e}}_hasPreviewFlag(e){return!!this._engineConfig.previewFeatures?.includes(e)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}}return t}function lZ(e,t){var i;return Array.isArray(i=e)&&Array.isArray(i.raw)?[new so(e,t),lA]:[e,lR]}var lX=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function l0(e){return new Proxy(e,{get(e,t){if(t in e)return e[t];if(!lX.has(t))throw TypeError(`Invalid enum value: ${String(t)}`)}})}function l1(e){iu(e,{conflictCheck:"warn"})}},31183:(e,t,i)=>{"use strict";i.d(t,{z:()=>r});var n=i(38824);let r=globalThis.prisma??new n.PrismaClient},38824:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});let{PrismaClientKnownRequestError:n,PrismaClientUnknownRequestError:r,PrismaClientRustPanicError:s,PrismaClientInitializationError:a,PrismaClientValidationError:l,getPrismaClient:o,sqltag:u,empty:d,join:c,raw:f,skip:p,Decimal:h,Debug:m,objectEnumValues:g,makeStrictEnum:y,Extensions:v,warnOnce:w,defineDmmfProperty:b,Public:E,getRuntime:A,createParam:R}=i(23604),T={};t.Prisma=T,t.$Enums={},T.prismaVersion={client:"6.8.2",engine:"2060c79ba17c6bb9f5823312b6f6b7f4a845738e"},T.PrismaClientKnownRequestError=n,T.PrismaClientUnknownRequestError=r,T.PrismaClientRustPanicError=s,T.PrismaClientInitializationError=a,T.PrismaClientValidationError=l,T.Decimal=h,T.sql=u,T.empty=d,T.join=c,T.raw=f,T.validator=E.validator,T.getExtensionContext=v.getExtensionContext,T.defineExtension=v.defineExtension,T.DbNull=g.instances.DbNull,T.JsonNull=g.instances.JsonNull,T.AnyNull=g.instances.AnyNull,T.NullTypes={DbNull:g.classes.DbNull,JsonNull:g.classes.JsonNull,AnyNull:g.classes.AnyNull};let S=i(33873);t.Prisma.TransactionIsolationLevel=y({Serializable:"Serializable"}),t.Prisma.UserScalarFieldEnum={id:"id",email:"email",username:"username",password:"password",role:"role",firstName:"firstName",lastName:"lastName",bio:"bio",avatarUrl:"avatarUrl",website:"website",location:"location",twitterUrl:"twitterUrl",linkedinUrl:"linkedinUrl",githubUrl:"githubUrl",emailNotifications:"emailNotifications",profilePublic:"profilePublic",createdAt:"createdAt",updatedAt:"updatedAt",lastLoginAt:"lastLoginAt"},t.Prisma.AccountScalarFieldEnum={id:"id",userId:"userId",type:"type",provider:"provider",providerAccountId:"providerAccountId",refresh_token:"refresh_token",access_token:"access_token",expires_at:"expires_at",token_type:"token_type",scope:"scope",id_token:"id_token",session_state:"session_state"},t.Prisma.SessionScalarFieldEnum={id:"id",sessionToken:"sessionToken",userId:"userId",expires:"expires"},t.Prisma.VerificationTokenScalarFieldEnum={identifier:"identifier",token:"token",expires:"expires"},t.Prisma.ArticleScalarFieldEnum={id:"id",title:"title",subtitle:"subtitle",slug:"slug",content:"content",excerpt:"excerpt",featuredImageUrl:"featuredImageUrl",featuredImageAlt:"featuredImageAlt",status:"status",publishedAt:"publishedAt",scheduledAt:"scheduledAt",viewCount:"viewCount",readingTime:"readingTime",metaDescription:"metaDescription",ogTitle:"ogTitle",ogDescription:"ogDescription",ogImageUrl:"ogImageUrl",isFeatured:"isFeatured",allowComments:"allowComments",createdAt:"createdAt",updatedAt:"updatedAt",authorId:"authorId",categoryId:"categoryId"},t.Prisma.ArticleVersionScalarFieldEnum={id:"id",title:"title",content:"content",version:"version",createdAt:"createdAt",articleId:"articleId"},t.Prisma.CategoryScalarFieldEnum={id:"id",name:"name",slug:"slug",description:"description",color:"color",articleCount:"articleCount",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.TagScalarFieldEnum={id:"id",name:"name",slug:"slug",description:"description",usageCount:"usageCount",createdAt:"createdAt",updatedAt:"updatedAt"},t.Prisma.ArticleTagScalarFieldEnum={articleId:"articleId",tagId:"tagId"},t.Prisma.CommentScalarFieldEnum={id:"id",content:"content",parentId:"parentId",createdAt:"createdAt",updatedAt:"updatedAt",authorId:"authorId",articleId:"articleId"},t.Prisma.ReactionScalarFieldEnum={id:"id",type:"type",createdAt:"createdAt",userId:"userId",articleId:"articleId"},t.Prisma.BookmarkScalarFieldEnum={id:"id",createdAt:"createdAt",userId:"userId",articleId:"articleId"},t.Prisma.FollowScalarFieldEnum={id:"id",createdAt:"createdAt",followerId:"followerId",followingId:"followingId"},t.Prisma.NotificationScalarFieldEnum={id:"id",type:"type",title:"title",message:"message",read:"read",data:"data",createdAt:"createdAt",userId:"userId"},t.Prisma.ArticleViewScalarFieldEnum={id:"id",ipAddress:"ipAddress",userAgent:"userAgent",country:"country",viewedAt:"viewedAt",articleId:"articleId",userId:"userId"},t.Prisma.SortOrder={asc:"asc",desc:"desc"},t.Prisma.NullableJsonNullValueInput={DbNull:T.DbNull,JsonNull:T.JsonNull},t.Prisma.NullsOrder={first:"first",last:"last"},t.Prisma.JsonNullValueFilter={DbNull:T.DbNull,JsonNull:T.JsonNull,AnyNull:T.AnyNull},t.Prisma.QueryMode={default:"default",insensitive:"insensitive"},t.UserRole=t.$Enums.UserRole={ADMIN:"ADMIN",EDITOR:"EDITOR",AUTHOR:"AUTHOR",READER:"READER"},t.ArticleStatus=t.$Enums.ArticleStatus={DRAFT:"DRAFT",REVIEW:"REVIEW",PUBLISHED:"PUBLISHED",ARCHIVED:"ARCHIVED"},t.ReactionType=t.$Enums.ReactionType={LIKE:"LIKE",CLAP:"CLAP",LOVE:"LOVE",DISLIKE:"DISLIKE"},t.NotificationType=t.$Enums.NotificationType={NEW_FOLLOWER:"NEW_FOLLOWER",NEW_COMMENT:"NEW_COMMENT",NEW_REACTION:"NEW_REACTION",ARTICLE_PUBLISHED:"ARTICLE_PUBLISHED",MENTION:"MENTION",SYSTEM:"SYSTEM"},t.Prisma.ModelName={User:"User",Account:"Account",Session:"Session",VerificationToken:"VerificationToken",Article:"Article",ArticleVersion:"ArticleVersion",Category:"Category",Tag:"Tag",ArticleTag:"ArticleTag",Comment:"Comment",Reaction:"Reaction",Bookmark:"Bookmark",Follow:"Follow",Notification:"Notification",ArticleView:"ArticleView"};let I={generator:{name:"client",provider:{fromEnvVar:null,value:"prisma-client-js"},output:{value:"C:\\D\\Books\\Blog\\blogcms\\src\\generated\\prisma",fromEnvVar:null},config:{engineType:"library"},binaryTargets:[{fromEnvVar:null,value:"windows",native:!0}],previewFeatures:[],sourceFilePath:"C:\\D\\Books\\Blog\\blogcms\\prisma\\schema.prisma",isCustomOutput:!0},relativeEnvPaths:{rootEnvPath:null,schemaEnvPath:"../../../.env"},relativePath:"../../../prisma",clientVersion:"6.8.2",engineVersion:"2060c79ba17c6bb9f5823312b6f6b7f4a845738e",datasourceNames:["db"],activeProvider:"sqlite",inlineDatasources:{db:{url:{fromEnvVar:"DATABASE_URL",value:null}}},inlineSchema:'// BlogCMS - Comprehensive Database Schema\n// Built for scalability and performance\n\ngenerator client {\n  provider = "prisma-client-js"\n  output   = "../src/generated/prisma"\n}\n\ndatasource db {\n  provider = "sqlite"\n  url      = env("DATABASE_URL")\n}\n\n// User Management\nmodel User {\n  id       String   @id @default(cuid())\n  email    String   @unique\n  username String   @unique\n  password String\n  role     UserRole @default(READER)\n\n  // Profile Information\n  firstName String?\n  lastName  String?\n  bio       String?\n  avatarUrl String?\n  website   String?\n  location  String?\n\n  // Social Links\n  twitterUrl  String?\n  linkedinUrl String?\n  githubUrl   String?\n\n  // Settings\n  emailNotifications Boolean @default(true)\n  profilePublic      Boolean @default(true)\n\n  // Timestamps\n  createdAt   DateTime  @default(now())\n  updatedAt   DateTime  @updatedAt\n  lastLoginAt DateTime?\n\n  // Relations\n  articles      Article[]\n  comments      Comment[]\n  reactions     Reaction[]\n  bookmarks     Bookmark[]\n  followers     Follow[]       @relation("UserFollowers")\n  following     Follow[]       @relation("UserFollowing")\n  notifications Notification[]\n\n  // NextAuth.js relations\n  accounts Account[]\n  sessions Session[]\n\n  @@map("users")\n}\n\n// NextAuth.js Account model\nmodel Account {\n  id                String  @id @default(cuid())\n  userId            String\n  type              String\n  provider          String\n  providerAccountId String\n  refresh_token     String?\n  access_token      String?\n  expires_at        Int?\n  token_type        String?\n  scope             String?\n  id_token          String?\n  session_state     String?\n\n  user User @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@unique([provider, providerAccountId])\n  @@map("accounts")\n}\n\n// NextAuth.js Session model\nmodel Session {\n  id           String   @id @default(cuid())\n  sessionToken String   @unique\n  userId       String\n  expires      DateTime\n  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map("sessions")\n}\n\n// NextAuth.js VerificationToken model\nmodel VerificationToken {\n  identifier String\n  token      String   @unique\n  expires    DateTime\n\n  @@unique([identifier, token])\n  @@map("verification_tokens")\n}\n\n// Content Management\nmodel Article {\n  id       String  @id @default(cuid())\n  title    String\n  subtitle String?\n  slug     String  @unique\n  content  String\n  excerpt  String?\n\n  // Media\n  featuredImageUrl String?\n  featuredImageAlt String?\n\n  // Publishing\n  status      ArticleStatus @default(DRAFT)\n  publishedAt DateTime?\n  scheduledAt DateTime?\n\n  // Metrics\n  viewCount   Int @default(0)\n  readingTime Int @default(0) // in minutes\n\n  // SEO\n  metaDescription String?\n  ogTitle         String?\n  ogDescription   String?\n  ogImageUrl      String?\n\n  // Features\n  isFeatured    Boolean @default(false)\n  allowComments Boolean @default(true)\n\n  // Timestamps\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // Relations\n  authorId   String\n  author     User             @relation(fields: [authorId], references: [id], onDelete: Cascade)\n  categoryId String?\n  category   Category?        @relation(fields: [categoryId], references: [id])\n  tags       ArticleTag[]\n  comments   Comment[]\n  reactions  Reaction[]\n  bookmarks  Bookmark[]\n  versions   ArticleVersion[]\n\n  @@map("articles")\n}\n\n// Article Version History\nmodel ArticleVersion {\n  id        String   @id @default(cuid())\n  title     String\n  content   String\n  version   Int\n  createdAt DateTime @default(now())\n\n  articleId String\n  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)\n\n  @@unique([articleId, version])\n  @@map("article_versions")\n}\n\n// Categories\nmodel Category {\n  id          String  @id @default(cuid())\n  name        String  @unique\n  slug        String  @unique\n  description String?\n  color       String? // Hex color for UI\n\n  // Metrics\n  articleCount Int @default(0)\n\n  // Timestamps\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // Relations\n  articles Article[]\n\n  @@map("categories")\n}\n\n// Tags\nmodel Tag {\n  id          String  @id @default(cuid())\n  name        String  @unique\n  slug        String  @unique\n  description String?\n\n  // Metrics\n  usageCount Int @default(0)\n\n  // Timestamps\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // Relations\n  articles ArticleTag[]\n\n  @@map("tags")\n}\n\n// Many-to-many relationship between Articles and Tags\nmodel ArticleTag {\n  articleId String\n  tagId     String\n\n  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)\n  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)\n\n  @@id([articleId, tagId])\n  @@map("article_tags")\n}\n\n// Comments System\nmodel Comment {\n  id      String @id @default(cuid())\n  content String\n\n  // Hierarchy for nested comments\n  parentId String?\n  parent   Comment?  @relation("CommentReplies", fields: [parentId], references: [id])\n  replies  Comment[] @relation("CommentReplies")\n\n  // Timestamps\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // Relations\n  authorId  String\n  author    User    @relation(fields: [authorId], references: [id], onDelete: Cascade)\n  articleId String\n  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)\n\n  @@map("comments")\n}\n\n// Reactions (likes, claps, etc.)\nmodel Reaction {\n  id   String       @id @default(cuid())\n  type ReactionType\n\n  // Timestamps\n  createdAt DateTime @default(now())\n\n  // Relations\n  userId    String\n  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)\n  articleId String\n  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, articleId, type])\n  @@map("reactions")\n}\n\n// Bookmarks\nmodel Bookmark {\n  id String @id @default(cuid())\n\n  // Timestamps\n  createdAt DateTime @default(now())\n\n  // Relations\n  userId    String\n  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)\n  articleId String\n  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)\n\n  @@unique([userId, articleId])\n  @@map("bookmarks")\n}\n\n// Follow System\nmodel Follow {\n  id String @id @default(cuid())\n\n  // Timestamps\n  createdAt DateTime @default(now())\n\n  // Relations\n  followerId  String\n  follower    User   @relation("UserFollowing", fields: [followerId], references: [id], onDelete: Cascade)\n  followingId String\n  following   User   @relation("UserFollowers", fields: [followingId], references: [id], onDelete: Cascade)\n\n  @@unique([followerId, followingId])\n  @@map("follows")\n}\n\n// Notifications System\nmodel Notification {\n  id      String           @id @default(cuid())\n  type    NotificationType\n  title   String\n  message String\n  read    Boolean          @default(false)\n\n  // Optional data for different notification types\n  data Json?\n\n  // Timestamps\n  createdAt DateTime @default(now())\n\n  // Relations\n  userId String\n  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)\n\n  @@map("notifications")\n}\n\n// Analytics for articles\nmodel ArticleView {\n  id        String  @id @default(cuid())\n  ipAddress String?\n  userAgent String?\n  country   String?\n\n  // Timestamps\n  viewedAt DateTime @default(now())\n\n  // Relations\n  articleId String\n  userId    String? // null for anonymous views\n\n  @@map("article_views")\n}\n\n// Enums\nenum UserRole {\n  ADMIN\n  EDITOR\n  AUTHOR\n  READER\n}\n\nenum ArticleStatus {\n  DRAFT\n  REVIEW\n  PUBLISHED\n  ARCHIVED\n}\n\nenum ReactionType {\n  LIKE\n  CLAP\n  LOVE\n  DISLIKE\n}\n\nenum NotificationType {\n  NEW_FOLLOWER\n  NEW_COMMENT\n  NEW_REACTION\n  ARTICLE_PUBLISHED\n  MENTION\n  SYSTEM\n}\n\n// Indexes for performance\n// These will be added as separate migration files for better control\n',inlineSchemaHash:"3dd2e8083fa6615b7a47d97610e5bbec3bca5ef326db530bbe2202f9e25cc621",copyEngine:!0},x=i(29021);if(I.dirname=__dirname,!x.existsSync(S.join(__dirname,"schema.prisma"))){let e=["src/generated/prisma","generated/prisma"],t=e.find(e=>x.existsSync(S.join(process.cwd(),e,"schema.prisma")))??e[0];I.dirname=S.join(process.cwd(),t),I.isBundled=!0}I.runtimeDataModel=JSON.parse('{"models":{"User":{"dbName":"users","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"email","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"username","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"password","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"role","kind":"enum","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"UserRole","nativeType":null,"default":"READER","isGenerated":false,"isUpdatedAt":false},{"name":"firstName","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"lastName","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"bio","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"avatarUrl","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"website","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"location","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"twitterUrl","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"linkedinUrl","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"githubUrl","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"emailNotifications","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Boolean","nativeType":null,"default":true,"isGenerated":false,"isUpdatedAt":false},{"name":"profilePublic","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Boolean","nativeType":null,"default":true,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"lastLoginAt","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"articles","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Article","nativeType":null,"relationName":"ArticleToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"comments","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Comment","nativeType":null,"relationName":"CommentToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"reactions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Reaction","nativeType":null,"relationName":"ReactionToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"bookmarks","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Bookmark","nativeType":null,"relationName":"BookmarkToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"followers","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Follow","nativeType":null,"relationName":"UserFollowers","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"following","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Follow","nativeType":null,"relationName":"UserFollowing","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"notifications","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Notification","nativeType":null,"relationName":"NotificationToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"accounts","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Account","nativeType":null,"relationName":"AccountToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"sessions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Session","nativeType":null,"relationName":"SessionToUser","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Account":{"dbName":"accounts","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"type","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"provider","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"providerAccountId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"refresh_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"access_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"expires_at","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"token_type","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"scope","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"id_token","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"session_state","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"AccountToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["provider","providerAccountId"]],"uniqueIndexes":[{"name":null,"fields":["provider","providerAccountId"]}],"isGenerated":false},"Session":{"dbName":"sessions","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"sessionToken","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"expires","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"SessionToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"VerificationToken":{"dbName":"verification_tokens","schema":null,"fields":[{"name":"identifier","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"token","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"expires","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["identifier","token"]],"uniqueIndexes":[{"name":null,"fields":["identifier","token"]}],"isGenerated":false},"Article":{"dbName":"articles","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"title","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"subtitle","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"slug","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"content","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"excerpt","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"featuredImageUrl","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"featuredImageAlt","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"status","kind":"enum","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"ArticleStatus","nativeType":null,"default":"DRAFT","isGenerated":false,"isUpdatedAt":false},{"name":"publishedAt","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"scheduledAt","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"viewCount","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Int","nativeType":null,"default":0,"isGenerated":false,"isUpdatedAt":false},{"name":"readingTime","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Int","nativeType":null,"default":0,"isGenerated":false,"isUpdatedAt":false},{"name":"metaDescription","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"ogTitle","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"ogDescription","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"ogImageUrl","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"isFeatured","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Boolean","nativeType":null,"default":false,"isGenerated":false,"isUpdatedAt":false},{"name":"allowComments","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Boolean","nativeType":null,"default":true,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"authorId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"author","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"ArticleToUser","relationFromFields":["authorId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"categoryId","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"category","kind":"object","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Category","nativeType":null,"relationName":"ArticleToCategory","relationFromFields":["categoryId"],"relationToFields":["id"],"isGenerated":false,"isUpdatedAt":false},{"name":"tags","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"ArticleTag","nativeType":null,"relationName":"ArticleToArticleTag","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"comments","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Comment","nativeType":null,"relationName":"ArticleToComment","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"reactions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Reaction","nativeType":null,"relationName":"ArticleToReaction","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"bookmarks","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Bookmark","nativeType":null,"relationName":"ArticleToBookmark","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"versions","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"ArticleVersion","nativeType":null,"relationName":"ArticleToArticleVersion","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"ArticleVersion":{"dbName":"article_versions","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"title","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"content","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"version","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Int","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"articleId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"article","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Article","nativeType":null,"relationName":"ArticleToArticleVersion","relationFromFields":["articleId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["articleId","version"]],"uniqueIndexes":[{"name":null,"fields":["articleId","version"]}],"isGenerated":false},"Category":{"dbName":"categories","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"name","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"slug","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"description","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"color","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"articleCount","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Int","nativeType":null,"default":0,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"articles","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Article","nativeType":null,"relationName":"ArticleToCategory","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Tag":{"dbName":"tags","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"name","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"slug","kind":"scalar","isList":false,"isRequired":true,"isUnique":true,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"description","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"usageCount","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Int","nativeType":null,"default":0,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"articles","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"ArticleTag","nativeType":null,"relationName":"ArticleTagToTag","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"ArticleTag":{"dbName":"article_tags","schema":null,"fields":[{"name":"articleId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"tagId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"article","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Article","nativeType":null,"relationName":"ArticleToArticleTag","relationFromFields":["articleId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"tag","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Tag","nativeType":null,"relationName":"ArticleTagToTag","relationFromFields":["tagId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":{"name":null,"fields":["articleId","tagId"]},"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Comment":{"dbName":"comments","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"content","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"parentId","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"parent","kind":"object","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Comment","nativeType":null,"relationName":"CommentReplies","relationFromFields":["parentId"],"relationToFields":["id"],"isGenerated":false,"isUpdatedAt":false},{"name":"replies","kind":"object","isList":true,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Comment","nativeType":null,"relationName":"CommentReplies","relationFromFields":[],"relationToFields":[],"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"updatedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"DateTime","nativeType":null,"isGenerated":false,"isUpdatedAt":true},{"name":"authorId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"author","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"CommentToUser","relationFromFields":["authorId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"articleId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"article","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Article","nativeType":null,"relationName":"ArticleToComment","relationFromFields":["articleId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"Reaction":{"dbName":"reactions","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"type","kind":"enum","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"ReactionType","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"ReactionToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"articleId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"article","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Article","nativeType":null,"relationName":"ArticleToReaction","relationFromFields":["articleId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["userId","articleId","type"]],"uniqueIndexes":[{"name":null,"fields":["userId","articleId","type"]}],"isGenerated":false},"Bookmark":{"dbName":"bookmarks","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"BookmarkToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"articleId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"article","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Article","nativeType":null,"relationName":"ArticleToBookmark","relationFromFields":["articleId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["userId","articleId"]],"uniqueIndexes":[{"name":null,"fields":["userId","articleId"]}],"isGenerated":false},"Follow":{"dbName":"follows","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"followerId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"follower","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"UserFollowing","relationFromFields":["followerId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false},{"name":"followingId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"following","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"UserFollowers","relationFromFields":["followingId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[["followerId","followingId"]],"uniqueIndexes":[{"name":null,"fields":["followerId","followingId"]}],"isGenerated":false},"Notification":{"dbName":"notifications","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"type","kind":"enum","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"NotificationType","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"title","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"message","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"read","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"Boolean","nativeType":null,"default":false,"isGenerated":false,"isUpdatedAt":false},{"name":"data","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"Json","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"createdAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":true,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"user","kind":"object","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"User","nativeType":null,"relationName":"NotificationToUser","relationFromFields":["userId"],"relationToFields":["id"],"relationOnDelete":"Cascade","isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false},"ArticleView":{"dbName":"article_views","schema":null,"fields":[{"name":"id","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":true,"isReadOnly":false,"hasDefaultValue":true,"type":"String","nativeType":null,"default":{"name":"cuid","args":[1]},"isGenerated":false,"isUpdatedAt":false},{"name":"ipAddress","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"userAgent","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"country","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"viewedAt","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":true,"type":"DateTime","nativeType":null,"default":{"name":"now","args":[]},"isGenerated":false,"isUpdatedAt":false},{"name":"articleId","kind":"scalar","isList":false,"isRequired":true,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false},{"name":"userId","kind":"scalar","isList":false,"isRequired":false,"isUnique":false,"isId":false,"isReadOnly":false,"hasDefaultValue":false,"type":"String","nativeType":null,"isGenerated":false,"isUpdatedAt":false}],"primaryKey":null,"uniqueFields":[],"uniqueIndexes":[],"isGenerated":false}},"enums":{"UserRole":{"values":[{"name":"ADMIN","dbName":null},{"name":"EDITOR","dbName":null},{"name":"AUTHOR","dbName":null},{"name":"READER","dbName":null}],"dbName":null},"ArticleStatus":{"values":[{"name":"DRAFT","dbName":null},{"name":"REVIEW","dbName":null},{"name":"PUBLISHED","dbName":null},{"name":"ARCHIVED","dbName":null}],"dbName":null},"ReactionType":{"values":[{"name":"LIKE","dbName":null},{"name":"CLAP","dbName":null},{"name":"LOVE","dbName":null},{"name":"DISLIKE","dbName":null}],"dbName":null},"NotificationType":{"values":[{"name":"NEW_FOLLOWER","dbName":null},{"name":"NEW_COMMENT","dbName":null},{"name":"NEW_REACTION","dbName":null},{"name":"ARTICLE_PUBLISHED","dbName":null},{"name":"MENTION","dbName":null},{"name":"SYSTEM","dbName":null}],"dbName":null}},"types":{}}'),b(t.Prisma,I.runtimeDataModel),I.engineWasm=void 0,I.compilerWasm=void 0;let{warnEnvConflicts:P}=i(23604);P({rootEnvPath:I.relativeEnvPaths.rootEnvPath&&S.resolve(I.dirname,I.relativeEnvPaths.rootEnvPath),schemaEnvPath:I.relativeEnvPaths.schemaEnvPath&&S.resolve(I.dirname,I.relativeEnvPaths.schemaEnvPath)}),t.PrismaClient=o(I),Object.assign(t,T),S.join(__dirname,"query_engine-windows.dll.node"),S.join(process.cwd(),"src/generated/prisma/query_engine-windows.dll.node"),S.join(__dirname,"schema.prisma"),S.join(process.cwd(),"src/generated/prisma/schema.prisma")},78335:()=>{},96487:()=>{}};