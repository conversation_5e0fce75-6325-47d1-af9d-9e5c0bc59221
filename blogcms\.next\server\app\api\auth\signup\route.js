"use strict";(()=>{var e={};e.id=887,e.ids=[887],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33190:(e,r,s)=>{s.r(r),s.d(r,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>c});var t={};s.r(t),s.d(t,{POST:()=>d});var a=s(96559),o=s(48088),i=s(37719),n=s(32190),u=s(31183),p=s(85663);async function d(e){try{let{firstName:r,lastName:s,username:t,email:a,password:o}=await e.json(),i={};if(r?.trim()||(i.firstName="First name is required"),s?.trim()||(i.lastName="Last name is required"),t?.trim()?t.length<3?i.username="Username must be at least 3 characters":/^[a-zA-Z0-9_]+$/.test(t)||(i.username="Username can only contain letters, numbers, and underscores"):i.username="Username is required",a?.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(a)||(i.email="Please enter a valid email address"):i.email="Email is required",o?o.length<6&&(i.password="Password must be at least 6 characters"):i.password="Password is required",Object.keys(i).length>0)return n.NextResponse.json({errors:i},{status:400});let d=await u.z.user.findFirst({where:{OR:[{email:a.toLowerCase()},{username:t.toLowerCase()}]}});if(d)return d.email===a.toLowerCase()&&(i.email="An account with this email already exists"),d.username===t.toLowerCase()&&(i.username="This username is already taken"),n.NextResponse.json({errors:i},{status:400});let l=await p.Ay.hash(o,12),m=await u.z.user.create({data:{firstName:r.trim(),lastName:s.trim(),username:t.toLowerCase().trim(),email:a.toLowerCase().trim(),password:l,role:"AUTHOR",profilePublic:!0,emailNotifications:!0},select:{id:!0,firstName:!0,lastName:!0,username:!0,email:!0,role:!0,createdAt:!0}});return n.NextResponse.json({message:"Account created successfully",user:m},{status:201})}catch(e){return console.error("Signup error:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\auth\\signup\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:c,serverHooks:x}=l;function h(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:c})}},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},55511:e=>{e.exports=require("crypto")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,580,663,275],()=>s(33190));module.exports=t})();