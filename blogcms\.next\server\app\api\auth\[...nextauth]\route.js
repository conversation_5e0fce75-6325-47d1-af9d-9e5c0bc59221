"use strict";(()=>{var e={};e.id=14,e.ids=[14],e.modules={1516:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>c,routeModule:()=>l,serverHooks:()=>q,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>d});var a=t(96559),o=t(48088),i=t(37719),u=t(19854),n=t.n(u),p=t(12909);let d=n()(p.N),l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:q}=l;function c(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},12909:(e,r,t)=>{t.d(r,{N:()=>u});var s=t(13581),a=t(60890),o=t(31183),i=t(85663);let u={adapter:(0,a.y)(o.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await o.z.user.findUnique({where:{email:e.email}});return r&&await i.Ay.compare(e.password,r.password)?(await o.z.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,username:r.username,role:r.role,firstName:r.firstName,lastName:r.lastName,avatarUrl:r.avatarUrl}):null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.username=r.username),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.username=r.username),e)},pages:{signIn:"/auth/signin"}}},16698:e=>{e.exports=require("node:async_hooks")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},74075:e=>{e.exports=require("zlib")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96559:(e,r,t)=>{e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,663,819,275],()=>t(1516));module.exports=s})();