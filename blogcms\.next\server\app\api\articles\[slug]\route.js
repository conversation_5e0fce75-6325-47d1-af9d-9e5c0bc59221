"use strict";(()=>{var e={};e.id=797,e.ids=[797],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{function s(e){return e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function a(e){return Math.ceil(e.trim().split(/\s+/).length/200)}function o(e,r=160){let t=e.replace(/<[^>]*>/g,"").replace(/[#*_`]/g,"").trim();return t.length<=r?t:t.substring(0,r).trim()+"..."}function i(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function n(e){let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),{isValid:0===r.length,errors:r}}t.d(r,{DT:()=>i,Oj:()=>n,_C:()=>a,dn:()=>o,z9:()=>s})},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},12909:(e,r,t)=>{t.d(r,{N:()=>n});var s=t(13581),a=t(60890),o=t(31183),i=t(85663);let n={adapter:(0,a.y)(o.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await o.z.user.findUnique({where:{email:e.email}});return r&&await i.Ay.compare(e.password,r.password)?(await o.z.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,username:r.username,role:r.role,firstName:r.firstName,lastName:r.lastName,avatarUrl:r.avatarUrl}):null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.username=r.username),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.username=r.username),e)},pages:{signIn:"/auth/signin"}}},16698:e=>{e.exports=require("node:async_hooks")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},37731:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{DELETE:()=>g,GET:()=>p,PUT:()=>m});var a=t(96559),o=t(48088),i=t(37719),n=t(32190),u=t(19854),l=t(12909),d=t(31183),c=t(10974);async function p(e,{params:r}){let{slug:t}=await r;try{let e=await d.z.article.findUnique({where:{slug:t},include:{author:{select:{id:!0,username:!0,firstName:!0,lastName:!0,bio:!0,avatarUrl:!0,_count:{select:{articles:!0,followers:!0}}}},category:!0,tags:{include:{tag:!0}},comments:{where:{parentId:null},include:{author:{select:{id:!0,username:!0,firstName:!0,lastName:!0,avatarUrl:!0}},replies:{include:{author:{select:{id:!0,username:!0,firstName:!0,lastName:!0,avatarUrl:!0}}}}},orderBy:{createdAt:"desc"}},_count:{select:{comments:!0,reactions:!0,bookmarks:!0}}}});if(!e)return n.NextResponse.json({error:"Article not found"},{status:404});return await d.z.article.update({where:{id:e.id},data:{viewCount:{increment:1}}}),n.NextResponse.json(e)}catch(e){return console.error("Error fetching article:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){let{slug:t}=await r;try{let r=await (0,u.getServerSession)(l.N);if(!r?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let s=await d.z.article.findUnique({where:{slug:t},select:{id:!0,authorId:!0,status:!0,title:!0}});if(!s)return n.NextResponse.json({error:"Article not found"},{status:404});if(s.authorId!==r.user.id&&"ADMIN"!==r.user.role&&"EDITOR"!==r.user.role)return n.NextResponse.json({error:"Forbidden"},{status:403});let{title:a,subtitle:o,content:i,categoryId:p,featuredImageUrl:m,featuredImageAlt:g,status:x,scheduledAt:w,metaDescription:h,ogTitle:f,ogDescription:v,ogImageUrl:q,allowComments:N}=await e.json(),y={};if(void 0!==a&&(y.title=a,a!==s.title)){let e=(0,c.z9)(a),r=e,t=1;for(;await d.z.article.findFirst({where:{slug:r,NOT:{id:s.id}}});)r=`${e}-${t}`,t++;y.slug=r}void 0!==i&&(y.content=i,y.readingTime=(0,c._C)(i),y.excerpt=(0,c.dn)(i)),void 0!==o&&(y.subtitle=o),void 0!==p&&(y.categoryId=p),void 0!==m&&(y.featuredImageUrl=m),void 0!==g&&(y.featuredImageAlt=g),void 0!==h&&(y.metaDescription=h),void 0!==f&&(y.ogTitle=f),void 0!==v&&(y.ogDescription=v),void 0!==q&&(y.ogImageUrl=q),void 0!==N&&(y.allowComments=N),void 0!==x&&(y.status=x,"PUBLISHED"===x&&"PUBLISHED"!==s.status&&(y.publishedAt=new Date)),void 0!==w&&(y.scheduledAt=w?new Date(w):null);let j=await d.z.article.update({where:{id:s.id},data:y,include:{author:{select:{id:!0,username:!0,firstName:!0,lastName:!0,avatarUrl:!0}},category:!0,tags:{include:{tag:!0}}}});return n.NextResponse.json(j)}catch(e){return console.error("Error updating article:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e,{params:r}){let{slug:t}=await r;try{let e=await (0,u.getServerSession)(l.N);if(!e?.user)return n.NextResponse.json({error:"Unauthorized"},{status:401});let r=await d.z.article.findUnique({where:{slug:t},select:{id:!0,authorId:!0}});if(!r)return n.NextResponse.json({error:"Article not found"},{status:404});if(r.authorId!==e.user.id&&"ADMIN"!==e.user.role)return n.NextResponse.json({error:"Forbidden"},{status:403});return await d.z.article.delete({where:{id:r.id}}),n.NextResponse.json({message:"Article deleted successfully"})}catch(e){return console.error("Error deleting article:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/articles/[slug]/route",pathname:"/api/articles/[slug]",filename:"route",bundlePath:"app/api/articles/[slug]/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\articles\\[slug]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:h,serverHooks:f}=x;function v(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:h})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},74075:e=>{e.exports=require("zlib")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,663,819,275],()=>t(37731));module.exports=s})();