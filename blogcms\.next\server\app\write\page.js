(()=>{var e={};e.id=426,e.ids=[426],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10478:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=a(65239),r=a(48088),l=a(88170),i=a.n(l),n=a(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);a.d(t,o);let d={children:["",{children:["write",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,73992)),"C:\\D\\Books\\Blog\\blogcms\\src\\app\\write\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,80871)),"C:\\D\\Books\\Blog\\blogcms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\D\\Books\\Blog\\blogcms\\src\\app\\write\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/write/page",pathname:"/write",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35111:(e,t,a)=>{Promise.resolve().then(a.bind(a,47013))},47013:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(60687),r=a(43210),l=a(82136),i=a(16189),n=a(62904),o=a(51934),d=a(9005),c=a(93613),m=a(16023),u=a(11860),p=a(5336);function g({imageUrl:e,altText:t,onImageChange:a,onAltTextChange:l}){let[i,n]=(0,r.useState)(!1),[g,x]=(0,r.useState)(""),[h,b]=(0,r.useState)(!1),f=e=>e.type.startsWith("image/")?e.size>0xa00000?{valid:!1,error:"Image size too large. Maximum size is 10MB."}:["image/png","image/jpeg","image/jpg","image/gif","image/webp","image/svg+xml"].includes(e.type)?{valid:!0}:{valid:!1,error:"Unsupported image format. Please use PNG, JPG, JPEG, GIF, WebP, or SVG."}:{valid:!1,error:"Please upload an image file (PNG, JPG, JPEG, GIF, WebP, SVG)."},y=(0,r.useCallback)(async e=>{x("");let t=f(e);if(!t.valid)return void x(t.error||"Invalid file");n(!0);try{let t=new FileReader;t.onload=()=>{let e=t.result;a(e),n(!1)},t.onerror=()=>{x("Failed to process image. Please try again."),n(!1)},t.readAsDataURL(e)}catch(e){console.error("Upload failed:",e),x("Failed to process image. Please try again."),n(!1)}},[a]),v=(0,r.useCallback)(e=>{let t=e[0];t&&y(t),b(!1)},[y]),{getRootProps:j,getInputProps:N,isDragActive:w}=(0,o.VB)({onDrop:v,accept:{"image/*":[".png",".jpg",".jpeg",".gif",".webp",".svg"]},maxFiles:1,onDragEnter:()=>b(!0),onDragLeave:()=>b(!1)});return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 inline mr-1"}),"Featured Image"]}),g&&(0,s.jsxs)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-red-500 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-red-700 text-sm",children:g})]}),e?(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("img",{src:e,alt:t||"Featured image preview",className:"w-full h-48 object-cover rounded-lg border",onError:()=>{x("Failed to load image. Please check the URL or try uploading again."),a("")}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all rounded-lg flex items-center justify-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>{a(""),x("")},className:"opacity-0 group-hover:opacity-100 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-all",title:"Remove image",children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})}),i&&(0,s.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Processing image..."})]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-green-600",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:"Image uploaded successfully"})]})]}):(0,s.jsxs)("div",{...j(),className:`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${w||h?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"}`,children:[(0,s.jsx)("input",{...N()}),(0,s.jsx)(m.A,{className:"h-8 w-8 mx-auto mb-3 text-gray-400"}),(0,s.jsx)("p",{className:"text-gray-600 mb-2",children:w?"Drop the image here...":"Drag & drop an image here, or click to select"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Supports: PNG, JPG, JPEG, GIF, WebP, SVG"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum size: 10MB"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Text (for accessibility)"}),(0,s.jsx)("input",{type:"text",placeholder:"Describe the image for screen readers...",value:t,onChange:e=>l(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Provide a brief description of the image for accessibility and SEO"})]})]})}var x=a(37360),h=a(62688);let b=(0,h.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);function f({selectedTags:e,availableTags:t,onTagsChange:a}){let[l,i]=(0,r.useState)(""),[n,o]=(0,r.useState)(!1),[d,c]=(0,r.useState)([]),[m,p]=(0,r.useState)(-1),g=(0,r.useRef)(null),h=(0,r.useRef)(null),f=e.map(e=>t.find(t=>t.id===e)).filter(Boolean),y=t=>{e.includes(t.id)||a([...e,t.id]),i(""),o(!1),p(-1),g.current?.focus()},v=async t=>{try{let s=await fetch("/api/tags",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:t})});if(s.ok){let t=await s.json();a([...e,t.id]),i(""),o(!1),g.current?.focus()}else{let e=await s.json();console.error("Failed to create tag:",e.error),i("")}}catch(e){console.error("Error creating tag:",e),i("")}},j=t=>{a(e.filter(e=>e!==t))};return(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 inline mr-1"}),"Tags"]}),f.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:f.map(e=>(0,s.jsxs)("span",{className:"inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full border border-blue-200",children:[e.name,(0,s.jsx)("button",{type:"button",onClick:()=>j(e.id),className:"hover:bg-blue-200 rounded-full p-0.5 transition-colors",title:`Remove ${e.name} tag`,children:(0,s.jsx)(u.A,{className:"h-3 w-3"})})]},e.id))}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)("input",{ref:g,type:"text",value:l,onChange:e=>{i(e.target.value)},onKeyDown:e=>{"ArrowDown"===e.key?(e.preventDefault(),p(e=>e<d.length-1?e+1:e)):"ArrowUp"===e.key?(e.preventDefault(),p(e=>e>0?e-1:-1)):"Enter"===e.key?(e.preventDefault(),m>=0&&d[m]?y(d[m]):l.trim()&&0===d.length&&v(l.trim())):"Escape"===e.key?(o(!1),p(-1)):"Tab"===e.key&&m>=0&&(e.preventDefault(),y(d[m]))},onFocus:()=>{l.length>=2&&d.length>0&&o(!0)},onBlur:()=>{setTimeout(()=>{o(!1),p(-1)},200)},placeholder:"Type to search tags or create new ones...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),n&&(0,s.jsxs)("div",{ref:h,className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto",children:[d.map((e,t)=>(0,s.jsx)("button",{type:"button",onClick:()=>y(e),className:`w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors ${t===m?"bg-blue-50 text-blue-700":""}`,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:e.name}),(0,s.jsx)(x.A,{className:"h-3 w-3 text-gray-400"})]})},e.id)),l.trim()&&0===d.length&&(0,s.jsx)("button",{type:"button",onClick:()=>v(l.trim()),className:"w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors border-t border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-green-600",children:[(0,s.jsx)(b,{className:"h-3 w-3"}),(0,s.jsxs)("span",{children:['Create "',l.trim(),'"']})]})})]})]})}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Type at least 2 characters to see suggestions. Press Enter to create new tags."})]})]})}var y=a(48730);let v=(0,h.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),j=(0,h.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),N=(0,h.A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);function w(){let{data:e,status:t}=(0,l.useSession)(),a=(0,i.useRouter)(),[o,d]=(0,r.useState)({title:"",subtitle:"",content:"",categoryId:"",tags:[],featuredImageUrl:"",featuredImageAlt:"",status:"DRAFT",metaDescription:"",allowComments:!0}),[c,m]=(0,r.useState)([]),[u,p]=(0,r.useState)([]),[x,h]=(0,r.useState)(!1),[b,w]=(0,r.useState)(!1),[k,P]=(0,r.useState)(null),C=async(t="DRAFT")=>{if(e&&o.title.trim()){w(!0);try{let e=await fetch("/api/articles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...o,status:t})});if(e.ok){let s=await e.json();P(new Date),"PUBLISHED"===t&&a.push(`/articles/${s.slug}`)}else{let t=await e.json();alert(t.error||"Failed to save article")}}catch(e){console.error("Error saving article:",e),alert("Failed to save article")}finally{w(!1)}}};return"loading"===t?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto py-8 px-4",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Write Article"}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[k&&(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 inline mr-1"}),"Saved ",k.toLocaleTimeString()]}),(0,s.jsxs)("button",{onClick:()=>C("DRAFT"),disabled:b,className:"flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50",children:[(0,s.jsx)(v,{className:"h-4 w-4"}),b?"Saving...":"Save Draft"]}),(0,s.jsxs)("button",{onClick:()=>C("PUBLISHED"),disabled:b||!o.title.trim(),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:[(0,s.jsx)(j,{className:"h-4 w-4"}),"Publish"]})]})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)("input",{type:"text",placeholder:"Article title...",value:o.title,onChange:e=>d({...o,title:e.target.value}),className:"w-full text-3xl font-bold border-none outline-none placeholder-gray-400 resize-none"})}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("input",{type:"text",placeholder:"Add a subtitle (optional)...",value:o.subtitle,onChange:e=>d({...o,subtitle:e.target.value}),className:"w-full text-xl text-gray-600 border-none outline-none placeholder-gray-400"})}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(g,{imageUrl:o.featuredImageUrl,altText:o.featuredImageAlt,onImageChange:e=>d({...o,featuredImageUrl:e}),onAltTextChange:e=>d({...o,featuredImageAlt:e})})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,s.jsx)(N,{className:"h-4 w-4 inline mr-1"}),"Category"]}),(0,s.jsxs)("select",{value:o.categoryId,onChange:e=>d({...o,categoryId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Select a category"}),c.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(f,{selectedTags:o.tags,availableTags:u,onTagsChange:e=>d({...o,tags:e})})})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Meta Description (SEO)"}),(0,s.jsx)("textarea",{placeholder:"Brief description for search engines...",value:o.metaDescription,onChange:e=>d({...o,metaDescription:e.target.value}),rows:2,maxLength:160,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[o.metaDescription.length,"/160 characters"]})]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border overflow-hidden",children:(0,s.jsx)(n.d,{content:o.content,onChange:e=>d({...o,content:e}),placeholder:"Tell your story..."})})]})}):null}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73992:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\write\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Books\\Blog\\blogcms\\src\\app\\write\\page.tsx","default")},75279:(e,t,a)=>{Promise.resolve().then(a.bind(a,73992))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[243,789,658,92,575,351],()=>a(10478));module.exports=s})();