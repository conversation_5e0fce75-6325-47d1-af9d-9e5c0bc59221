"use strict";(()=>{var e={};e.id=549,e.ids=[549],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16698:e=>{e.exports=require("node:async_hooks")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},87901:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>c,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>u,POST:()=>d});var o=t(96559),a=t(48088),n=t(37719),i=t(32190),p=t(31183);async function u(){try{let e=await p.z.tag.findMany({orderBy:{name:"asc"},include:{_count:{select:{articles:!0}}}});return i.NextResponse.json(e)}catch(e){return console.error("Error fetching tags:",e),i.NextResponse.json({error:"Failed to fetch tags"},{status:500})}}async function d(e){try{let{name:r}=await e.json();if(!r)return i.NextResponse.json({error:"Tag name is required"},{status:400});let t=r.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"");if(await p.z.tag.findFirst({where:{OR:[{name:r},{slug:t}]}}))return i.NextResponse.json({error:"Tag with this name already exists"},{status:400});let s=await p.z.tag.create({data:{name:r,slug:t}});return i.NextResponse.json(s,{status:201})}catch(e){return console.error("Error creating tag:",e),i.NextResponse.json({error:"Failed to create tag"},{status:500})}}let c=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tags/route",pathname:"/api/tags",filename:"route",bundlePath:"app/api/tags/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\tags\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:g}=c;function q(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,275],()=>t(87901));module.exports=s})();