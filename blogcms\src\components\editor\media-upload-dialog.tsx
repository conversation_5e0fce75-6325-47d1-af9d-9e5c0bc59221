'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { X, Upload, Link, Image, Video, FileText, AlertCircle, CheckCircle } from 'lucide-react'

interface MediaUploadDialogProps {
  isOpen: boolean
  onClose: () => void
  onInsert: (url: string, type: 'image' | 'video' | 'pdf' | 'audio') => void
}

export function MediaUploadDialog({ isOpen, onClose, onInsert }: MediaUploadDialogProps) {
  const [activeTab, setActiveTab] = useState<'upload' | 'url' | 'embed'>('upload')
  const [url, setUrl] = useState('')
  const [uploading, setUploading] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<Array<{file: File, preview: string, type: string}>>([])
  const [error, setError] = useState('')
  const [embedCode, setEmbedCode] = useState('')

  // File size limits (in bytes)
  const MAX_FILE_SIZE = {
    image: 10 * 1024 * 1024, // 10MB
    video: 100 * 1024 * 1024, // 100MB
    pdf: 25 * 1024 * 1024, // 25MB
    audio: 50 * 1024 * 1024, // 50MB
  }

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    // Check file type
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')
    const isPdf = file.type === 'application/pdf'
    const isAudio = file.type.startsWith('audio/')

    if (!isImage && !isVideo && !isPdf && !isAudio) {
      return { valid: false, error: 'Unsupported file type. Please upload images, videos, PDFs, or audio files.' }
    }

    // Check file size
    let maxSize = MAX_FILE_SIZE.image
    if (isVideo) maxSize = MAX_FILE_SIZE.video
    else if (isPdf) maxSize = MAX_FILE_SIZE.pdf
    else if (isAudio) maxSize = MAX_FILE_SIZE.audio

    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024))
      return { valid: false, error: `File size too large. Maximum size is ${maxSizeMB}MB.` }
    }

    return { valid: true }
  }

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setError('')
    const file = acceptedFiles[0]
    if (!file) return

    const validation = validateFile(file)
    if (!validation.valid) {
      setError(validation.error || 'Invalid file')
      return
    }

    setUploading(true)
    try {
      // In a real app, you would upload to your storage service (Cloudinary, AWS S3, etc.)
      // For now, we'll create a base64 URL for demonstration
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        let type: 'image' | 'video' | 'pdf' | 'audio' = 'image'

        if (file.type.startsWith('video/')) {
          type = 'video'
        } else if (file.type === 'application/pdf') {
          type = 'pdf'
        } else if (file.type.startsWith('audio/')) {
          type = 'audio'
        }

        // Add to uploaded files for preview
        setUploadedFiles([{ file, preview: result, type }])
      }
      reader.readAsDataURL(file)
    } catch (error) {
      console.error('Upload failed:', error)
      setError('Failed to process file. Please try again.')
    } finally {
      setUploading(false)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'],
      'video/*': ['.mp4', '.webm', '.ogg'],
      'audio/*': ['.mp3', '.wav', '.ogg', '.m4a'],
      'application/pdf': ['.pdf'],
    },
    maxFiles: 1,
  })

  // Enhanced URL parsing for various video platforms
  const parseVideoUrl = (url: string): { embedUrl: string; type: 'youtube' | 'vimeo' | 'dailymotion' | 'other' } | null => {
    // YouTube URL patterns
    const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const youtubeMatch = url.match(youtubeRegex)
    if (youtubeMatch) {
      return {
        embedUrl: `https://www.youtube.com/embed/${youtubeMatch[1]}`,
        type: 'youtube'
      }
    }

    // Vimeo URL patterns
    const vimeoRegex = /(?:vimeo\.com\/)(?:.*\/)?(\d+)/
    const vimeoMatch = url.match(vimeoRegex)
    if (vimeoMatch) {
      return {
        embedUrl: `https://player.vimeo.com/video/${vimeoMatch[1]}`,
        type: 'vimeo'
      }
    }

    // Dailymotion URL patterns
    const dailymotionRegex = /(?:dailymotion\.com\/video\/|dai\.ly\/)([a-zA-Z0-9]+)/
    const dailymotionMatch = url.match(dailymotionRegex)
    if (dailymotionMatch) {
      return {
        embedUrl: `https://www.dailymotion.com/embed/video/${dailymotionMatch[1]}`,
        type: 'dailymotion'
      }
    }

    return null
  }

  const handleUrlInsert = () => {
    if (!url.trim()) return
    setError('')

    let type: 'image' | 'video' | 'pdf' | 'audio' = 'image'
    let finalUrl = url.trim()

    // Check if it's a video URL that needs parsing
    const videoData = parseVideoUrl(finalUrl)
    if (videoData) {
      type = 'video'
      finalUrl = videoData.embedUrl
    } else if (finalUrl.includes('vimeo.com') || finalUrl.includes('youtube.com') || finalUrl.includes('youtu.be') || finalUrl.includes('dailymotion.com')) {
      type = 'video'
    } else if (finalUrl.endsWith('.pdf')) {
      type = 'pdf'
    } else if (finalUrl.match(/\.(mp3|wav|ogg|m4a)$/i)) {
      type = 'audio'
    } else if (finalUrl.match(/\.(mp4|webm|ogg|avi|mov)$/i)) {
      type = 'video'
    }

    onInsert(finalUrl, type)
    setUrl('')
    onClose()
  }

  const handleEmbedInsert = () => {
    if (!embedCode.trim()) return
    setError('')

    // For embed codes, we'll treat them as video by default
    // In a real implementation, you might want to parse the embed code to determine type
    onInsert(embedCode.trim(), 'video')
    setEmbedCode('')
    onClose()
  }

  const handleFileInsert = (fileData: {file: File, preview: string, type: string}) => {
    onInsert(fileData.preview, fileData.type as 'image' | 'video' | 'pdf' | 'audio')
    setUploadedFiles([])
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Insert Media</h3>
          <button
            type="button"
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded"
            title="Close dialog"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}

        {/* Tabs */}
        <div className="flex border-b mb-4">
          <button
            type="button"
            onClick={() => setActiveTab('upload')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'upload'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Upload className="h-4 w-4 inline mr-2" />
            Upload
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('url')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'url'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Link className="h-4 w-4 inline mr-2" />
            URL
          </button>
          <button
            type="button"
            onClick={() => setActiveTab('embed')}
            className={`px-4 py-2 font-medium ${
              activeTab === 'embed'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            <Video className="h-4 w-4 inline mr-2" />
            Embed
          </button>
        </div>

        {activeTab === 'upload' && (
          <div>
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-blue-400 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input {...getInputProps()} />
              <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600 mb-2">
                {isDragActive
                  ? 'Drop the file here...'
                  : 'Drag & drop a file here, or click to select'}
              </p>
              <p className="text-sm text-gray-500">
                Supports: Images (PNG, JPG, GIF, WebP, SVG), Videos (MP4, WebM), Audio (MP3, WAV), PDFs
              </p>
              <p className="text-xs text-gray-400 mt-1">
                Max sizes: Images 10MB, Videos 100MB, Audio 50MB, PDFs 25MB
              </p>
            </div>

            {uploading && (
              <div className="mt-4 text-center">
                <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <p className="mt-2 text-sm text-gray-600">Processing file...</p>
              </div>
            )}

            {/* File Preview */}
            {uploadedFiles.length > 0 && (
              <div className="mt-4 space-y-3">
                <h4 className="font-medium text-gray-700">Preview:</h4>
                {uploadedFiles.map((fileData, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm font-medium">{fileData.file.name}</span>
                        <span className="text-xs text-gray-500">
                          ({Math.round(fileData.file.size / 1024)}KB)
                        </span>
                      </div>
                    </div>

                    {fileData.type === 'image' && (
                      <div className="mb-3">
                        <img
                          src={fileData.preview}
                          alt="Preview"
                          className="max-w-full h-32 object-contain rounded border"
                        />
                      </div>
                    )}

                    {fileData.type === 'video' && (
                      <div className="mb-3">
                        <video
                          src={fileData.preview}
                          className="max-w-full h-32 rounded border"
                          controls
                        />
                      </div>
                    )}

                    {fileData.type === 'audio' && (
                      <div className="mb-3">
                        <audio
                          src={fileData.preview}
                          className="w-full"
                          controls
                        />
                      </div>
                    )}

                    <button
                      type="button"
                      onClick={() => handleFileInsert(fileData)}
                      className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
                    >
                      Insert {fileData.type}
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'url' && (
          <div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Media URL
                </label>
                <input
                  type="url"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div className="text-sm text-gray-600">
                <p className="font-medium mb-2">Supported URLs:</p>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Image className="h-4 w-4" />
                    <span>Direct image links (.jpg, .png, .gif, etc.)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Video className="h-4 w-4" />
                    <span>YouTube, Vimeo, Dailymotion, or direct video links</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <span>PDF documents and audio files</span>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  <p>Examples:</p>
                  <p>• https://youtube.com/watch?v=dQw4w9WgXcQ</p>
                  <p>• https://youtu.be/dQw4w9WgXcQ</p>
                  <p>• https://vimeo.com/123456789</p>
                  <p>• https://example.com/image.jpg</p>
                </div>
              </div>

              <button
                type="button"
                onClick={handleUrlInsert}
                disabled={!url.trim()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Insert Media
              </button>
            </div>
          </div>
        )}

        {activeTab === 'embed' && (
          <div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Embed Code
                </label>
                <textarea
                  value={embedCode}
                  onChange={(e) => setEmbedCode(e.target.value)}
                  placeholder='<iframe src="https://www.youtube.com/embed/..." width="560" height="315" frameborder="0"></iframe>'
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                />
              </div>

              <div className="text-sm text-gray-600">
                <p className="font-medium mb-2">Supported embed codes:</p>
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <Video className="h-4 w-4" />
                    <span>YouTube, Vimeo, Dailymotion iframes</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Video className="h-4 w-4" />
                    <span>Social media embeds (Twitter, Instagram)</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Video className="h-4 w-4" />
                    <span>Other iframe-based embeds</span>
                  </div>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  <p>Paste the complete embed code from the platform's share/embed option.</p>
                </div>
              </div>

              <button
                type="button"
                onClick={handleEmbedInsert}
                disabled={!embedCode.trim()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Insert Embed
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
