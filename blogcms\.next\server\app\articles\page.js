(()=>{var e={};e.id=292,e.ids=[292],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,s,r)=>{"use strict";r.d(s,{cn:()=>l});var t=r(49384),a=r(82348);function l(...e){return(0,a.QP)((0,t.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24596:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\articles\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Books\\Blog\\blogcms\\src\\app\\articles\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32573:(e,s,r)=>{Promise.resolve().then(r.bind(r,34779))},33872:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},34779:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var t=r(60687),a=r(43210),l=r(85814),i=r.n(l),n=r(99270);let o=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]);var d=r(40228),c=r(58869),m=r(13861),u=r(33872),p=r(67760),x=r(44493),h=r(89667);function f(){let[e,s]=(0,a.useState)([]),[r,l]=(0,a.useState)([]),[f,g]=(0,a.useState)(!0),[v,b]=(0,a.useState)(""),[j,N]=(0,a.useState)(""),[y,w]=(0,a.useState)("latest"),k=(0,a.useCallback)(async()=>{try{let e=new URLSearchParams({status:"PUBLISHED",...j&&{categoryId:j},...y&&{sortBy:y},...v&&{search:v}}),r=await fetch(`/api/articles?${e}`);if(r.ok){let e=await r.json();s(e.articles||[])}}catch(e){console.error("Error loading articles:",e)}finally{g(!1)}},[j,y]),A=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),C=e=>e.firstName&&e.lastName?`${e.firstName} ${e.lastName}`:e.username;return f?(0,t.jsx)("div",{className:"min-h-screen bg-background",children:(0,t.jsx)("div",{className:"max-w-6xl mx-auto py-8 px-4",children:(0,t.jsxs)("div",{className:"animate-pulse space-y-6",children:[(0,t.jsx)("div",{className:"h-8 bg-muted rounded w-1/4"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,s)=>(0,t.jsx)(x.Zp,{children:(0,t.jsxs)(x.Wu,{className:"p-6",children:[(0,t.jsx)("div",{className:"h-48 bg-muted rounded mb-4"}),(0,t.jsx)("div",{className:"h-6 bg-muted rounded mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-muted rounded w-3/4"})]})},s))})]})})}):(0,t.jsx)("div",{className:"min-h-screen bg-background",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto py-8 px-4",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground mb-4",children:"Articles"}),(0,t.jsx)(x.Zp,{children:(0,t.jsxs)(x.Wu,{className:"p-6",children:[(0,t.jsx)("form",{onSubmit:e=>{e.preventDefault(),k()},className:"mb-4",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5"}),(0,t.jsx)(h.p,{type:"text",placeholder:"Search articles...",value:v,onChange:e=>b(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(o,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("select",{value:j,onChange:e=>N(e.target.value),className:"px-3 py-1 border border-input rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground","aria-label":"Filter by category",children:[(0,t.jsx)("option",{value:"",children:"All Categories"}),r.map(e=>(0,t.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}),(0,t.jsxs)("select",{value:y,onChange:e=>w(e.target.value),className:"px-3 py-1 border border-input rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring bg-background text-foreground","aria-label":"Sort articles",children:[(0,t.jsx)("option",{value:"latest",children:"Latest"}),(0,t.jsx)("option",{value:"oldest",children:"Oldest"}),(0,t.jsx)("option",{value:"popular",children:"Most Popular"}),(0,t.jsx)("option",{value:"trending",children:"Trending"})]})]})]})]})})]}),0===e.length?(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsx)("p",{className:"text-muted-foreground text-lg",children:"No articles found."})}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,t.jsxs)(x.Zp,{className:"hover:shadow-md transition-shadow",children:[e.featuredImageUrl&&(0,t.jsx)("div",{className:"aspect-video overflow-hidden rounded-t-lg",children:(0,t.jsx)("img",{src:e.featuredImageUrl,alt:e.title,className:"w-full h-full object-cover hover:scale-105 transition-transform duration-300"})}),(0,t.jsxs)(x.Wu,{className:"p-6",children:[e.category&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs font-medium rounded-full text-white bg-primary",children:e.category.name})}),(0,t.jsx)("h2",{className:"text-xl font-bold text-foreground mb-2 line-clamp-2",children:(0,t.jsx)(i(),{href:`/articles/${e.slug}`,className:"hover:text-primary transition-colors",children:e.title})}),e.subtitle&&(0,t.jsx)("p",{className:"text-muted-foreground mb-3 line-clamp-2",children:e.subtitle}),e.excerpt&&(0,t.jsx)("p",{className:"text-muted-foreground text-sm mb-4 line-clamp-3",children:e.excerpt}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.author.avatarUrl?(0,t.jsx)("img",{src:e.author.avatarUrl,alt:C(e.author),className:"w-6 h-6 rounded-full"}):(0,t.jsx)("div",{className:"w-6 h-6 rounded-full bg-muted flex items-center justify-center",children:(0,t.jsx)(c.A,{className:"h-3 w-3"})}),(0,t.jsx)("span",{children:C(e.author)})]}),(0,t.jsxs)("span",{children:[e.readingTime," min read"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground pt-4 border-t border-border",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"h-4 w-4"}),e.viewCount]}),(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(u.A,{className:"h-4 w-4"}),e._count.comments]}),(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),e._count.reactions]})]}),e.publishedAt&&(0,t.jsx)("span",{children:A(e.publishedAt)})]})]})]},e.id))})]})})}},40228:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},44493:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n});var t=r(60687),a=r(43210),l=r(4780);let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let n=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",e),...s})).displayName="CardDescription";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",e),...s}));d.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},62429:(e,s,r)=>{Promise.resolve().then(r.bind(r,24596))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67760:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},70440:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75516:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let d={children:["",{children:["articles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,24596)),"C:\\D\\Books\\Blog\\blogcms\\src\\app\\articles\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,80871)),"C:\\D\\Books\\Blog\\blogcms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\D\\Books\\Blog\\blogcms\\src\\app\\articles\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/articles/page",pathname:"/articles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79551:e=>{"use strict";e.exports=require("url")},89667:(e,s,r)=>{"use strict";r.d(s,{p:()=>i});var t=r(60687),a=r(43210),l=r(4780);let i=a.forwardRef(({className:e,type:s,...r},a)=>(0,t.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));i.displayName="Input"}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[243,789,658,945,575],()=>r(75516));module.exports=t})();