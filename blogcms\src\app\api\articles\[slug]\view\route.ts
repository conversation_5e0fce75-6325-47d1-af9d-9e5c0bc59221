import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// POST /api/articles/[slug]/view - Track article view
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params

    // Find article by slug
    const article = await prisma.article.findUnique({
      where: { slug },
      select: { id: true }
    })

    if (!article) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      )
    }

    // Get client IP and user agent for analytics
    const forwarded = request.headers.get('x-forwarded-for')
    const ip = forwarded ? forwarded.split(',')[0] : request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    // Increment view count
    await prisma.article.update({
      where: { id: article.id },
      data: {
        viewCount: {
          increment: 1
        }
      }
    })

    // Record detailed view for analytics (optional)
    await prisma.articleView.create({
      data: {
        articleId: article.id,
        ipAddress: ip,
        userAgent,
        // You could also track userId if user is logged in
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error tracking article view:', error)
    return NextResponse.json(
      { error: 'Failed to track view' },
      { status: 500 }
    )
  }
}
