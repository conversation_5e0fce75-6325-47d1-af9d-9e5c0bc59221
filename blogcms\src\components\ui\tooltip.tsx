'use client'

import * as React from "react"
import { cn } from "@/lib/utils"

interface TooltipProps {
  children: React.ReactNode
  content: React.ReactNode
  side?: "top" | "right" | "bottom" | "left"
  align?: "start" | "center" | "end"
  delayDuration?: number
}

interface TooltipContentProps extends React.HTMLAttributes<HTMLDivElement> {
  side?: "top" | "right" | "bottom" | "left"
  align?: "start" | "center" | "end"
}

const Tooltip = ({ 
  children, 
  content, 
  side = "top", 
  align = "center",
  delayDuration = 700 
}: TooltipProps) => {
  const [open, setOpen] = React.useState(false)
  const [delayHandler, setDelayHandler] = React.useState<NodeJS.Timeout | null>(null)

  const handleMouseEnter = () => {
    const handler = setTimeout(() => setOpen(true), delayDuration)
    setDelay<PERSON>and<PERSON>(handler)
  }

  const handleMouseLeave = () => {
    if (delayHandler) {
      clearTimeout(delayHandler)
      setDelay<PERSON><PERSON>ler(null)
    }
    setOpen(false)
  }

  const getPositionClasses = () => {
    const positions = {
      top: "bottom-full left-1/2 -translate-x-1/2 mb-2",
      bottom: "top-full left-1/2 -translate-x-1/2 mt-2",
      left: "right-full top-1/2 -translate-y-1/2 mr-2",
      right: "left-full top-1/2 -translate-y-1/2 ml-2",
    }
    return positions[side]
  }

  const getArrowClasses = () => {
    const arrows = {
      top: "top-full left-1/2 -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-border",
      bottom: "bottom-full left-1/2 -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-border",
      left: "left-full top-1/2 -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-border",
      right: "right-full top-1/2 -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-border",
    }
    return arrows[side]
  }

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      {open && (
        <div
          className={cn(
            "absolute z-50 px-3 py-1.5 text-sm text-popover-foreground bg-popover border rounded-md shadow-md animate-in fade-in-0 zoom-in-95",
            getPositionClasses()
          )}
        >
          {content}
          <div
            className={cn(
              "absolute w-0 h-0 border-4",
              getArrowClasses()
            )}
          />
        </div>
      )}
    </div>
  )
}

const TooltipTrigger = ({ children, ...props }: { children: React.ReactNode }) => {
  return <div {...props}>{children}</div>
}

const TooltipContent = React.forwardRef<HTMLDivElement, TooltipContentProps>(
  ({ className, side = "top", align = "center", ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95",
        className
      )}
      {...props}
    />
  )
)
TooltipContent.displayName = "TooltipContent"

export { Tooltip, TooltipTrigger, TooltipContent }
