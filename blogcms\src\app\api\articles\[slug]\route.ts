import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSlug, calculateReadingTime, extractExcerpt } from '@/lib/utils'

// GET /api/articles/[slug] - Get single article
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  const { slug } = await params
  try {
    const article = await prisma.article.findUnique({
      where: { slug },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            bio: true,
            avatarUrl: true,
            _count: {
              select: {
                articles: true,
                followers: true
              }
            }
          }
        },
        category: true,
        tags: {
          include: {
            tag: true
          }
        },
        comments: {
          where: { parentId: null },
          include: {
            author: {
              select: {
                id: true,
                username: true,
                firstName: true,
                lastName: true,
                avatarUrl: true
              }
            },
            replies: {
              include: {
                author: {
                  select: {
                    id: true,
                    username: true,
                    firstName: true,
                    lastName: true,
                    avatarUrl: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: {
            comments: true,
            reactions: true,
            bookmarks: true
          }
        }
      }
    })

    if (!article) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      )
    }

    // Increment view count
    await prisma.article.update({
      where: { id: article.id },
      data: { viewCount: { increment: 1 } }
    })

    return NextResponse.json(article)
  } catch (error) {
    console.error('Error fetching article:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT /api/articles/[slug] - Update article
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  const { slug } = await params
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const article = await prisma.article.findUnique({
      where: { slug },
      select: { id: true, authorId: true, status: true, title: true }
    })

    if (!article) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      )
    }

    // Check permissions
    const canEdit = article.authorId === session.user.id || 
                   session.user.role === 'ADMIN' || 
                   session.user.role === 'EDITOR'

    if (!canEdit) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }

    const {
      title,
      subtitle,
      content,
      categoryId,
      featuredImageUrl,
      featuredImageAlt,
      status,
      scheduledAt,
      metaDescription,
      ogTitle,
      ogDescription,
      ogImageUrl,
      allowComments
    } = await request.json()

    const updateData: Record<string, unknown> = {}

    if (title !== undefined) {
      updateData.title = title
      // Update slug if title changed
      if (title !== article.title) {
        const baseSlug = generateSlug(title)
        let newSlug = baseSlug
        let counter = 1

        while (await prisma.article.findFirst({ 
          where: { slug: newSlug, NOT: { id: article.id } } 
        })) {
          newSlug = `${baseSlug}-${counter}`
          counter++
        }
        updateData.slug = newSlug
      }
    }

    if (content !== undefined) {
      updateData.content = content
      updateData.readingTime = calculateReadingTime(content)
      updateData.excerpt = extractExcerpt(content)
    }

    if (subtitle !== undefined) updateData.subtitle = subtitle
    if (categoryId !== undefined) updateData.categoryId = categoryId
    if (featuredImageUrl !== undefined) updateData.featuredImageUrl = featuredImageUrl
    if (featuredImageAlt !== undefined) updateData.featuredImageAlt = featuredImageAlt
    if (metaDescription !== undefined) updateData.metaDescription = metaDescription
    if (ogTitle !== undefined) updateData.ogTitle = ogTitle
    if (ogDescription !== undefined) updateData.ogDescription = ogDescription
    if (ogImageUrl !== undefined) updateData.ogImageUrl = ogImageUrl
    if (allowComments !== undefined) updateData.allowComments = allowComments

    if (status !== undefined) {
      updateData.status = status
      if (status === 'PUBLISHED' && article.status !== 'PUBLISHED') {
        updateData.publishedAt = new Date()
      }
    }

    if (scheduledAt !== undefined) {
      updateData.scheduledAt = scheduledAt ? new Date(scheduledAt) : null
    }

    const updatedArticle = await prisma.article.update({
      where: { id: article.id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
            avatarUrl: true
          }
        },
        category: true,
        tags: {
          include: {
            tag: true
          }
        }
      }
    })

    return NextResponse.json(updatedArticle)
  } catch (error) {
    console.error('Error updating article:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/articles/[slug] - Delete article
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  const { slug } = await params
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const article = await prisma.article.findUnique({
      where: { slug },
      select: { id: true, authorId: true }
    })

    if (!article) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      )
    }

    // Check permissions
    const canDelete = article.authorId === session.user.id || 
                     session.user.role === 'ADMIN'

    if (!canDelete) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }

    await prisma.article.delete({
      where: { id: article.id }
    })

    return NextResponse.json({ message: 'Article deleted successfully' })
  } catch (error) {
    console.error('Error deleting article:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
