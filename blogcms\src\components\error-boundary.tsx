'use client'

import React from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    this.setState({
      error,
      errorInfo,
    })
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />
      }

      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />
    }

    return this.props.children
  }
}

interface ErrorFallbackProps {
  error?: Error
  resetError: () => void
}

function DefaultErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="min-h-[400px] flex items-center justify-center p-8">
      <div className="text-center max-w-md space-y-4">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Something went wrong</AlertTitle>
          <AlertDescription>
            We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.
          </AlertDescription>
        </Alert>

        {process.env.NODE_ENV === 'development' && error && (
          <details className="text-left p-3 bg-muted rounded-md">
            <summary className="cursor-pointer text-sm font-medium">Error Details</summary>
            <pre className="mt-2 text-xs overflow-auto">
              {error.message}
              {error.stack && (
                <>
                  {'\n\n'}
                  {error.stack}
                </>
              )}
            </pre>
          </details>
        )}

        <Button onClick={resetError} className="w-full">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    </div>
  )
}

// Specific error boundaries for different sections
export function EditorErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary fallback={EditorErrorFallback}>
      {children}
    </ErrorBoundary>
  )
}

function EditorErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="border rounded-lg p-8 text-center space-y-4">
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Editor Error</AlertTitle>
        <AlertDescription>
          The editor encountered an error. Your content may not be saved.
        </AlertDescription>
      </Alert>

      <Button onClick={resetError} size="sm">
        <RefreshCw className="h-3 w-3 mr-2" />
        Reload Editor
      </Button>
    </div>
  )
}

export function DashboardErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary fallback={DashboardErrorFallback}>
      {children}
    </ErrorBoundary>
  )
}

function DashboardErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="p-6 text-center space-y-4">
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Dashboard Error</AlertTitle>
        <AlertDescription>
          Unable to load dashboard data. Please try again.
        </AlertDescription>
      </Alert>

      <Button onClick={resetError}>
        <RefreshCw className="h-4 w-4 mr-2" />
        Reload Dashboard
      </Button>
    </div>
  )
}
