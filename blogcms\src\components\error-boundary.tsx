'use client'

import React from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    this.setState({
      error,
      errorInfo,
    })
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />
      }

      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />
    }

    return this.props.children
  }
}

interface ErrorFallbackProps {
  error?: Error
  resetError: () => void
}

function DefaultErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="min-h-[400px] flex items-center justify-center p-8">
      <div className="text-center max-w-md">
        <div className="mb-4">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto" />
        </div>
        <h2 className="text-xl font-semibold text-foreground mb-2">Something went wrong</h2>
        <p className="text-muted-foreground mb-4">
          We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.
        </p>
        {process.env.NODE_ENV === 'development' && error && (
          <details className="text-left mb-4 p-3 bg-muted rounded-md">
            <summary className="cursor-pointer text-sm font-medium">Error Details</summary>
            <pre className="mt-2 text-xs overflow-auto">
              {error.message}
              {error.stack && (
                <>
                  {'\n\n'}
                  {error.stack}
                </>
              )}
            </pre>
          </details>
        )}
        <button
          type="button"
          onClick={resetError}
          className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
        >
          <RefreshCw className="h-4 w-4" />
          Try Again
        </button>
      </div>
    </div>
  )
}

// Specific error boundaries for different sections
export function EditorErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary fallback={EditorErrorFallback}>
      {children}
    </ErrorBoundary>
  )
}

function EditorErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="border rounded-lg p-8 text-center">
      <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-3" />
      <h3 className="text-lg font-medium text-foreground mb-2">Editor Error</h3>
      <p className="text-muted-foreground mb-4">
        The editor encountered an error. Your content may not be saved.
      </p>
      <button
        type="button"
        onClick={resetError}
        className="inline-flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-sm"
      >
        <RefreshCw className="h-3 w-3" />
        Reload Editor
      </button>
    </div>
  )
}

export function DashboardErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary fallback={DashboardErrorFallback}>
      {children}
    </ErrorBoundary>
  )
}

function DashboardErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="p-6 text-center">
      <AlertTriangle className="h-10 w-10 text-destructive mx-auto mb-4" />
      <h3 className="text-xl font-medium text-foreground mb-2">Dashboard Error</h3>
      <p className="text-muted-foreground mb-4">
        Unable to load dashboard data. Please try again.
      </p>
      <button
        type="button"
        onClick={resetError}
        className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
      >
        <RefreshCw className="h-4 w-4" />
        Reload Dashboard
      </button>
    </div>
  )
}
