'use client'

import { useEditor, EditorContent } from '@tiptap/react'
import StarterKit from '@tiptap/starter-kit'
import Image from '@tiptap/extension-image'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'
import Youtube from '@tiptap/extension-youtube'
import Link from '@tiptap/extension-link'
import TextAlign from '@tiptap/extension-text-align'
import Color from '@tiptap/extension-color'
import Highlight from '@tiptap/extension-highlight'
import FontFamily from '@tiptap/extension-font-family'
import Underline from '@tiptap/extension-underline'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight'
import TaskList from '@tiptap/extension-task-list'
import TaskItem from '@tiptap/extension-task-item'
import CharacterCount from '@tiptap/extension-character-count'
import Placeholder from '@tiptap/extension-placeholder'
import { createLowlight } from 'lowlight'
import js from 'highlight.js/lib/languages/javascript'
import ts from 'highlight.js/lib/languages/typescript'
import html from 'highlight.js/lib/languages/xml'
import css from 'highlight.js/lib/languages/css'
import { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { EditorToolbar } from './editor-toolbar'
import { MediaUploadDialog } from './media-upload-dialog'
// import { ChartDialog } from './chart-dialog'
import { TableDialog } from './table-dialog'

interface RichTextEditorProps {
  content?: string
  onChange?: (content: string) => void
  placeholder?: string
  className?: string
  readOnly?: boolean
}

export function RichTextEditor({
  content = '',
  onChange,
  placeholder = 'Start writing your story...',
  className = '',
  readOnly = false
}: RichTextEditorProps) {
  const [isMediaDialogOpen, setIsMediaDialogOpen] = useState(false)
  const [isChartDialogOpen, setIsChartDialogOpen] = useState(false)
  const [isTableDialogOpen, setIsTableDialogOpen] = useState(false)

  const lowlight = createLowlight()

  // Register languages
  lowlight.register('javascript', js)
  lowlight.register('typescript', ts)
  lowlight.register('html', html)
  lowlight.register('css', css)

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg max-w-full h-auto',
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-border my-4',
        },
      }),
      TableRow.configure({
        HTMLAttributes: {
          class: 'border border-border',
        },
      }),
      TableHeader.configure({
        HTMLAttributes: {
          class: 'border border-border bg-muted font-semibold p-2',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-border p-2',
        },
      }),
      Youtube.configure({
        width: 640,
        height: 480,
        HTMLAttributes: {
          class: 'rounded-lg my-4',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-primary hover:text-primary/80 underline',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      FontFamily.configure({
        types: ['textStyle'],
      }),
      Underline,
      Subscript,
      Superscript,
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: 'bg-muted rounded-lg p-4 my-4 overflow-x-auto',
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: 'not-prose',
        },
      }),
      TaskItem.configure({
        nested: true,
        HTMLAttributes: {
          class: 'flex items-start gap-2',
        },
      }),
      CharacterCount,
      Placeholder.configure({
        placeholder,
      }),
    ],
    content,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML())
    },
    editorProps: {
      attributes: {
        class: `prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4 ${className}`,
      },
    },
  })

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      acceptedFiles.forEach((file) => {
        if (file.type.startsWith('image/')) {
          const reader = new FileReader()
          reader.onload = () => {
            const url = reader.result as string
            editor?.chain().focus().setImage({ src: url }).run()
          }
          reader.readAsDataURL(file)
        }
      })
    },
    [editor]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp'],
      'video/*': ['.mp4', '.webm', '.ogg'],
      'application/pdf': ['.pdf'],
    },
    noClick: true,
  })

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="h-12 bg-muted rounded mb-4"></div>
        <div className="h-64 bg-muted rounded"></div>
      </div>
    )
  }

  return (
    <div className="border rounded-lg overflow-hidden">
      {!readOnly && (
        <EditorToolbar
          editor={editor}
          onOpenMediaDialog={() => setIsMediaDialogOpen(true)}
          onOpenTableDialog={() => setIsTableDialogOpen(true)}
        />
      )}
      
      <div
        {...getRootProps()}
        className={`relative ${isDragActive ? 'bg-primary/5 border-primary/30' : ''}`}
      >
        <input {...getInputProps()} />
        
        {isDragActive && (
          <div className="absolute inset-0 bg-primary/5 bg-opacity-90 flex items-center justify-center z-10 border-2 border-dashed border-primary/30">
            <p className="text-primary font-medium">Drop files here to upload</p>
          </div>
        )}
        
        <EditorContent editor={editor} />
      </div>

      {!readOnly && editor.storage.characterCount && (
        <div className="px-4 py-2 bg-muted border-t text-sm text-muted-foreground flex justify-between">
          <span>
            {editor.storage.characterCount.characters()} characters, {editor.storage.characterCount.words()} words
          </span>
          <span>
            Reading time: ~{Math.ceil(editor.storage.characterCount.words() / 200)} min
          </span>
        </div>
      )}

      <MediaUploadDialog
        isOpen={isMediaDialogOpen}
        onClose={() => setIsMediaDialogOpen(false)}
        onInsert={(url, type) => {
          if (type === 'image') {
            editor.chain().focus().setImage({ src: url }).run()
          } else if (type === 'video') {
            // Check if it's a YouTube embed URL or regular video
            if (url.includes('youtube.com/embed/') || url.includes('youtu.be/') || url.includes('youtube.com/watch')) {
              editor.chain().focus().setYoutubeVideo({ src: url }).run()
            } else if (url.startsWith('<iframe') || url.startsWith('<embed')) {
              // Handle embed codes
              editor.chain().focus().insertContent(url).run()
            } else {
              // Handle direct video files
              const videoHtml = `<video controls class="max-w-full h-auto rounded-lg my-4"><source src="${url}" type="video/mp4">Your browser does not support the video tag.</video>`
              editor.chain().focus().insertContent(videoHtml).run()
            }
          } else if (type === 'audio') {
            const audioHtml = `<audio controls class="w-full my-4"><source src="${url}" type="audio/mpeg">Your browser does not support the audio tag.</audio>`
            editor.chain().focus().insertContent(audioHtml).run()
          } else if (type === 'pdf') {
            const pdfHtml = `<div class="pdf-embed my-4 p-4 border rounded-lg bg-gray-50"><a href="${url}" target="_blank" class="flex items-center gap-2 text-blue-600 hover:text-blue-800"><svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path d="M4 18h12V6l-4-4H4v16z"/></svg>View PDF Document</a></div>`
            editor.chain().focus().insertContent(pdfHtml).run()
          }
        }}
      />

      {/* <ChartDialog
        isOpen={isChartDialogOpen}
        onClose={() => setIsChartDialogOpen(false)}
        onInsert={(chartHtml) => {
          editor.chain().focus().insertContent(chartHtml).run()
        }}
      /> */}

      <TableDialog
        isOpen={isTableDialogOpen}
        onClose={() => setIsTableDialogOpen(false)}
        onInsert={(rows, cols) => {
          editor.chain().focus().insertTable({ rows, cols, withHeaderRow: true }).run()
        }}
      />
    </div>
  )
}
