import { render, screen } from '@testing-library/react'
import { Alert, AlertTitle, AlertDescription } from '../alert'
import { AlertTriangle, CheckCircle, Info } from 'lucide-react'

describe('Alert Components', () => {
  describe('Alert', () => {
    it('renders with default variant', () => {
      render(
        <Alert data-testid="alert">
          <AlertTitle>Default Alert</AlertTitle>
          <AlertDescription>This is a default alert</AlertDescription>
        </Alert>
      )
      
      const alert = screen.getByTestId('alert')
      expect(alert).toBeInTheDocument()
      expect(alert).toHaveAttribute('role', 'alert')
      expect(alert).toHaveClass('bg-background', 'text-foreground')
    })

    it('renders with destructive variant', () => {
      render(
        <Alert variant="destructive" data-testid="alert">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Something went wrong</AlertDescription>
        </Alert>
      )
      
      const alert = screen.getByTestId('alert')
      expect(alert).toHaveClass('border-destructive/50', 'text-destructive')
    })

    it('renders with warning variant', () => {
      render(
        <Alert variant="warning" data-testid="alert">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Warning</AlertTitle>
          <AlertDescription>Please be careful</AlertDescription>
        </Alert>
      )
      
      const alert = screen.getByTestId('alert')
      expect(alert).toHaveClass('border-yellow-500/50', 'text-yellow-600')
    })

    it('renders with success variant', () => {
      render(
        <Alert variant="success" data-testid="alert">
          <CheckCircle className="h-4 w-4" />
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>Operation completed successfully</AlertDescription>
        </Alert>
      )
      
      const alert = screen.getByTestId('alert')
      expect(alert).toHaveClass('border-green-500/50', 'text-green-600')
    })

    it('accepts custom className', () => {
      render(
        <Alert className="custom-alert" data-testid="alert">
          <AlertDescription>Custom alert</AlertDescription>
        </Alert>
      )
      
      const alert = screen.getByTestId('alert')
      expect(alert).toHaveClass('custom-alert')
    })
  })

  describe('AlertTitle', () => {
    it('renders with correct styling', () => {
      render(<AlertTitle>Alert Title</AlertTitle>)
      const title = screen.getByText('Alert Title')
      
      expect(title).toBeInTheDocument()
      expect(title).toHaveClass('mb-1', 'font-medium', 'leading-none', 'tracking-tight')
    })

    it('forwards ref correctly', () => {
      const ref = jest.fn()
      render(<AlertTitle ref={ref}>Title</AlertTitle>)
      
      expect(ref).toHaveBeenCalled()
    })
  })

  describe('AlertDescription', () => {
    it('renders with correct styling', () => {
      render(<AlertDescription>Alert description text</AlertDescription>)
      const description = screen.getByText('Alert description text')
      
      expect(description).toBeInTheDocument()
      expect(description).toHaveClass('text-sm')
    })

    it('can contain rich content', () => {
      render(
        <AlertDescription>
          <p>Paragraph 1</p>
          <p>Paragraph 2</p>
        </AlertDescription>
      )
      
      expect(screen.getByText('Paragraph 1')).toBeInTheDocument()
      expect(screen.getByText('Paragraph 2')).toBeInTheDocument()
    })
  })

  describe('Complete Alert', () => {
    it('renders with icon, title, and description', () => {
      render(
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error occurred</AlertTitle>
          <AlertDescription>
            There was a problem with your request. Please try again.
          </AlertDescription>
        </Alert>
      )

      expect(screen.getByText('Error occurred')).toBeInTheDocument()
      expect(screen.getByText('There was a problem with your request. Please try again.')).toBeInTheDocument()
    })
  })
})
