(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[445],{34:(e,t,n)=>{"use strict";n.d(t,{$:()=>o});var i=n(8772);let r={};function o(e){let t=i.newInstance();return e&&s(e),{highlight:n,highlightAuto:function(e,i){let a,s=(i||r).subset||o(),l=-1,c=0;for(;++l<s.length;){let r=s[l];if(!t.getLanguage(r))continue;let o=n(r,e,i);o.data&&void 0!==o.data.relevance&&o.data.relevance>c&&(c=o.data.relevance,a=o)}return a||{type:"root",children:[],data:{language:void 0,relevance:c}}},listLanguages:o,register:s,registerAlias:function(e,n){if("string"==typeof e)t.registerAliases("string"==typeof n?n:[...n],{languageName:e});else{let n;for(n in e)if(Object.hasOwn(e,n)){let i=e[n];t.registerAliases("string"==typeof i?i:[...i],{languageName:n})}}},registered:function(e){return!!t.getLanguage(e)}};function n(e,n,i){let o=i||r,s="string"==typeof o.prefix?o.prefix:"hljs-";if(!t.getLanguage(e))throw Error("Unknown language: `"+e+"` is not registered");t.configure({__emitter:a,classPrefix:s});let l=t.highlight(n,{ignoreIllegals:!0,language:e});if(l.errorRaised)throw Error("Could not highlight with `Highlight.js`",{cause:l.errorRaised});let c=l._emitter.root,p=c.data;return p.language=l.language,p.relevance=l.relevance,c}function o(){return t.listLanguages()}function s(e,n){if("string"==typeof e)t.registerLanguage(e,n);else{let n;for(n in e)Object.hasOwn(e,n)&&t.registerLanguage(n,e[n])}}}class a{constructor(e){this.options=e,this.root={type:"root",children:[],data:{language:void 0,relevance:0}},this.stack=[this.root]}addText(e){if(""===e)return;let t=this.stack[this.stack.length-1],n=t.children[t.children.length-1];n&&"text"===n.type?n.value+=e:t.children.push({type:"text",value:e})}startScope(e){this.openNode(String(e))}endScope(){this.closeNode()}__addSublanguage(e,t){let n=this.stack[this.stack.length-1],i=e.root.children;t?n.children.push({type:"element",tagName:"span",properties:{className:[t]},children:i}):n.children.push(...i)}openNode(e){let t=this,n=e.split(".").map(function(e,n){return n?e+"_".repeat(n):t.options.classPrefix+e}),i=this.stack[this.stack.length-1],r={type:"element",tagName:"span",properties:{className:n},children:[]};i.children.push(r),this.stack.push(r)}closeNode(){this.stack.pop()}finalize(){}toHTML(){return""}}},156:(e,t,n)=>{"use strict";function i(e){this.content=e}n.d(t,{S4:()=>B,ZF:()=>G,FK:()=>r,CU:()=>l,sX:()=>D,bP:()=>S,u$:()=>w,vI:()=>c,Sj:()=>$,Ji:()=>p}),i.prototype={constructor:i,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return -1},get:function(e){var t=this.find(e);return -1==t?void 0:this.content[t+1]},update:function(e,t,n){var r=n&&n!=e?this.remove(n):this,o=r.find(e),a=r.content.slice();return -1==o?a.push(n||e,t):(a[o+1]=t,n&&(a[o]=n)),new i(a)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new i(n)},addToStart:function(e,t){return new i([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new i(n)},addBefore:function(e,t,n){var r=this.remove(t),o=r.content.slice(),a=r.find(e);return o.splice(-1==a?o.length:a,0,t,n),new i(o)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=i.from(e)).size?new i(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=i.from(e)).size?new i(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=i.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach(function(t,n){e[t]=n}),e},get size(){return this.content.length>>1}},i.from=function(e){if(e instanceof i)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new i(t)};class r{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let t=0;t<e.length;t++)this.size+=e[t].nodeSize}nodesBetween(e,t,n,i=0,r){for(let o=0,a=0;a<t;o++){let s=this.content[o],l=a+s.nodeSize;if(l>e&&!1!==n(s,i+a,r||null,o)&&s.content.size){let r=a+1;s.nodesBetween(Math.max(0,e-r),Math.min(s.content.size,t-r),n,i+r)}a=l}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,i){let r="",o=!0;return this.nodesBetween(e,t,(a,s)=>{let l=a.isText?a.text.slice(Math.max(e,s)-s,t-s):a.isLeaf?i?"function"==typeof i?i(a):i:a.type.spec.leafText?a.type.spec.leafText(a):"":"";a.isBlock&&(a.isLeaf&&l||a.isTextblock)&&n&&(o?o=!1:r+=n),r+=l},0),r}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,i=this.content.slice(),o=0;for(t.isText&&t.sameMarkup(n)&&(i[i.length-1]=t.withText(t.text+n.text),o=1);o<e.content.length;o++)i.push(e.content[o]);return new r(i,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],i=0;if(t>e)for(let r=0,o=0;o<t;r++){let a=this.content[r],s=o+a.nodeSize;s>e&&((o<e||s>t)&&(a=a.isText?a.cut(Math.max(0,e-o),Math.min(a.text.length,t-o)):a.cut(Math.max(0,e-o-1),Math.min(a.content.size,t-o-1))),n.push(a),i+=a.nodeSize),o=s}return new r(n,i)}cutByIndex(e,t){return e==t?r.empty:0==e&&t==this.content.length?this:new r(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let i=this.content.slice(),o=this.size+t.nodeSize-n.nodeSize;return i[e]=t,new r(i,o)}addToStart(e){return new r([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new r(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let i=this.content[t];e(i,n,t),n+=i.nodeSize}}findDiffStart(e,t=0){return function e(t,n,i){for(let r=0;;r++){if(r==t.childCount||r==n.childCount)return t.childCount==n.childCount?null:i;let o=t.child(r),a=n.child(r);if(o==a){i+=o.nodeSize;continue}if(!o.sameMarkup(a))return i;if(o.isText&&o.text!=a.text){for(let e=0;o.text[e]==a.text[e];e++)i++;return i}if(o.content.size||a.content.size){let t=e(o.content,a.content,i+1);if(null!=t)return t}i+=o.nodeSize}}(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return function e(t,n,i,r){for(let o=t.childCount,a=n.childCount;;){if(0==o||0==a)return o==a?null:{a:i,b:r};let s=t.child(--o),l=n.child(--a),c=s.nodeSize;if(s==l){i-=c,r-=c;continue}if(!s.sameMarkup(l))return{a:i,b:r};if(s.isText&&s.text!=l.text){let e=0,t=Math.min(s.text.length,l.text.length);for(;e<t&&s.text[s.text.length-e-1]==l.text[l.text.length-e-1];)e++,i--,r--;return{a:i,b:r}}if(s.content.size||l.content.size){let t=e(s.content,l.content,i-1,r-1);if(t)return t}i-=c,r-=c}}(this,e,t,n)}findIndex(e,t=-1){if(0==e)return a(0,e);if(e==this.size)return a(this.content.length,e);if(e>this.size||e<0)throw RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,i=0;;n++){let r=i+this.child(n).nodeSize;if(r>=e){if(r==e||t>0)return a(n+1,r);return a(n,i)}i=r}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return r.empty;if(!Array.isArray(t))throw RangeError("Invalid input for Fragment.fromJSON");return new r(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return r.empty;let t,n=0;for(let i=0;i<e.length;i++){let r=e[i];n+=r.nodeSize,i&&r.isText&&e[i-1].sameMarkup(r)?(t||(t=e.slice(0,i)),t[t.length-1]=r.withText(t[t.length-1].text+r.text)):t&&t.push(r)}return new r(t||e,n)}static from(e){if(!e)return r.empty;if(e instanceof r)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new r([e],e.nodeSize);throw RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}r.empty=new r([],0);let o={index:0,offset:0};function a(e,t){return o.index=e,o.offset=t,o}function s(e,t){if(e===t)return!0;if(!(e&&"object"==typeof e)||!(t&&"object"==typeof t))return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!s(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!s(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}class l{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let i=0;i<e.length;i++){let r=e[i];if(this.eq(r))return e;if(this.type.excludes(r.type))t||(t=e.slice(0,i));else{if(r.type.excludes(this.type))return e;!n&&r.type.rank>this.type.rank&&(t||(t=e.slice(0,i)),t.push(this),n=!0),t&&t.push(r)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&s(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw RangeError(`There is no mark type ${t.type} in this schema`);let i=n.create(t.attrs);return n.checkAttrs(i.attrs),i}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&0==e.length)return l.none;if(e instanceof l)return[e];let t=e.slice();return t.sort((e,t)=>e.type.rank-t.type.rank),t}}l.none=[];class c extends Error{}class p{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=function e(t,n,i,r){let{index:o,offset:a}=t.findIndex(n),s=t.maybeChild(o);if(a==n||s.isText)return r&&!r.canReplace(o,o,i)?null:t.cut(0,n).append(i).append(t.cut(n));let l=e(s.content,n-a-1,i);return l&&t.replaceChild(o,s.copy(l))}(this.content,e+this.openStart,t);return n&&new p(n,this.openStart,this.openEnd)}removeBetween(e,t){return new p(function e(t,n,i){let{index:r,offset:o}=t.findIndex(n),a=t.maybeChild(r),{index:s,offset:l}=t.findIndex(i);if(o==n||a.isText){if(l!=i&&!t.child(s).isText)throw RangeError("Removing non-flat range");return t.cut(0,n).append(t.cut(i))}if(r!=s)throw RangeError("Removing non-flat range");return t.replaceChild(r,a.copy(e(a.content,n-o-1,i-o-1)))}(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return p.empty;let n=t.openStart||0,i=t.openEnd||0;if("number"!=typeof n||"number"!=typeof i)throw RangeError("Invalid input for Slice.fromJSON");return new p(r.fromJSON(e,t.content),n,i)}static maxOpen(e,t=!0){let n=0,i=0;for(let i=e.firstChild;i&&!i.isLeaf&&(t||!i.type.spec.isolating);i=i.firstChild)n++;for(let n=e.lastChild;n&&!n.isLeaf&&(t||!n.type.spec.isolating);n=n.lastChild)i++;return new p(e,n,i)}}function d(e,t){if(!t.type.compatibleContent(e.type))throw new c("Cannot join "+t.type.name+" onto "+e.type.name)}function h(e,t,n){let i=e.node(n);return d(i,t.node(n)),i}function u(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function f(e,t,n,i){let r=(t||e).node(n),o=0,a=t?t.index(n):r.childCount;e&&(o=e.index(n),e.depth>n?o++:e.textOffset&&(u(e.nodeAfter,i),o++));for(let e=o;e<a;e++)u(r.child(e),i);t&&t.depth==n&&t.textOffset&&u(t.nodeBefore,i)}function m(e,t){return e.type.checkContent(t),e.copy(t)}function g(e,t,n){let i=[];return f(null,e,n,i),e.depth>n&&u(m(h(e,t,n+1),g(e,t,n+1)),i),f(t,null,n,i),new r(i)}p.empty=new p(r.empty,0,0);class v{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],i=e.child(t);return n?e.child(t).cut(n):i}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],i=0==t?0:this.path[3*t-1]+1;for(let t=0;t<e;t++)i+=n.child(t).nodeSize;return i}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return l.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),i=e.maybeChild(t);if(!n){let e=n;n=i,i=e}let r=n.marks;for(var o=0;o<r.length;o++)!1!==r[o].type.spec.inclusive||i&&r[o].isInSet(i.marks)||(r=r[o--].removeFromSet(r));return r}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,i=e.parent.maybeChild(e.index());for(var r=0;r<n.length;r++)!1!==n[r].type.spec.inclusive||i&&n[r].isInSet(i.marks)||(n=n[r--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new w(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw RangeError("Position "+t+" out of range");let n=[],i=0,r=t;for(let t=e;;){let{index:e,offset:o}=t.content.findIndex(r),a=r-o;if(n.push(t,e,i+o),!a||(t=t.child(e)).isText)break;r=a-1,i+=o+1}return new v(t,n,r)}static resolveCached(e,t){let n=x.get(e);if(n)for(let e=0;e<n.elts.length;e++){let i=n.elts[e];if(i.pos==t)return i}else x.set(e,n=new b);let i=n.elts[n.i]=v.resolve(e,t);return n.i=(n.i+1)%y,i}}class b{constructor(){this.elts=[],this.i=0}}let y=12,x=new WeakMap;class w{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let k=Object.create(null);class S{constructor(e,t,n,i=l.none){this.type=e,this.attrs=t,this.marks=i,this.content=n||r.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,i=0){this.content.nodesBetween(e,t,n,i,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,i){return this.content.textBetween(e,t,n,i)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&s(this.attrs,t||e.defaultAttrs||k)&&l.sameSet(this.marks,n||l.none)}copy(e=null){return e==this.content?this:new S(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new S(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return p.empty;let i=this.resolve(e),r=this.resolve(t),o=n?0:i.sharedDepth(t),a=i.start(o);return new p(i.node(o).content.cut(i.pos-a,r.pos-a),i.depth-o,r.depth-o)}replace(e,t,n){var i=this.resolve(e),o=this.resolve(t);if(n.openStart>i.depth)throw new c("Inserted content deeper than insertion position");if(i.depth-n.openStart!=o.depth-n.openEnd)throw new c("Inconsistent open depths");return function e(t,n,i,o){let a=t.index(o),s=t.node(o);if(a==n.index(o)&&o<t.depth-i.openStart){let r=e(t,n,i,o+1);return s.copy(s.content.replaceChild(a,r))}if(!i.content.size)return m(s,g(t,n,o));if(i.openStart||i.openEnd||t.depth!=o||n.depth!=o){let{start:e,end:a}=function(e,t){let n=t.depth-e.openStart,i=t.node(n).copy(e.content);for(let e=n-1;e>=0;e--)i=t.node(e).copy(r.from(i));return{start:i.resolveNoCache(e.openStart+n),end:i.resolveNoCache(i.content.size-e.openEnd-n)}}(i,t);return m(s,function e(t,n,i,o,a){let s=t.depth>a&&h(t,n,a+1),l=o.depth>a&&h(i,o,a+1),c=[];return f(null,t,a,c),s&&l&&n.index(a)==i.index(a)?(d(s,l),u(m(s,e(t,n,i,o,a+1)),c)):(s&&u(m(s,g(t,n,a+1)),c),f(n,i,a,c),l&&u(m(l,g(i,o,a+1)),c)),f(o,null,a,c),new r(c)}(t,e,a,n,o))}{let e=t.parent,r=e.content;return m(e,r.cut(0,t.parentOffset).append(i.content).append(r.cut(n.parentOffset)))}}(i,o,n,0)}nodeAt(e){for(let t=this;;){let{index:n,offset:i}=t.content.findIndex(e);if(!(t=t.maybeChild(n)))return null;if(i==e||t.isText)return t;e-=i+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let i=this.content.child(t-1);return{node:i,index:t-1,offset:n-i.nodeSize}}resolve(e){return v.resolveCached(this,e)}resolveNoCache(e){return v.resolve(this,e)}rangeHasMark(e,t,n){let i=!1;return t>e&&this.nodesBetween(e,t,e=>(n.isInSet(e.marks)&&(i=!0),!i)),i}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),E(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=r.empty,i=0,o=n.childCount){let a=this.contentMatchAt(e).matchFragment(n,i,o),s=a&&a.matchFragment(this.content,t);if(!s||!s.validEnd)return!1;for(let e=i;e<o;e++)if(!this.type.allowsMarks(n.child(e).marks))return!1;return!0}canReplaceWith(e,t,n,i){if(i&&!this.type.allowsMarks(i))return!1;let r=this.contentMatchAt(e).matchType(n),o=r&&r.matchFragment(this.content,t);return!!o&&o.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=l.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!l.sameSet(e,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(e=>e.type.name)}`);this.content.forEach(e=>e.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(e=>e.toJSON())),e}static fromJSON(e,t){let n;if(!t)throw RangeError("Invalid input for Node.fromJSON");if(t.marks){if(!Array.isArray(t.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw RangeError("Invalid text node in JSON");return e.text(t.text,n)}let i=r.fromJSON(e,t.content),o=e.nodeType(t.type).create(t.attrs,i,n);return o.type.checkAttrs(o.attrs),o}}S.prototype.text=void 0;class A extends S{constructor(e,t,n,i){if(super(e,t,null,i),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):E(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new A(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new A(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function E(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class M{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){var n;let i,r=new C(e,t);if(null==r.next)return M.empty;let o=function e(t){let n=[];do n.push(function(t){let n=[];do n.push(function(t){let n=function(t){if(t.eat("(")){let n=e(t);return t.eat(")")||t.err("Missing closing paren"),n}if(/\W/.test(t.next))t.err("Unexpected token '"+t.next+"'");else{let e=(function(e,t){let n=e.nodeTypes,i=n[t];if(i)return[i];let r=[];for(let e in n){let i=n[e];i.isInGroup(t)&&r.push(i)}return 0==r.length&&e.err("No node type or group '"+t+"' found"),r})(t,t.next).map(e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e}));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}}(t);for(;;)if(t.eat("+"))n={type:"plus",expr:n};else if(t.eat("*"))n={type:"star",expr:n};else if(t.eat("?"))n={type:"opt",expr:n};else if(t.eat("{"))n=function(e,t){let n=T(e),i=n;return e.eat(",")&&(i="}"!=e.next?T(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:i,expr:t}}(t,n);else break;return n}(t));while(t.next&&")"!=t.next&&"|"!=t.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(t));while(t.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(r);r.next&&r.err("Unexpected trailing text");let a=(n=function(e){let t=[[]];return r(function e(t,o){if("choice"==t.type)return t.exprs.reduce((t,n)=>t.concat(e(n,o)),[]);if("seq"==t.type)for(let i=0;;i++){let a=e(t.exprs[i],o);if(i==t.exprs.length-1)return a;r(a,o=n())}else if("star"==t.type){let a=n();return i(o,a),r(e(t.expr,a),a),[i(a)]}else if("plus"==t.type){let a=n();return r(e(t.expr,o),a),r(e(t.expr,a),a),[i(a)]}else if("opt"==t.type)return[i(o)].concat(e(t.expr,o));else if("range"==t.type){let a=o;for(let i=0;i<t.min;i++){let i=n();r(e(t.expr,a),i),a=i}if(-1==t.max)r(e(t.expr,a),a);else for(let o=t.min;o<t.max;o++){let o=n();i(a,o),r(e(t.expr,a),o),a=o}return[i(a)]}else if("name"==t.type)return[i(o,void 0,t.value)];else throw Error("Unknown expr type")}(e,0),n()),t;function n(){return t.push([])-1}function i(e,n,i){let r={term:i,to:n};return t[e].push(r),r}function r(e,t){e.forEach(e=>e.to=t)}}(o),i=Object.create(null),function e(t){let r=[];t.forEach(e=>{n[e].forEach(({term:e,to:t})=>{let i;if(e){for(let t=0;t<r.length;t++)r[t][0]==e&&(i=r[t][1]);N(n,t).forEach(t=>{i||r.push([e,i=[]]),-1==i.indexOf(t)&&i.push(t)})}})});let o=i[t.join(",")]=new M(t.indexOf(n.length-1)>-1);for(let t=0;t<r.length;t++){let n=r[t][1].sort(O);o.next.push({type:r[t][0],next:i[n.join(",")]||e(n)})}return o}(N(n,0)));return function(e,t){for(let n=0,i=[e];n<i.length;n++){let e=i[n],r=!e.validEnd,o=[];for(let t=0;t<e.next.length;t++){let{type:n,next:a}=e.next[t];o.push(n.name),r&&!(n.isText||n.hasRequiredAttrs())&&(r=!1),-1==i.indexOf(a)&&i.push(a)}r&&t.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(a,r),a}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let i=this;for(let r=t;i&&r<n;r++)i=i.matchType(e.child(r).type);return i}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let i=[this];return function o(a,s){let l=a.matchFragment(e,n);if(l&&(!t||l.validEnd))return r.from(s.map(e=>e.createAndFill()));for(let e=0;e<a.next.length;e++){let{type:t,next:n}=a.next[e];if(!(t.isText||t.hasRequiredAttrs())&&-1==i.indexOf(n)){i.push(n);let e=o(n,s.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let t=0;t<this.wrapCache.length;t+=2)if(this.wrapCache[t]==e)return this.wrapCache[t+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let i=n.shift(),r=i.match;if(r.matchType(e)){let e=[];for(let t=i;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<r.next.length;e++){let{type:o,next:a}=r.next[e];o.isLeaf||o.hasRequiredAttrs()||o.name in t||i.type&&!a.validEnd||(n.push({match:o.contentMatch,type:o,via:i}),t[o.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return!function t(n){e.push(n);for(let i=0;i<n.next.length;i++)-1==e.indexOf(n.next[i].next)&&t(n.next[i].next)}(this),e.map((t,n)=>{let i=n+(t.validEnd?"*":" ")+" ";for(let n=0;n<t.next.length;n++)i+=(n?", ":"")+t.next[n].type.name+"->"+e.indexOf(t.next[n].next);return i}).join("\n")}}M.empty=new M(!0);class C{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw SyntaxError(e+" (in content expression '"+this.string+"')")}}function T(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function O(e,t){return t-e}function N(e,t){let n=[];return function t(i){let r=e[i];if(1==r.length&&!r[0].term)return t(r[0].to);n.push(i);for(let e=0;e<r.length;e++){let{term:i,to:o}=r[e];i||-1!=n.indexOf(o)||t(o)}}(t),n.sort(O)}function R(e){let t=Object.create(null);for(let n in e){let i=e[n];if(!i.hasDefault)return null;t[n]=i.default}return t}function L(e,t){let n=Object.create(null);for(let i in e){let r=t&&t[i];if(void 0===r){let t=e[i];if(t.hasDefault)r=t.default;else throw RangeError("No value supplied for attribute "+i)}n[i]=r}return n}function z(e,t,n,i){for(let i in t)if(!(i in e))throw RangeError(`Unsupported attribute ${i} for ${n} of type ${i}`);for(let n in e){let i=e[n];i.validate&&i.validate(t[n])}}function I(e,t){let n=Object.create(null);if(t)for(let i in t)n[i]=new _(e,i,t[i]);return n}class j{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=I(e,n.attrs),this.defaultAttrs=R(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==M.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:L(this.attrs,e)}create(e=null,t,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new S(this,this.computeAttrs(e),r.from(t),l.setFrom(n))}createChecked(e=null,t,n){return t=r.from(t),this.checkContent(t),new S(this,this.computeAttrs(e),t,l.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=r.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let i=this.contentMatch.matchFragment(t),o=i&&i.fillBefore(r.empty,!0);return o?new S(this,e,t.append(o),l.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let t=0;t<e.childCount;t++)if(!this.allowsMarks(e.child(t).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){z(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){let t;if(null==this.markSet)return e;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:l.none:e}static compile(e,t){let n=Object.create(null);e.forEach((e,i)=>n[e]=new j(e,t,i));let i=t.spec.topNode||"doc";if(!n[i])throw RangeError("Schema is missing its top node type ('"+i+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let e in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class _{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(e,t,n){let i=n.split("|");return n=>{let r=null===n?"null":typeof n;if(0>i.indexOf(r))throw RangeError(`Expected value of type ${i} for attribute ${t} on type ${e}, got ${r}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class D{constructor(e,t,n,i){this.name=e,this.rank=t,this.schema=n,this.spec=i,this.attrs=I(e,i.attrs),this.excluded=null;let r=R(this.attrs);this.instance=r?new l(this,r):null}create(e=null){return!e&&this.instance?this.instance:new l(this,L(this.attrs,e))}static compile(e,t){let n=Object.create(null),i=0;return e.forEach((e,r)=>n[e]=new D(e,i++,t,r)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){z(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class ${constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let n in e)t[n]=e[n];t.nodes=i.from(e.nodes),t.marks=i.from(e.marks||{}),this.nodes=j.compile(this.spec.nodes,this),this.marks=D.compile(this.spec.marks,this);let n=Object.create(null);for(let e in this.nodes){if(e in this.marks)throw RangeError(e+" can not be both a node and a mark");let t=this.nodes[e],i=t.spec.content||"",r=t.spec.marks;if(t.contentMatch=n[i]||(n[i]=M.parse(i,this.nodes)),t.inlineContent=t.contentMatch.inlineContent,t.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!t.isInline||!t.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=t}t.markSet="_"==r?null:r?P(this,r.split(" ")):""!=r&&t.inlineContent?null:[]}for(let e in this.marks){let t=this.marks[e],n=t.spec.excludes;t.excluded=null==n?[t]:""==n?[]:P(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,i){if("string"==typeof e)e=this.nodeType(e);else if(e instanceof j){if(e.schema!=this)throw RangeError("Node type from different schema used ("+e.name+")")}else throw RangeError("Invalid node type: "+e);return e.createChecked(t,n,i)}text(e,t){let n=this.nodes.text;return new A(n,n.defaultAttrs,e,l.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return S.fromJSON(this,e)}markFromJSON(e){return l.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw RangeError("Unknown node type: "+e);return t}}function P(e,t){let n=[];for(let i=0;i<t.length;i++){let r=t[i],o=e.marks[r],a=o;if(o)n.push(o);else for(let t in e.marks){let i=e.marks[t];("_"==r||i.spec.group&&i.spec.group.split(" ").indexOf(r)>-1)&&n.push(a=i)}if(!a)throw SyntaxError("Unknown mark type: '"+t[i]+"'")}return n}class B{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(e=>{if(null!=e.tag)this.tags.push(e);else if(null!=e.style){let t=/[^=]*/.exec(e.style)[0];0>n.indexOf(t)&&n.push(t),this.styles.push(e)}}),this.normalizeLists=!this.tags.some(t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)})}parse(e,t={}){let n=new q(this,t,!1);return n.addAll(e,l.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new q(this,t,!0);return n.addAll(e,l.none,t.from,t.to),p.maxOpen(n.finish())}matchTag(e,t,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){var i,r;let n=this.tags[o];if(i=e,r=n.tag,(i.matches||i.msMatchesSelector||i.webkitMatchesSelector||i.mozMatchesSelector).call(i,r)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,i){for(let r=i?this.styles.indexOf(i)+1:0;r<this.styles.length;r++){let i=this.styles[r],o=i.style;if(0==o.indexOf(e)&&(!i.context||n.matchesContext(i.context))&&(!(o.length>e.length)||61==o.charCodeAt(e.length)&&o.slice(e.length+1)==t)){if(i.getAttrs){let e=i.getAttrs(t);if(!1===e)continue;i.attrs=e||void 0}return i}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,i=0;for(;i<t.length;i++){let e=t[i];if((null==e.priority?50:e.priority)<n)break}t.splice(i,0,e)}for(let t in e.marks){let i=e.marks[t].spec.parseDOM;i&&i.forEach(e=>{n(e=W(e)),e.mark||e.ignore||e.clearMark||(e.mark=t)})}for(let t in e.nodes){let i=e.nodes[t].spec.parseDOM;i&&i.forEach(e=>{n(e=W(e)),e.node||e.ignore||e.mark||(e.node=t)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new B(e,B.schemaRules(e)))}}let F={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},H={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},K={ol:!0,ul:!0};function U(e,t,n){return null!=t?!!t|2*("full"===t):e&&"pre"==e.whitespace?3:-5&n}class J{constructor(e,t,n,i,r,o){this.type=e,this.attrs=t,this.marks=n,this.solid=i,this.options=o,this.content=[],this.activeMarks=l.none,this.match=r||(4&o?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(r.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let t=this.type.contentMatch,n;return(n=t.findWrapping(e.type))?(this.match=t,n):null}}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e=this.content[this.content.length-1],t;e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))&&(e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=e.withText(e.text.slice(0,e.text.length-t[0].length)))}let t=r.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(r.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!F.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class q{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let i=t.topNode,r,o=U(null,t.preserveWhitespace,0)|4*!!n;r=i?new J(i.type,i.attrs,l.none,!0,t.topMatch||i.type.contentMatch,o):n?new J(null,null,l.none,!0,null,o):new J(e.schema.topNodeType,null,l.none,!0,null,o),this.nodes=[r],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,i=this.top,r=2&i.options?"full":this.localPreserveWS||(1&i.options)>0;if("full"===r||i.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(r)n="full"!==r?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=i.content[i.content.length-1],r=e.previousSibling;(!t||r&&"BR"==r.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let i=this.localPreserveWS,r=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),a;K.hasOwnProperty(o)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&K.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let s=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(a=this.parser.matchTag(e,this,n));e:if(s?s.ignore:H.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!s||s.skip||s.closeParent){s&&s.closeParent?this.open=Math.max(0,this.open-1):s&&s.skip.nodeType&&(e=s.skip);let n,i=this.needsBlock;if(F.hasOwnProperty(o))r.content.length&&r.content[0].isInline&&this.open&&(this.open--,r=this.top),n=!0,r.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let a=s&&s.skip?t:this.readStyles(e,t);a&&this.addAll(e,a),n&&this.sync(r),this.needsBlock=i}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,s,n,!1===s.consuming?a:void 0)}this.localPreserveWS=i}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let e=0;e<this.parser.matchedStyles.length;e++){let i=this.parser.matchedStyles[e],r=n.getPropertyValue(i);if(r)for(let e;;){let n=this.parser.matchStyle(i,r,this,e);if(!n)break;if(n.ignore)return null;if(t=n.clearMark?t.filter(e=>!n.clearMark(e)):t.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)e=n;else break}}return t}addElementByRule(e,t,n,i){let r,o;if(t.node)if((o=this.parser.schema.nodes[t.node]).isLeaf)this.insertNode(o.create(t.attrs),n,"BR"==e.nodeName)||this.leafFallback(e,n);else{let e=this.enter(o,t.attrs||null,n,t.preserveWhitespace);e&&(r=!0,n=e)}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let a=this.top;if(o&&o.isLeaf)this.findInside(e);else if(i)this.addElement(e,n,i);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(e=>this.insertNode(e,n,!1));else{let i=e;"string"==typeof t.contentElement?i=e.querySelector(t.contentElement):"function"==typeof t.contentElement?i=t.contentElement(e):t.contentElement&&(i=t.contentElement),this.findAround(e,i,!0),this.addAll(i,n),this.findAround(e,i,!1)}r&&this.sync(a)&&this.open--}addAll(e,t,n,i){let r=n||0;for(let o=n?e.childNodes[n]:e.firstChild,a=null==i?null:e.childNodes[i];o!=a;o=o.nextSibling,++r)this.findAtPoint(e,r),this.addDOM(o,t);this.findAtPoint(e,r)}findPlace(e,t,n){let i,r;for(let t=this.open,o=0;t>=0;t--){let a=this.nodes[t],s=a.findWrapping(e);if(s&&(!i||i.length>s.length+o)&&(i=s,r=a,!s.length))break;if(a.solid){if(n)break;o+=2}}if(!i)return null;this.sync(r);for(let e=0;e<i.length;e++)t=this.enterInner(i[e],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let i=this.findPlace(e,t,n);if(i){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let n=l.none;for(let r of i.concat(e.marks))(t.type?t.type.allowsMarkType(r.type):V(r.type,e.type))&&(n=r.addToSet(n));return t.content.push(e.mark(n)),!0}return!1}enter(e,t,n,i){let r=this.findPlace(e.create(t),n,!1);return r&&(r=this.enterInner(e,t,n,!0,i)),r}enterInner(e,t,n,i=!1,r){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let a=U(e,r,o.options);4&o.options&&0==o.content.length&&(a|=4);let s=l.none;return n=n.filter(t=>(o.type?!o.type.allowsMarkType(t.type):!V(t.type,e))||(s=t.addToSet(s),!1)),this.nodes.push(new J(e,t,s,i,null,a)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;else this.localPreserveWS&&(this.nodes[t].options|=1);return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let i=0;i<this.find.length;i++)null==this.find[i].pos&&1==e.nodeType&&e.contains(this.find[i].node)&&t.compareDocumentPosition(this.find[i].node)&(n?2:4)&&(this.find[i].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,i=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),r=-(n?n.depth+1:0)+ +!i,o=(e,a)=>{for(;e>=0;e--){let s=t[e];if(""==s){if(e==t.length-1||0==e)continue;for(;a>=r;a--)if(o(e-1,a))return!0;return!1}{let e=a>0||0==a&&i?this.nodes[a].type:n&&a>=r?n.node(a-r).type:null;if(!e||e.name!=s&&!e.isInGroup(s))return!1;a--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let e in this.parser.schema.nodes){let t=this.parser.schema.nodes[e];if(t.isTextblock&&t.defaultAttrs)return t}}}function W(e){let t={};for(let n in e)t[n]=e[n];return t}function V(e,t){let n=t.schema.nodes;for(let i in n){let r=n[i];if(!r.allowsMarkType(e))continue;let o=[],a=e=>{o.push(e);for(let n=0;n<e.edgeCount;n++){let{type:i,next:r}=e.edge(n);if(i==t||0>o.indexOf(r)&&a(r))return!0}};if(a(r.contentMatch))return!0}}class G{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=X(t).createDocumentFragment());let i=n,r=[];return e.forEach(e=>{if(r.length||e.marks.length){let n=0,o=0;for(;n<r.length&&o<e.marks.length;){let t=e.marks[o];if(!this.marks[t.type.name]){o++;continue}if(!t.eq(r[n][0])||!1===t.type.spec.spanning)break;n++,o++}for(;n<r.length;)i=r.pop()[1];for(;o<e.marks.length;){let n=e.marks[o++],a=this.serializeMark(n,e.isInline,t);a&&(r.push([n,i]),i.appendChild(a.dom),i=a.contentDOM||a.dom)}}i.appendChild(this.serializeNodeInner(e,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:i}=Q(X(t),this.nodes[e.type.name](e),null,e.attrs);if(i){if(e.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,i)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let i=e.marks.length-1;i>=0;i--){let r=this.serializeMark(e.marks[i],e.isInline,t);r&&((r.contentDOM||r.dom).appendChild(n),n=r.dom)}return n}serializeMark(e,t,n={}){let i=this.marks[e.type.name];return i&&Q(X(n),i(e,t),null,e.attrs)}static renderSpec(e,t,n=null,i){return Q(e,t,n,i)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new G(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=Z(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return Z(e.marks)}}function Z(e){let t={};for(let n in e){let i=e[n].spec.toDOM;i&&(t[n]=i)}return t}function X(e){return e.document||window.document}let Y=new WeakMap;function Q(e,t,n,i){let r,o,a;if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let s=t[0],l;if("string"!=typeof s)throw RangeError("Invalid array passed to renderSpec");if(i&&(void 0===(o=Y.get(i))&&Y.set(i,(a=null,!function e(t){if(t&&"object"==typeof t)if(Array.isArray(t))if("string"==typeof t[0])a||(a=[]),a.push(t);else for(let n=0;n<t.length;n++)e(t[n]);else for(let n in t)e(t[n])}(i),o=a)),l=o)&&l.indexOf(t)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let c=s.indexOf(" ");c>0&&(n=s.slice(0,c),s=s.slice(c+1));let p=n?e.createElementNS(n,s):e.createElement(s),d=t[1],h=1;if(d&&"object"==typeof d&&null==d.nodeType&&!Array.isArray(d)){for(let e in h=2,d)if(null!=d[e]){let t=e.indexOf(" ");t>0?p.setAttributeNS(e.slice(0,t),e.slice(t+1),d[e]):p.setAttribute(e,d[e])}}for(let o=h;o<t.length;o++){let a=t[o];if(0===a){if(o<t.length-1||o>h)throw RangeError("Content hole must be the only child of its parent node");return{dom:p,contentDOM:p}}{let{dom:t,contentDOM:o}=Q(e,a,n,i);if(p.appendChild(t),o){if(r)throw RangeError("Multiple content holes");r=o}}}return{dom:p,contentDOM:r}}},192:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>ed});var i,r,o=n(4701),a=n(2571),s=n(156),l=n(2695),c=n(6770),p=n(808);if("undefined"!=typeof WeakMap){let e=new WeakMap;i=t=>e.get(t),r=(t,n)=>(e.set(t,n),n)}else{let e=[],t=0;i=t=>{for(let n=0;n<e.length;n+=2)if(e[n]==t)return e[n+1]},r=(n,i)=>(10==t&&(t=0),e[t++]=n,e[t++]=i)}var d=class{constructor(e,t,n,i){this.width=e,this.height=t,this.map=n,this.problems=i}findCell(e){for(let t=0;t<this.map.length;t++){let n=this.map[t];if(n!=e)continue;let i=t%this.width,r=t/this.width|0,o=i+1,a=r+1;for(let e=1;o<this.width&&this.map[t+e]==n;e++)o++;for(let e=1;a<this.height&&this.map[t+this.width*e]==n;e++)a++;return{left:i,top:r,right:o,bottom:a}}throw RangeError(`No cell with offset ${e} found`)}colCount(e){for(let t=0;t<this.map.length;t++)if(this.map[t]==e)return t%this.width;throw RangeError(`No cell with offset ${e} found`)}nextCell(e,t,n){let{left:i,right:r,top:o,bottom:a}=this.findCell(e);return"horiz"==t?(n<0?0==i:r==this.width)?null:this.map[o*this.width+(n<0?i-1:r)]:(n<0?0==o:a==this.height)?null:this.map[i+this.width*(n<0?o-1:a)]}rectBetween(e,t){let{left:n,right:i,top:r,bottom:o}=this.findCell(e),{left:a,right:s,top:l,bottom:c}=this.findCell(t);return{left:Math.min(n,a),top:Math.min(r,l),right:Math.max(i,s),bottom:Math.max(o,c)}}cellsInRect(e){let t=[],n={};for(let i=e.top;i<e.bottom;i++)for(let r=e.left;r<e.right;r++){let o=i*this.width+r,a=this.map[o];!n[a]&&(n[a]=!0,r==e.left&&r&&this.map[o-1]==a||i==e.top&&i&&this.map[o-this.width]==a||t.push(a))}return t}positionAt(e,t,n){for(let i=0,r=0;;i++){let o=r+n.child(i).nodeSize;if(i==e){let n=t+e*this.width,i=(e+1)*this.width;for(;n<i&&this.map[n]<r;)n++;return n==i?o-1:this.map[n]}r=o}}static get(e){return i(e)||r(e,function(e){if("table"!=e.type.spec.tableRole)throw RangeError("Not a table node: "+e.type.name);let t=function(e){let t=-1,n=!1;for(let i=0;i<e.childCount;i++){let r=e.child(i),o=0;if(n)for(let t=0;t<i;t++){let n=e.child(t);for(let e=0;e<n.childCount;e++){let r=n.child(e);t+r.attrs.rowspan>i&&(o+=r.attrs.colspan)}}for(let e=0;e<r.childCount;e++){let t=r.child(e);o+=t.attrs.colspan,t.attrs.rowspan>1&&(n=!0)}-1==t?t=o:t!=o&&(t=Math.max(t,o))}return t}(e),n=e.childCount,i=[],r=0,o=null,a=[];for(let e=0,r=t*n;e<r;e++)i[e]=0;for(let s=0,l=0;s<n;s++){let c=e.child(s);l++;for(let e=0;;e++){for(;r<i.length&&0!=i[r];)r++;if(e==c.childCount)break;let p=c.child(e),{colspan:d,rowspan:h,colwidth:u}=p.attrs;for(let e=0;e<h;e++){if(e+s>=n){(o||(o=[])).push({type:"overlong_rowspan",pos:l,n:h-e});break}let c=r+e*t;for(let e=0;e<d;e++){0==i[c+e]?i[c+e]=l:(o||(o=[])).push({type:"collision",row:s,pos:l,n:d-e});let n=u&&u[e];if(n){let i=(c+e)%t*2,r=a[i];null==r||r!=n&&1==a[i+1]?(a[i]=n,a[i+1]=1):r==n&&a[i+1]++}}}r+=d,l+=p.nodeSize}let p=(s+1)*t,d=0;for(;r<p;)0==i[r++]&&d++;d&&(o||(o=[])).push({type:"missing",row:s,n:d}),l++}(0===t||0===n)&&(o||(o=[])).push({type:"zero_sized"});let s=new d(t,n,i,o),l=!1;for(let e=0;!l&&e<a.length;e+=2)null!=a[e]&&a[e+1]<n&&(l=!0);return l&&function(e,t,n){e.problems||(e.problems=[]);let i={};for(let r=0;r<e.map.length;r++){let o=e.map[r];if(i[o])continue;i[o]=!0;let a=n.nodeAt(o);if(!a)throw RangeError(`No cell with offset ${o} found`);let s=null,l=a.attrs;for(let n=0;n<l.colspan;n++){let i=t[2*((r+n)%e.width)];null==i||l.colwidth&&l.colwidth[n]==i||((s||(s=function(e){if(e.colwidth)return e.colwidth.slice();let t=[];for(let n=0;n<e.colspan;n++)t.push(0);return t}(l)))[n]=i)}s&&e.problems.unshift({type:"colwidth mismatch",pos:o,colwidth:s})}}(s,a,e),s}(e))}};function h(e){let t=e.cached.tableNodeTypes;if(!t)for(let n in t=e.cached.tableNodeTypes={},e.nodes){let i=e.nodes[n],r=i.spec.tableRole;r&&(t[r]=i)}return t}var u=new a.hs("selectingCells");function f(e){for(let t=e.depth-1;t>0;t--)if("row"==e.node(t).type.spec.tableRole)return e.node(0).resolve(e.before(t+1));return null}function m(e){let t=e.selection.$head;for(let e=t.depth;e>0;e--)if("row"==t.node(e).type.spec.tableRole)return!0;return!1}function g(e){let t=e.selection;if("$anchorCell"in t&&t.$anchorCell)return t.$anchorCell.pos>t.$headCell.pos?t.$anchorCell:t.$headCell;if("node"in t&&t.node&&"cell"==t.node.type.spec.tableRole)return t.$anchor;let n=f(t.$head)||function(e){for(let t=e.nodeAfter,n=e.pos;t;t=t.firstChild,n++){let i=t.type.spec.tableRole;if("cell"==i||"header_cell"==i)return e.doc.resolve(n)}for(let t=e.nodeBefore,n=e.pos;t;t=t.lastChild,n--){let i=t.type.spec.tableRole;if("cell"==i||"header_cell"==i)return e.doc.resolve(n-t.nodeSize)}}(t.$head);if(n)return n;throw RangeError(`No cell found around position ${t.head}`)}function v(e){return"row"==e.parent.type.spec.tableRole&&!!e.nodeAfter}function b(e,t){return e.depth==t.depth&&e.pos>=t.start(-1)&&e.pos<=t.end(-1)}function y(e,t,n){let i=e.node(-1),r=d.get(i),o=e.start(-1),a=r.nextCell(e.pos-o,t,n);return null==a?null:e.node(0).resolve(o+a)}function x(e,t,n=1){let i={...e,colspan:e.colspan-n};return i.colwidth&&(i.colwidth=i.colwidth.slice(),i.colwidth.splice(t,n),i.colwidth.some(e=>e>0)||(i.colwidth=null)),i}function w(e,t,n=1){let i={...e,colspan:e.colspan+n};if(i.colwidth){i.colwidth=i.colwidth.slice();for(let e=0;e<n;e++)i.colwidth.splice(t,0,0)}return i}var k=class e extends a.LN{constructor(e,t=e){let n=e.node(-1),i=d.get(n),r=e.start(-1),o=i.rectBetween(e.pos-r,t.pos-r),s=e.node(0),l=i.cellsInRect(o).filter(e=>e!=t.pos-r);l.unshift(t.pos-r);let c=l.map(e=>{let t=n.nodeAt(e);if(!t)throw RangeError(`No cell with offset ${e} found`);let i=r+e+1;return new a.yn(s.resolve(i),s.resolve(i+t.content.size))});super(c[0].$from,c[0].$to,c),this.$anchorCell=e,this.$headCell=t}map(t,n){let i=t.resolve(n.map(this.$anchorCell.pos)),r=t.resolve(n.map(this.$headCell.pos));if(v(i)&&v(r)&&b(i,r)){let t=this.$anchorCell.node(-1)!=i.node(-1);return t&&this.isRowSelection()?e.rowSelection(i,r):t&&this.isColSelection()?e.colSelection(i,r):new e(i,r)}return a.U3.between(i,r)}content(){let e=this.$anchorCell.node(-1),t=d.get(e),n=this.$anchorCell.start(-1),i=t.rectBetween(this.$anchorCell.pos-n,this.$headCell.pos-n),r={},o=[];for(let n=i.top;n<i.bottom;n++){let a=[];for(let o=n*t.width+i.left,s=i.left;s<i.right;s++,o++){let n=t.map[o];if(r[n])continue;r[n]=!0;let s=t.findCell(n),l=e.nodeAt(n);if(!l)throw RangeError(`No cell with offset ${n} found`);let c=i.left-s.left,p=s.right-i.right;if(c>0||p>0){let e=l.attrs;if(c>0&&(e=x(e,0,c)),p>0&&(e=x(e,e.colspan-p,p)),s.left<i.left){if(!(l=l.type.createAndFill(e)))throw RangeError(`Could not create cell with attrs ${JSON.stringify(e)}`)}else l=l.type.create(e,l.content)}if(s.top<i.top||s.bottom>i.bottom){let e={...l.attrs,rowspan:Math.min(s.bottom,i.bottom)-Math.max(s.top,i.top)};l=s.top<i.top?l.type.createAndFill(e):l.type.create(e,l.content)}a.push(l)}o.push(e.child(n).copy(s.FK.from(a)))}let a=this.isColSelection()&&this.isRowSelection()?e:o;return new s.Ji(s.FK.from(a),1,1)}replace(e,t=s.Ji.empty){let n=e.steps.length,i=this.ranges;for(let r=0;r<i.length;r++){let{$from:o,$to:a}=i[r],l=e.mapping.slice(n);e.replace(l.map(o.pos),l.map(a.pos),r?s.Ji.empty:t)}let r=a.LN.findFrom(e.doc.resolve(e.mapping.slice(n).map(this.to)),-1);r&&e.setSelection(r)}replaceWith(e,t){this.replace(e,new s.Ji(s.FK.from(t),0,0))}forEachCell(e){let t=this.$anchorCell.node(-1),n=d.get(t),i=this.$anchorCell.start(-1),r=n.cellsInRect(n.rectBetween(this.$anchorCell.pos-i,this.$headCell.pos-i));for(let n=0;n<r.length;n++)e(t.nodeAt(r[n]),i+r[n])}isColSelection(){let e=this.$anchorCell.index(-1),t=this.$headCell.index(-1);return!(Math.min(e,t)>0)&&Math.max(e+this.$anchorCell.nodeAfter.attrs.rowspan,t+this.$headCell.nodeAfter.attrs.rowspan)==this.$headCell.node(-1).childCount}static colSelection(t,n=t){let i=t.node(-1),r=d.get(i),o=t.start(-1),a=r.findCell(t.pos-o),s=r.findCell(n.pos-o),l=t.node(0);return a.top<=s.top?(a.top>0&&(t=l.resolve(o+r.map[a.left])),s.bottom<r.height&&(n=l.resolve(o+r.map[r.width*(r.height-1)+s.right-1]))):(s.top>0&&(n=l.resolve(o+r.map[s.left])),a.bottom<r.height&&(t=l.resolve(o+r.map[r.width*(r.height-1)+a.right-1]))),new e(t,n)}isRowSelection(){let e=this.$anchorCell.node(-1),t=d.get(e),n=this.$anchorCell.start(-1),i=t.colCount(this.$anchorCell.pos-n),r=t.colCount(this.$headCell.pos-n);return!(Math.min(i,r)>0)&&Math.max(i+this.$anchorCell.nodeAfter.attrs.colspan,r+this.$headCell.nodeAfter.attrs.colspan)==t.width}eq(t){return t instanceof e&&t.$anchorCell.pos==this.$anchorCell.pos&&t.$headCell.pos==this.$headCell.pos}static rowSelection(t,n=t){let i=t.node(-1),r=d.get(i),o=t.start(-1),a=r.findCell(t.pos-o),s=r.findCell(n.pos-o),l=t.node(0);return a.left<=s.left?(a.left>0&&(t=l.resolve(o+r.map[a.top*r.width])),s.right<r.width&&(n=l.resolve(o+r.map[r.width*(s.top+1)-1]))):(s.left>0&&(n=l.resolve(o+r.map[s.top*r.width])),a.right<r.width&&(t=l.resolve(o+r.map[r.width*(a.top+1)-1]))),new e(t,n)}toJSON(){return{type:"cell",anchor:this.$anchorCell.pos,head:this.$headCell.pos}}static fromJSON(t,n){return new e(t.resolve(n.anchor),t.resolve(n.head))}static create(t,n,i=n){return new e(t.resolve(n),t.resolve(i))}getBookmark(){return new S(this.$anchorCell.pos,this.$headCell.pos)}};k.prototype.visible=!1,a.LN.jsonID("cell",k);var S=class e{constructor(e,t){this.anchor=e,this.head=t}map(t){return new e(t.map(this.anchor),t.map(this.head))}resolve(e){let t=e.resolve(this.anchor),n=e.resolve(this.head);return"row"==t.parent.type.spec.tableRole&&"row"==n.parent.type.spec.tableRole&&t.index()<t.parent.childCount&&n.index()<n.parent.childCount&&b(t,n)?new k(t,n):a.LN.near(n,1)}};function A(e){if(!(e.selection instanceof k))return null;let t=[];return e.selection.forEachCell((e,n)=>{t.push(l.NZ.node(n,n+e.nodeSize,{class:"selectedCell"}))}),l.zF.create(e.doc,t)}var E=new a.hs("fix-tables");function M(e,t){let n,i=(t,i)=>{"table"==t.type.spec.tableRole&&(n=function(e,t,n,i){let r,o,a=d.get(t);if(!a.problems)return i;i||(i=e.tr);let s=[];for(let e=0;e<a.height;e++)s.push(0);for(let e=0;e<a.problems.length;e++){let r=a.problems[e];if("collision"==r.type){let e=t.nodeAt(r.pos);if(!e)continue;let o=e.attrs;for(let e=0;e<o.rowspan;e++)s[r.row+e]+=r.n;i.setNodeMarkup(i.mapping.map(n+1+r.pos),null,x(o,o.colspan-r.n,r.n))}else if("missing"==r.type)s[r.row]+=r.n;else if("overlong_rowspan"==r.type){let e=t.nodeAt(r.pos);if(!e)continue;i.setNodeMarkup(i.mapping.map(n+1+r.pos),null,{...e.attrs,rowspan:e.attrs.rowspan-r.n})}else if("colwidth mismatch"==r.type){let e=t.nodeAt(r.pos);if(!e)continue;i.setNodeMarkup(i.mapping.map(n+1+r.pos),null,{...e.attrs,colwidth:r.colwidth})}else if("zero_sized"==r.type){let e=i.mapping.map(n);i.delete(e,e+t.nodeSize)}}for(let e=0;e<s.length;e++)s[e]&&(null==r&&(r=e),o=e);for(let l=0,c=n+1;l<a.height;l++){let n=t.child(l),a=c+n.nodeSize,p=s[l];if(p>0){let t="cell";n.firstChild&&(t=n.firstChild.type.spec.tableRole);let s=[];for(let n=0;n<p;n++){let n=h(e.schema)[t].createAndFill();n&&s.push(n)}let d=(0==l||r==l-1)&&o==l?c+1:a-1;i.insert(i.mapping.map(d),s)}c=a}return i.setMeta(E,{fixTables:!0})}(e,t,i,n))};return t?t.doc!=e.doc&&function e(t,n,i,r){let o=t.childCount,a=n.childCount;t:for(let s=0,l=0;s<a;s++){let a=n.child(s);for(let e=l,n=Math.min(o,s+3);e<n;e++)if(t.child(e)==a){l=e+1,i+=a.nodeSize;continue t}r(a,i),l<o&&t.child(l).sameMarkup(a)?e(t.child(l),a,i+1,r):a.nodesBetween(0,a.content.size,r,i+1),i+=a.nodeSize}}(t.doc,e.doc,0,i):e.doc.descendants(i),n}function C(e){let t=e.selection,n=g(e),i=n.node(-1),r=n.start(-1),o=d.get(i);return{...t instanceof k?o.rectBetween(t.$anchorCell.pos-r,t.$headCell.pos-r):o.findCell(n.pos-r),tableStart:r,map:o,table:i}}function T(e,{map:t,tableStart:n,table:i},r){let o=r>0?-1:0;(function(e,t,n){let i=h(t.type.schema).header_cell;for(let r=0;r<e.height;r++)if(t.nodeAt(e.map[n+r*e.width]).type!=i)return!1;return!0})(t,i,r+o)&&(o=0==r||r==t.width?null:0);for(let a=0;a<t.height;a++){let s=a*t.width+r;if(r>0&&r<t.width&&t.map[s-1]==t.map[s]){let o=t.map[s],l=i.nodeAt(o);e.setNodeMarkup(e.mapping.map(n+o),null,w(l.attrs,r-t.colCount(o))),a+=l.attrs.rowspan-1}else{let l=null==o?h(i.type.schema).cell:i.nodeAt(t.map[s+o]).type,c=t.positionAt(a,r,i);e.insert(e.mapping.map(n+c),l.createAndFill())}}return e}function O(e,{map:t,tableStart:n,table:i},r){var o;let a=n;for(let e=0;e<r;e++)a+=i.child(e).nodeSize;let s=[],l=r>0?-1:0;(function(e,t,n){var i;let r=h(t.type.schema).header_cell;for(let o=0;o<e.width;o++)if((null==(i=t.nodeAt(e.map[o+n*e.width]))?void 0:i.type)!=r)return!1;return!0})(t,i,r+l)&&(l=0==r||r==t.height?null:0);for(let a=0,c=t.width*r;a<t.width;a++,c++)if(r>0&&r<t.height&&t.map[c]==t.map[c-t.width]){let r=t.map[c],o=i.nodeAt(r).attrs;e.setNodeMarkup(n+r,null,{...o,rowspan:o.rowspan+1}),a+=o.colspan-1}else{let e=null==l?h(i.type.schema).cell:null==(o=i.nodeAt(t.map[c+l*t.width]))?void 0:o.type,n=null==e?void 0:e.createAndFill();n&&s.push(n)}return e.insert(a,h(i.type.schema).row.create(null,s)),e}function N(e){let t=e.content;return 1==t.childCount&&t.child(0).isTextblock&&0==t.child(0).childCount}function R(e,t){let n=e.selection;if(!(n instanceof k)||n.$anchorCell.pos==n.$headCell.pos)return!1;let i=C(e),{map:r}=i;if(function({width:e,height:t,map:n},i){let r=i.top*e+i.left,o=r,a=(i.bottom-1)*e+i.left,s=r+(i.right-i.left-1);for(let t=i.top;t<i.bottom;t++){if(i.left>0&&n[o]==n[o-1]||i.right<e&&n[s]==n[s+1])return!0;o+=e,s+=e}for(let o=i.left;o<i.right;o++){if(i.top>0&&n[r]==n[r-e]||i.bottom<t&&n[a]==n[a+e])return!0;r++,a++}return!1}(r,i))return!1;if(t){let n,o,a=e.tr,l={},c=s.FK.empty;for(let e=i.top;e<i.bottom;e++)for(let t=i.left;t<i.right;t++){let s=r.map[e*r.width+t],p=i.table.nodeAt(s);if(!l[s]&&p)if(l[s]=!0,null==n)n=s,o=p;else{N(p)||(c=c.append(p.content));let e=a.mapping.map(s+i.tableStart);a.delete(e,e+p.nodeSize)}}if(null==n||null==o)return!0;if(a.setNodeMarkup(n+i.tableStart,null,{...w(o.attrs,o.attrs.colspan,i.right-i.left-o.attrs.colspan),rowspan:i.bottom-i.top}),c.size){let e=n+1+o.content.size,t=N(o)?n+1:e;a.replaceWith(t+i.tableStart,e+i.tableStart,c)}a.setSelection(new k(a.doc.resolve(n+i.tableStart))),t(a)}return!0}function L(e,t){var n;let i=h(e.schema);return(n=({node:e})=>i[e.type.spec.tableRole],(e,t)=>{var i;let r,o,a=e.selection;if(a instanceof k){if(a.$anchorCell.pos!=a.$headCell.pos)return!1;r=a.$anchorCell.nodeAfter,o=a.$anchorCell.pos}else{if(!(r=function(e){for(let t=e.depth;t>0;t--){let n=e.node(t).type.spec.tableRole;if("cell"===n||"header_cell"===n)return e.node(t)}return null}(a.$from)))return!1;o=null==(i=f(a.$from))?void 0:i.pos}if(null==r||null==o||1==r.attrs.colspan&&1==r.attrs.rowspan)return!1;if(t){let i,s=r.attrs,l=[],c=s.colwidth;s.rowspan>1&&(s={...s,rowspan:1}),s.colspan>1&&(s={...s,colspan:1});let p=C(e),d=e.tr;for(let e=0;e<p.right-p.left;e++)l.push(c?{...s,colwidth:c&&c[e]?[c[e]]:null}:s);for(let e=p.top;e<p.bottom;e++){let t=p.map.positionAt(e,p.left,p.table);e==p.top&&(t+=r.nodeSize);for(let o=p.left,a=0;o<p.right;o++,a++)(o!=p.left||e!=p.top)&&d.insert(i=d.mapping.map(t+p.tableStart,1),n({node:r,row:e,col:o}).createAndFill(l[a]))}d.setNodeMarkup(o,n({node:r,row:p.top,col:p.left}),l[0]),a instanceof k&&d.setSelection(new k(d.doc.resolve(a.$anchorCell.pos),i?d.doc.resolve(i):void 0)),t(d)}return!0})(e,t)}function z(e,t,n){let i=t.map.cellsInRect({left:0,top:0,right:"row"==e?t.map.width:1,bottom:"column"==e?t.map.height:1});for(let e=0;e<i.length;e++){let r=t.table.nodeAt(i[e]);if(r&&r.type!==n.header_cell)return!1}return!0}function I(e,t){if((t=t||{useDeprecatedLogic:!1}).useDeprecatedLogic)return function(t,n){if(!m(t))return!1;if(n){let i=h(t.schema),r=C(t),o=t.tr,a=r.map.cellsInRect("column"==e?{left:r.left,top:0,right:r.right,bottom:r.map.height}:"row"==e?{left:0,top:r.top,right:r.map.width,bottom:r.bottom}:r),s=a.map(e=>r.table.nodeAt(e));for(let e=0;e<a.length;e++)s[e].type==i.header_cell&&o.setNodeMarkup(r.tableStart+a[e],i.cell,s[e].attrs);if(0==o.steps.length)for(let e=0;e<a.length;e++)o.setNodeMarkup(r.tableStart+a[e],i.header_cell,s[e].attrs);n(o)}return!0};return function(t,n){if(!m(t))return!1;if(n){let i=h(t.schema),r=C(t),o=t.tr,a=z("row",r,i),s=z("column",r,i),l=+!!("column"===e?a:"row"===e&&s),c="column"==e?{left:0,top:l,right:1,bottom:r.map.height}:"row"==e?{left:l,top:0,right:r.map.width,bottom:1}:r,p="column"==e?s?i.cell:i.header_cell:"row"==e?a?i.cell:i.header_cell:i.cell;r.map.cellsInRect(c).forEach(e=>{let t=e+r.tableStart,n=o.doc.nodeAt(t);n&&o.setNodeMarkup(t,p,n.attrs)}),n(o)}return!0}}I("row",{useDeprecatedLogic:!0}),I("column",{useDeprecatedLogic:!0});var j=I("cell",{useDeprecatedLogic:!0});function _(e){return function(t,n){if(!m(t))return!1;let i=function(e,t){if(t<0){let t=e.nodeBefore;if(t)return e.pos-t.nodeSize;for(let t=e.index(-1)-1,n=e.before();t>=0;t--){let i=e.node(-1).child(t),r=i.lastChild;if(r)return n-1-r.nodeSize;n-=i.nodeSize}}else{if(e.index()<e.parent.childCount-1)return e.pos+e.nodeAfter.nodeSize;let t=e.node(-1);for(let n=e.indexAfter(-1),i=e.after();n<t.childCount;n++){let e=t.child(n);if(e.childCount)return i+1;i+=e.nodeSize}}return null}(g(t),e);if(null==i)return!1;if(n){let e=t.doc.resolve(i);n(t.tr.setSelection(a.U3.between(e,e.node(0).resolve(e.pos+e.nodeAfter.nodeSize))).scrollIntoView())}return!0}}function D(e,t){let n=e.selection;if(!(n instanceof k))return!1;if(t){let i=e.tr,r=h(e.schema).cell.createAndFill().content;n.forEachCell((e,t)=>{e.content.eq(r)||i.replace(i.mapping.map(t+1),i.mapping.map(t+e.nodeSize-1),new s.Ji(r,0,0))}),i.docChanged&&t(i)}return!0}function $(e,t){let n=e.createAndFill();return new p.dL(n).replace(0,n.content.size,t).doc}function P(e,t,n,i,r,o,a,s){if(0==a||a==t.height)return!1;let l=!1;for(let c=r;c<o;c++){let r=a*t.width+c,o=t.map[r];if(t.map[r-t.width]==o){l=!0;let r=n.nodeAt(o),{top:p,left:d}=t.findCell(o);e.setNodeMarkup(e.mapping.slice(s).map(o+i),null,{...r.attrs,rowspan:a-p}),e.insert(e.mapping.slice(s).map(t.positionAt(a,d,n)),r.type.createAndFill({...r.attrs,rowspan:p+r.attrs.rowspan-a})),c+=r.attrs.colspan-1}}return l}function B(e,t,n,i,r,o,a,s){if(0==a||a==t.width)return!1;let l=!1;for(let c=r;c<o;c++){let r=c*t.width+a,o=t.map[r];if(t.map[r-1]==o){l=!0;let r=n.nodeAt(o),p=t.colCount(o),d=e.mapping.slice(s).map(o+i);e.setNodeMarkup(d,null,x(r.attrs,a-p,r.attrs.colspan-(a-p))),e.insert(d+r.nodeSize,r.type.createAndFill(x(r.attrs,0,a-p))),c+=r.attrs.rowspan-1}}return l}function F(e,t,n,i,r){let o=n?e.doc.nodeAt(n-1):e.doc;if(!o)throw Error("No table found");let a=d.get(o),{top:l,left:c}=i,p=c+r.width,u=l+r.height,f=e.tr,m=0;function g(){if(!(o=n?f.doc.nodeAt(n-1):f.doc))throw Error("No table found");a=d.get(o),m=f.mapping.maps.length}(function(e,t,n,i,r,o,a){let l,c,p=h(e.doc.type.schema);if(r>t.width)for(let o=0,a=0;o<t.height;o++){let s,d=n.child(o);a+=d.nodeSize;let h=[];s=null==d.lastChild||d.lastChild.type==p.cell?l||(l=p.cell.createAndFill()):c||(c=p.header_cell.createAndFill());for(let e=t.width;e<r;e++)h.push(s);e.insert(e.mapping.slice(0).map(a-1+i),h)}if(o>t.height){let d=[];for(let e=0,i=(t.height-1)*t.width;e<Math.max(t.width,r);e++){let r=!(e>=t.width)&&n.nodeAt(t.map[i+e]).type==p.header_cell;d.push(r?c||(c=p.header_cell.createAndFill()):l||(l=p.cell.createAndFill()))}let h=p.row.create(null,s.FK.from(d)),u=[];for(let e=t.height;e<o;e++)u.push(h);e.insert(e.mapping.slice(a).map(i+n.nodeSize-2),u)}return!!(l||c)})(f,a,o,n,p,u,0)&&g(),P(f,a,o,n,c,p,l,m)&&g(),P(f,a,o,n,c,p,u,m)&&g(),B(f,a,o,n,l,u,c,m)&&g(),B(f,a,o,n,l,u,p,m)&&g();for(let e=l;e<u;e++){let t=a.positionAt(e,c,o),i=a.positionAt(e,p,o);f.replace(f.mapping.slice(m).map(t+n),f.mapping.slice(m).map(i+n),new s.Ji(r.rows[e-l],0,0))}g(),f.setSelection(new k(f.doc.resolve(n+a.positionAt(l,c,o)),f.doc.resolve(n+a.positionAt(u-1,p-1,o)))),t(f)}var H=(0,c.K)({ArrowLeft:U("horiz",-1),ArrowRight:U("horiz",1),ArrowUp:U("vert",-1),ArrowDown:U("vert",1),"Shift-ArrowLeft":J("horiz",-1),"Shift-ArrowRight":J("horiz",1),"Shift-ArrowUp":J("vert",-1),"Shift-ArrowDown":J("vert",1),Backspace:D,"Mod-Backspace":D,Delete:D,"Mod-Delete":D});function K(e,t,n){return!n.eq(e.selection)&&(t&&t(e.tr.setSelection(n).scrollIntoView()),!0)}function U(e,t){return(n,i,r)=>{if(!r)return!1;let o=n.selection;if(o instanceof k)return K(n,i,a.LN.near(o.$headCell,t));if("horiz"!=e&&!o.empty)return!1;let s=G(r,e,t);if(null==s)return!1;if("horiz"==e)return K(n,i,a.LN.near(n.doc.resolve(o.head+t),t));{let r,o=n.doc.resolve(s),l=y(o,e,t);return r=l?a.LN.near(l,1):t<0?a.LN.near(n.doc.resolve(o.before(-1)),-1):a.LN.near(n.doc.resolve(o.after(-1)),1),K(n,i,r)}}}function J(e,t){return(n,i,r)=>{let o;if(!r)return!1;let a=n.selection;if(a instanceof k)o=a;else{let i=G(r,e,t);if(null==i)return!1;o=new k(n.doc.resolve(i))}let s=y(o.$headCell,e,t);return!!s&&K(n,i,new k(o.$anchorCell,s))}}function q(e,t){let n=f(e.state.doc.resolve(t));return!!n&&(e.dispatch(e.state.tr.setSelection(new k(n))),!0)}function W(e,t,n){if(!m(e.state))return!1;let i=function(e){if(!e.size)return null;let{content:t,openStart:n,openEnd:i}=e;for(;1==t.childCount&&(n>0&&i>0||"table"==t.child(0).type.spec.tableRole);)n--,i--,t=t.child(0).content;let r=t.child(0),o=r.type.spec.tableRole,a=r.type.schema,l=[];if("row"==o)for(let e=0;e<t.childCount;e++){let r=t.child(e).content,o=e?0:Math.max(0,n-1),c=e<t.childCount-1?0:Math.max(0,i-1);(o||c)&&(r=$(h(a).row,new s.Ji(r,o,c)).content),l.push(r)}else{if("cell"!=o&&"header_cell"!=o)return null;l.push(n||i?$(h(a).row,new s.Ji(t,n,i)).content:t)}return function(e,t){let n=[];for(let e=0;e<t.length;e++){let i=t[e];for(let t=i.childCount-1;t>=0;t--){let{rowspan:r,colspan:o}=i.child(t).attrs;for(let t=e;t<e+r;t++)n[t]=(n[t]||0)+o}}let i=0;for(let e=0;e<n.length;e++)i=Math.max(i,n[e]);for(let r=0;r<n.length;r++)if(r>=t.length&&t.push(s.FK.empty),n[r]<i){let o=h(e).cell.createAndFill(),a=[];for(let e=n[r];e<i;e++)a.push(o);t[r]=t[r].append(s.FK.from(a))}return{height:t.length,width:i,rows:t}}(a,l)}(n),r=e.state.selection;if(r instanceof k){i||(i={width:1,height:1,rows:[s.FK.from($(h(e.state.schema).cell,n))]});let t=r.$anchorCell.node(-1),o=r.$anchorCell.start(-1),a=d.get(t).rectBetween(r.$anchorCell.pos-o,r.$headCell.pos-o);return i=function({width:e,height:t,rows:n},i,r){if(e!=i){let t=[],r=[];for(let e=0;e<n.length;e++){let o=n[e],a=[];for(let n=t[e]||0,r=0;n<i;r++){let s=o.child(r%o.childCount);n+s.attrs.colspan>i&&(s=s.type.createChecked(x(s.attrs,s.attrs.colspan,n+s.attrs.colspan-i),s.content)),a.push(s),n+=s.attrs.colspan;for(let n=1;n<s.attrs.rowspan;n++)t[e+n]=(t[e+n]||0)+s.attrs.colspan}r.push(s.FK.from(a))}n=r,e=i}if(t!=r){let e=[];for(let i=0,o=0;i<r;i++,o++){let a=[],l=n[o%t];for(let e=0;e<l.childCount;e++){let t=l.child(e);i+t.attrs.rowspan>r&&(t=t.type.create({...t.attrs,rowspan:Math.max(1,r-t.attrs.rowspan)},t.content)),a.push(t)}e.push(s.FK.from(a))}n=e,t=r}return{width:e,height:t,rows:n}}(i,a.right-a.left,a.bottom-a.top),F(e.state,e.dispatch,o,a,i),!0}if(!i)return!1;{let t=g(e.state),n=t.start(-1);return F(e.state,e.dispatch,n,d.get(t.node(-1)).findCell(t.pos-n),i),!0}}function V(e,t){var n;let i;if(t.ctrlKey||t.metaKey)return;let r=Z(e,t.target);if(t.shiftKey&&e.state.selection instanceof k)o(e.state.selection.$anchorCell,t),t.preventDefault();else if(t.shiftKey&&r&&null!=(i=f(e.state.selection.$anchor))&&(null==(n=X(e,t))?void 0:n.pos)!=i.pos)o(i,t),t.preventDefault();else if(!r)return;function o(t,n){let i=X(e,n),r=null==u.getState(e.state);if(!i||!b(t,i))if(!r)return;else i=t;let o=new k(t,i);if(r||!e.state.selection.eq(o)){let n=e.state.tr.setSelection(o);r&&n.setMeta(u,t.pos),e.dispatch(n)}}function a(){e.root.removeEventListener("mouseup",a),e.root.removeEventListener("dragstart",a),e.root.removeEventListener("mousemove",s),null!=u.getState(e.state)&&e.dispatch(e.state.tr.setMeta(u,-1))}function s(n){let i,s=u.getState(e.state);if(null!=s)i=e.state.doc.resolve(s);else if(Z(e,n.target)!=r&&!(i=X(e,t)))return a();i&&o(i,n)}e.root.addEventListener("mouseup",a),e.root.addEventListener("dragstart",a),e.root.addEventListener("mousemove",s)}function G(e,t,n){if(!(e.state.selection instanceof a.U3))return null;let{$head:i}=e.state.selection;for(let r=i.depth-1;r>=0;r--){let o=i.node(r);if((n<0?i.index(r):i.indexAfter(r))!=(n<0?0:o.childCount))break;if("cell"==o.type.spec.tableRole||"header_cell"==o.type.spec.tableRole){let o=i.before(r),a="vert"==t?n>0?"down":"up":n>0?"right":"left";return e.endOfTextblock(a)?o:null}}return null}function Z(e,t){for(;t&&t!=e.dom;t=t.parentNode)if("TD"==t.nodeName||"TH"==t.nodeName)return t;return null}function X(e,t){let n=e.posAtCoords({left:t.clientX,top:t.clientY});return n&&n?f(e.state.doc.resolve(n.pos)):null}var Y=class{constructor(e,t){this.node=e,this.defaultCellMinWidth=t,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.table.style.setProperty("--default-cell-min-width",`${t}px`),this.colgroup=this.table.appendChild(document.createElement("colgroup")),Q(e,this.colgroup,this.table,t),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(e){return e.type==this.node.type&&(this.node=e,Q(e,this.colgroup,this.table,this.defaultCellMinWidth),!0)}ignoreMutation(e){return"attributes"==e.type&&(e.target==this.table||this.colgroup.contains(e.target))}};function Q(e,t,n,i,r,o){var a;let s=0,l=!0,c=t.firstChild,p=e.firstChild;if(p){for(let e=0,n=0;e<p.childCount;e++){let{colspan:a,colwidth:d}=p.child(e).attrs;for(let e=0;e<a;e++,n++){let a=r==n?o:d&&d[e],p=a?a+"px":"";if(s+=a||i,a||(l=!1),c)c.style.width!=p&&(c.style.width=p),c=c.nextSibling;else{let e=document.createElement("col");e.style.width=p,t.appendChild(e)}}}for(;c;){let e=c.nextSibling;null==(a=c.parentNode)||a.removeChild(c),c=e}l?(n.style.width=s+"px",n.style.minWidth=""):(n.style.width="",n.style.minWidth=s+"px")}}var ee=new a.hs("tableColumnResizing"),et=class e{constructor(e,t){this.activeHandle=e,this.dragging=t}apply(t){let n=t.getMeta(ee);if(n&&null!=n.setHandle)return new e(n.setHandle,!1);if(n&&void 0!==n.setDragging)return new e(this.activeHandle,n.setDragging);if(this.activeHandle>-1&&t.docChanged){let n=t.mapping.map(this.activeHandle,-1);return v(t.doc.resolve(n))||(n=-1),new e(n,this.dragging)}return this}};function en(e,t,n,i){let r=e.posAtCoords({left:t.clientX+("right"==n?-i:i),top:t.clientY});if(!r)return -1;let{pos:o}=r,a=f(e.state.doc.resolve(o));if(!a)return -1;if("right"==n)return a.pos;let s=d.get(a.node(-1)),l=a.start(-1),c=s.map.indexOf(a.pos-l);return c%s.width==0?-1:l+s.map[c-1]}function ei(e,t,n){let i=t.clientX-e.startX;return Math.max(n,e.startWidth+i)}function er(e,t){e.dispatch(e.state.tr.setMeta(ee,{setHandle:t}))}function eo(e,t,n,i){let r=e.state.doc.resolve(t),o=r.node(-1),a=r.start(-1),s=d.get(o).colCount(r.pos-a)+r.nodeAfter.attrs.colspan-1,l=e.domAtPos(r.start(-1)).node;for(;l&&"TABLE"!=l.nodeName;)l=l.parentNode;l&&Q(o,l.firstChild,l,i,s,n)}function ea(e,t){return t?["width",`${Math.max(t,e)}px`]:["min-width",`${e}px`]}function es(e,t,n,i,r,o){var a;let s=0,l=!0,c=t.firstChild,p=e.firstChild;if(null!==p)for(let e=0,n=0;e<p.childCount;e+=1){let{colspan:a,colwidth:d}=p.child(e).attrs;for(let e=0;e<a;e+=1,n+=1){let a=r===n?o:d&&d[e],p=a?`${a}px`:"";if(s+=a||i,a||(l=!1),c){if(c.style.width!==p){let[e,t]=ea(i,a);c.style.setProperty(e,t)}c=c.nextSibling}else{let e=document.createElement("col"),[n,r]=ea(i,a);e.style.setProperty(n,r),t.appendChild(e)}}}for(;c;){let e=c.nextSibling;null==(a=c.parentNode)||a.removeChild(c),c=e}l?(n.style.width=`${s}px`,n.style.minWidth=""):(n.style.width="",n.style.minWidth=`${s}px`)}class el{constructor(e,t){this.node=e,this.cellMinWidth=t,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.colgroup=this.table.appendChild(document.createElement("colgroup")),es(e,this.colgroup,this.table,t),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(e){return e.type===this.node.type&&(this.node=e,es(e,this.colgroup,this.table,this.cellMinWidth),!0)}ignoreMutation(e){return"attributes"===e.type&&(e.target===this.table||this.colgroup.contains(e.target))}}function ec(e,t){return t?e.createChecked(null,t):e.createAndFill()}let ep=({editor:e})=>{let{selection:t}=e.state;if(!(t instanceof k))return!1;let n=0,i=(0,o.eL)(t.ranges[0].$from,e=>"table"===e.type.name);return null==i||i.node.descendants(e=>{if("table"===e.type.name)return!1;["tableCell","tableHeader"].includes(e.type.name)&&(n+=1)}),n===t.ranges.length&&(e.commands.deleteTable(),!0)},ed=o.bP.create({name:"table",addOptions:()=>({HTMLAttributes:{},resizable:!1,handleWidth:5,cellMinWidth:25,View:el,lastColumnResizable:!0,allowTableNodeSelection:!1}),content:"tableRow+",tableRole:"table",isolating:!0,group:"block",parseHTML:()=>[{tag:"table"}],renderHTML({node:e,HTMLAttributes:t}){let{colgroup:n,tableWidth:i,tableMinWidth:r}=function(e,t,n,i){let r=0,o=!0,a=[],s=e.firstChild;if(!s)return{};for(let e=0,n=0;e<s.childCount;e+=1){let{colspan:i,colwidth:l}=s.child(e).attrs;for(let e=0;e<i;e+=1,n+=1){let i=void 0===n?void 0:l&&l[e];r+=i||t,i||(o=!1);let[s,c]=ea(t,i);a.push(["col",{style:`${s}: ${c}`}])}}return{colgroup:["colgroup",{},...a],tableWidth:o?`${r}px`:"",tableMinWidth:o?"":`${r}px`}}(e,this.options.cellMinWidth);return["table",(0,o.KV)(this.options.HTMLAttributes,t,{style:i?`width: ${i}`:`min-width: ${r}`}),n,["tbody",0]]},addCommands:()=>({insertTable:({rows:e=3,cols:t=3,withHeaderRow:n=!0}={})=>({tr:i,dispatch:r,editor:o})=>{let s=function(e,t,n,i,r){let o=function(e){if(e.cached.tableNodeTypes)return e.cached.tableNodeTypes;let t={};return Object.keys(e.nodes).forEach(n=>{let i=e.nodes[n];i.spec.tableRole&&(t[i.spec.tableRole]=i)}),e.cached.tableNodeTypes=t,t}(e),a=[],s=[];for(let e=0;e<n;e+=1){let e=ec(o.cell,void 0);if(e&&s.push(e),i){let e=ec(o.header_cell,void 0);e&&a.push(e)}}let l=[];for(let e=0;e<t;e+=1)l.push(o.row.createChecked(null,i&&0===e?a:s));return o.table.createChecked(null,l)}(o.schema,e,t,n);if(r){let e=i.selection.from+1;i.replaceSelectionWith(s).scrollIntoView().setSelection(a.U3.near(i.doc.resolve(e)))}return!0},addColumnBefore:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=C(e);t(T(e.tr,n,n.left))}return!0})(e,t),addColumnAfter:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=C(e);t(T(e.tr,n,n.right))}return!0})(e,t),deleteColumn:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=C(e),i=e.tr;if(0==n.left&&n.right==n.map.width)return!1;for(let e=n.right-1;!function(e,{map:t,table:n,tableStart:i},r){let o=e.mapping.maps.length;for(let a=0;a<t.height;){let s=a*t.width+r,l=t.map[s],c=n.nodeAt(l),p=c.attrs;if(r>0&&t.map[s-1]==l||r<t.width-1&&t.map[s+1]==l)e.setNodeMarkup(e.mapping.slice(o).map(i+l),null,x(p,r-t.colCount(l)));else{let t=e.mapping.slice(o).map(i+l);e.delete(t,t+c.nodeSize)}a+=p.rowspan}}(i,n,e),e!=n.left;e--){let e=n.tableStart?i.doc.nodeAt(n.tableStart-1):i.doc;if(!e)throw RangeError("No table found");n.table=e,n.map=d.get(e)}t(i)}return!0})(e,t),addRowBefore:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=C(e);t(O(e.tr,n,n.top))}return!0})(e,t),addRowAfter:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=C(e);t(O(e.tr,n,n.bottom))}return!0})(e,t),deleteRow:()=>({state:e,dispatch:t})=>(function(e,t){if(!m(e))return!1;if(t){let n=C(e),i=e.tr;if(0==n.top&&n.bottom==n.map.height)return!1;for(let e=n.bottom-1;!function(e,{map:t,table:n,tableStart:i},r){let o=0;for(let e=0;e<r;e++)o+=n.child(e).nodeSize;let a=o+n.child(r).nodeSize,s=e.mapping.maps.length;e.delete(o+i,a+i);let l=new Set;for(let o=0,a=r*t.width;o<t.width;o++,a++){let c=t.map[a];if(!l.has(c)){if(l.add(c),r>0&&c==t.map[a-t.width]){let t=n.nodeAt(c).attrs;e.setNodeMarkup(e.mapping.slice(s).map(c+i),null,{...t,rowspan:t.rowspan-1}),o+=t.colspan-1}else if(r<t.height&&c==t.map[a+t.width]){let a=n.nodeAt(c),l=a.attrs,p=a.type.create({...l,rowspan:a.attrs.rowspan-1},a.content),d=t.positionAt(r+1,o,n);e.insert(e.mapping.slice(s).map(i+d),p),o+=l.colspan-1}}}}(i,n,e),e!=n.top;e--){let e=n.tableStart?i.doc.nodeAt(n.tableStart-1):i.doc;if(!e)throw RangeError("No table found");n.table=e,n.map=d.get(n.table)}t(i)}return!0})(e,t),deleteTable:()=>({state:e,dispatch:t})=>(function(e,t){let n=e.selection.$anchor;for(let i=n.depth;i>0;i--)if("table"==n.node(i).type.spec.tableRole)return t&&t(e.tr.delete(n.before(i),n.after(i)).scrollIntoView()),!0;return!1})(e,t),mergeCells:()=>({state:e,dispatch:t})=>R(e,t),splitCell:()=>({state:e,dispatch:t})=>L(e,t),toggleHeaderColumn:()=>({state:e,dispatch:t})=>I("column")(e,t),toggleHeaderRow:()=>({state:e,dispatch:t})=>I("row")(e,t),toggleHeaderCell:()=>({state:e,dispatch:t})=>j(e,t),mergeOrSplit:()=>({state:e,dispatch:t})=>!!R(e,t)||L(e,t),setCellAttribute:(e,t)=>({state:n,dispatch:i})=>(function(e,t){return function(n,i){if(!m(n))return!1;let r=g(n);if(r.nodeAfter.attrs[e]===t)return!1;if(i){let o=n.tr;n.selection instanceof k?n.selection.forEachCell((n,i)=>{n.attrs[e]!==t&&o.setNodeMarkup(i,null,{...n.attrs,[e]:t})}):o.setNodeMarkup(r.pos,null,{...r.nodeAfter.attrs,[e]:t}),i(o)}return!0}})(e,t)(n,i),goToNextCell:()=>({state:e,dispatch:t})=>_(1)(e,t),goToPreviousCell:()=>({state:e,dispatch:t})=>_(-1)(e,t),fixTables:()=>({state:e,dispatch:t})=>(t&&M(e),!0),setCellSelection:e=>({tr:t,dispatch:n})=>{if(n){let n=k.create(t.doc,e.anchorCell,e.headCell);t.setSelection(n)}return!0}}),addKeyboardShortcuts(){return{Tab:()=>!!this.editor.commands.goToNextCell()||!!this.editor.can().addRowAfter()&&this.editor.chain().addRowAfter().goToNextCell().run(),"Shift-Tab":()=>this.editor.commands.goToPreviousCell(),Backspace:ep,"Mod-Backspace":ep,Delete:ep,"Mod-Delete":ep}},addProseMirrorPlugins(){return[...this.options.resizable&&this.editor.isEditable?[function({handleWidth:e=5,cellMinWidth:t=25,defaultCellMinWidth:n=100,View:i=Y,lastColumnResizable:r=!0}={}){let o=new a.k_({key:ee,state:{init(e,t){var r,a;let s=null==(a=null==(r=o.spec)?void 0:r.props)?void 0:a.nodeViews,l=h(t.schema).table.name;return i&&s&&(s[l]=(e,t)=>new i(e,n,t)),new et(-1,!1)},apply:(e,t)=>t.apply(e)},props:{attributes:e=>{let t=ee.getState(e);return t&&t.activeHandle>-1?{class:"resize-cursor"}:{}},handleDOMEvents:{mousemove:(t,n)=>{!function(e,t,n,i){if(!e.editable)return;let r=ee.getState(e.state);if(r&&!r.dragging){let o=function(e){for(;e&&"TD"!=e.nodeName&&"TH"!=e.nodeName;)e=e.classList&&e.classList.contains("ProseMirror")?null:e.parentNode;return e}(t.target),a=-1;if(o){let{left:i,right:r}=o.getBoundingClientRect();t.clientX-i<=n?a=en(e,t,"left",n):r-t.clientX<=n&&(a=en(e,t,"right",n))}if(a!=r.activeHandle){if(!i&&-1!==a){let t=e.state.doc.resolve(a),n=t.node(-1),i=d.get(n),r=t.start(-1);if(i.colCount(t.pos-r)+t.nodeAfter.attrs.colspan-1==i.width-1)return}er(e,a)}}}(t,n,e,r)},mouseleave:e=>{!function(e){if(!e.editable)return;let t=ee.getState(e.state);t&&t.activeHandle>-1&&!t.dragging&&er(e,-1)}(e)},mousedown:(e,i)=>{!function(e,t,n,i){var r;if(!e.editable)return;let o=null!=(r=e.dom.ownerDocument.defaultView)?r:window,a=ee.getState(e.state);if(!a||-1==a.activeHandle||a.dragging)return;let s=e.state.doc.nodeAt(a.activeHandle),l=function(e,t,{colspan:n,colwidth:i}){let r=i&&i[i.length-1];if(r)return r;let o=e.domAtPos(t),a=o.node.childNodes[o.offset].offsetWidth,s=n;if(i)for(let e=0;e<n;e++)i[e]&&(a-=i[e],s--);return a/s}(e,a.activeHandle,s.attrs);function c(t){o.removeEventListener("mouseup",c),o.removeEventListener("mousemove",p);let i=ee.getState(e.state);(null==i?void 0:i.dragging)&&(function(e,t,n){let i=e.state.doc.resolve(t),r=i.node(-1),o=d.get(r),a=i.start(-1),s=o.colCount(i.pos-a)+i.nodeAfter.attrs.colspan-1,l=e.state.tr;for(let e=0;e<o.height;e++){let t=e*o.width+s;if(e&&o.map[t]==o.map[t-o.width])continue;let i=o.map[t],c=r.nodeAt(i).attrs,p=1==c.colspan?0:s-o.colCount(i);if(c.colwidth&&c.colwidth[p]==n)continue;let d=c.colwidth?c.colwidth.slice():Array(c.colspan).fill(0);d[p]=n,l.setNodeMarkup(a+i,null,{...c,colwidth:d})}l.docChanged&&e.dispatch(l)}(e,i.activeHandle,ei(i.dragging,t,n)),e.dispatch(e.state.tr.setMeta(ee,{setDragging:null})))}function p(t){if(!t.which)return c(t);let r=ee.getState(e.state);if(r&&r.dragging){let o=ei(r.dragging,t,n);eo(e,r.activeHandle,o,i)}}e.dispatch(e.state.tr.setMeta(ee,{setDragging:{startX:t.clientX,startWidth:l}})),eo(e,a.activeHandle,l,i),o.addEventListener("mouseup",c),o.addEventListener("mousemove",p),t.preventDefault()}(e,i,t,n)}},decorations:e=>{let t=ee.getState(e);if(t&&t.activeHandle>-1)return function(e,t){var n;let i=[],r=e.doc.resolve(t),o=r.node(-1);if(!o)return l.zF.empty;let a=d.get(o),s=r.start(-1),c=a.colCount(r.pos-s)+r.nodeAfter.attrs.colspan-1;for(let t=0;t<a.height;t++){let r=c+t*a.width;if((c==a.width-1||a.map[r]!=a.map[r+1])&&(0==t||a.map[r]!=a.map[r-a.width])){let t=a.map[r],c=s+t+o.nodeAt(t).nodeSize-1,p=document.createElement("div");p.className="column-resize-handle",(null==(n=ee.getState(e))?void 0:n.dragging)&&i.push(l.NZ.node(s+t,s+t+o.nodeAt(t).nodeSize,{class:"column-resize-dragging"})),i.push(l.NZ.widget(c,p))}}return l.zF.create(e.doc,i)}(e,t.activeHandle)},nodeViews:{}}});return o}({handleWidth:this.options.handleWidth,cellMinWidth:this.options.cellMinWidth,defaultCellMinWidth:this.options.cellMinWidth,View:this.options.View,lastColumnResizable:this.options.lastColumnResizable})]:[],function({allowTableNodeSelection:e=!1}={}){return new a.k_({key:u,state:{init:()=>null,apply(e,t){let n=e.getMeta(u);if(null!=n)return -1==n?null:n;if(null==t||!e.docChanged)return t;let{deleted:i,pos:r}=e.mapping.mapResult(t);return i?null:r}},props:{decorations:A,handleDOMEvents:{mousedown:V},createSelectionBetween:e=>null!=u.getState(e.state)?e.state.selection:null,handleTripleClick:q,handleKeyDown:H,handlePaste:W},appendTransaction:(t,n,i)=>(function(e,t,n){let i,r,o=(t||e).selection,s=(t||e).doc;if(o instanceof a.nh&&(r=o.node.type.spec.tableRole)){if("cell"==r||"header_cell"==r)i=k.create(s,o.from);else if("row"==r){let e=s.resolve(o.from+1);i=k.rowSelection(e,e)}else if(!n){let e=d.get(o.node),t=o.from+1,n=t+e.map[e.width*e.height-1];i=k.create(s,t+1,n)}}else o instanceof a.U3&&function({$from:e,$to:t}){if(e.pos==t.pos||e.pos<t.pos-6)return!1;let n=e.pos,i=t.pos,r=e.depth;for(;r>=0&&!(e.after(r+1)<e.end(r));r--,n++);for(let e=t.depth;e>=0&&!(t.before(e+1)>t.start(e));e--,i--);return n==i&&/row|table/.test(e.node(r).type.spec.tableRole)}(o)?i=a.U3.create(s,o.from):o instanceof a.U3&&function({$from:e,$to:t}){let n,i;for(let t=e.depth;t>0;t--){let i=e.node(t);if("cell"===i.type.spec.tableRole||"header_cell"===i.type.spec.tableRole){n=i;break}}for(let e=t.depth;e>0;e--){let n=t.node(e);if("cell"===n.type.spec.tableRole||"header_cell"===n.type.spec.tableRole){i=n;break}}return n!==i&&0===t.parentOffset}(o)&&(i=a.U3.create(s,o.$from.start(),o.$from.end()));return i&&(t||(t=e.tr)).setSelection(i),t})(i,M(i,n),e)})}({allowTableNodeSelection:this.options.allowTableNodeSelection})]},extendNodeSchema(e){let t={name:e.name,options:e.options,storage:e.storage};return{tableRole:(0,o.gk)((0,o.iI)(e,"tableRole",t))}}})},224:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},290:(e,t,n)=>{"use strict";n.d(t,{$f:()=>R,G2:()=>x,I$:()=>E,Im:()=>z,Qv:()=>c,Sd:()=>b,Z1:()=>M,_G:()=>d,_e:()=>f,bh:()=>w,eB:()=>p,eT:()=>v,ec:()=>L,hy:()=>T,ic:()=>s,iz:()=>C,pC:()=>S,yY:()=>k,y_:()=>I});var i,r=n(808),o=n(156),a=n(2571);let s=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function l(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("backward",e):!(n.parentOffset>0))?n:null}let c=(e,t,n)=>{let i=l(e,n);if(!i)return!1;let s=m(i);if(!s){let n=i.blockRange(),o=n&&(0,r.jP)(n);return null!=o&&(t&&t(e.tr.lift(n,o).scrollIntoView()),!0)}let c=s.nodeBefore;if(O(e,s,t,-1))return!0;if(0==i.parent.content.size&&(u(c,"end")||a.nh.isSelectable(c)))for(let n=i.depth;;n--){let l=(0,r.$L)(e.doc,i.before(n),i.after(n),o.Ji.empty);if(l&&l.slice.size<l.to-l.from){if(t){let n=e.tr.step(l);n.setSelection(u(c,"end")?a.LN.findFrom(n.doc.resolve(n.mapping.map(s.pos,-1)),-1):a.nh.create(n.doc,s.pos-c.nodeSize)),t(n.scrollIntoView())}return!0}if(1==n||i.node(n-1).childCount>1)break}return!!c.isAtom&&s.depth==i.depth-1&&(t&&t(e.tr.delete(s.pos-c.nodeSize,s.pos).scrollIntoView()),!0)},p=(e,t,n)=>{let i=l(e,n);if(!i)return!1;let r=m(i);return!!r&&h(e,r,t)},d=(e,t,n)=>{let i=g(e,n);if(!i)return!1;let r=y(i);return!!r&&h(e,r,t)};function h(e,t,n){let i=t.nodeBefore,s=t.pos-1;for(;!i.isTextblock;s--){if(i.type.spec.isolating)return!1;let e=i.lastChild;if(!e)return!1;i=e}let l=t.nodeAfter,c=t.pos+1;for(;!l.isTextblock;c++){if(l.type.spec.isolating)return!1;let e=l.firstChild;if(!e)return!1;l=e}let p=(0,r.$L)(e.doc,s,c,o.Ji.empty);if(!p||p.from!=s||p instanceof r.Ln&&p.slice.size>=c-s)return!1;if(n){let t=e.tr.step(p);t.setSelection(a.U3.create(t.doc,s)),n(t.scrollIntoView())}return!0}function u(e,t,n=!1){for(let i=e;i;i="start"==t?i.firstChild:i.lastChild){if(i.isTextblock)return!0;if(n&&1!=i.childCount)break}return!1}let f=(e,t,n)=>{let{$head:i,empty:r}=e.selection,o=i;if(!r)return!1;if(i.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):i.parentOffset>0)return!1;o=m(i)}let s=o&&o.nodeBefore;return!!s&&!!a.nh.isSelectable(s)&&(t&&t(e.tr.setSelection(a.nh.create(e.doc,o.pos-s.nodeSize)).scrollIntoView()),!0)};function m(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function g(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("forward",e):!(n.parentOffset<n.parent.content.size))?n:null}let v=(e,t,n)=>{let i=g(e,n);if(!i)return!1;let s=y(i);if(!s)return!1;let l=s.nodeAfter;if(O(e,s,t,1))return!0;if(0==i.parent.content.size&&(u(l,"start")||a.nh.isSelectable(l))){let n=(0,r.$L)(e.doc,i.before(),i.after(),o.Ji.empty);if(n&&n.slice.size<n.to-n.from){if(t){let i=e.tr.step(n);i.setSelection(u(l,"start")?a.LN.findFrom(i.doc.resolve(i.mapping.map(s.pos)),1):a.nh.create(i.doc,i.mapping.map(s.pos))),t(i.scrollIntoView())}return!0}}return!!l.isAtom&&s.depth==i.depth-1&&(t&&t(e.tr.delete(s.pos,s.pos+l.nodeSize).scrollIntoView()),!0)},b=(e,t,n)=>{let{$head:i,empty:r}=e.selection,o=i;if(!r)return!1;if(i.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):i.parentOffset<i.parent.content.size)return!1;o=y(i)}let s=o&&o.nodeAfter;return!!s&&!!a.nh.isSelectable(s)&&(t&&t(e.tr.setSelection(a.nh.create(e.doc,o.pos)).scrollIntoView()),!0)};function y(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}let x=(e,t)=>{let n=e.selection,i=n instanceof a.nh,o;if(i){if(n.node.isTextblock||!(0,r.n9)(e.doc,n.from))return!1;o=n.from}else if(null==(o=(0,r.N0)(e.doc,n.from,-1)))return!1;if(t){let n=e.tr.join(o);i&&n.setSelection(a.nh.create(n.doc,o-e.doc.resolve(o).nodeBefore.nodeSize)),t(n.scrollIntoView())}return!0},w=(e,t)=>{let n=e.selection,i;if(n instanceof a.nh){if(n.node.isTextblock||!(0,r.n9)(e.doc,n.to))return!1;i=n.to}else if(null==(i=(0,r.N0)(e.doc,n.to,1)))return!1;return t&&t(e.tr.join(i).scrollIntoView()),!0},k=(e,t)=>{let{$from:n,$to:i}=e.selection,o=n.blockRange(i),a=o&&(0,r.jP)(o);return null!=a&&(t&&t(e.tr.lift(o,a).scrollIntoView()),!0)},S=(e,t)=>{let{$head:n,$anchor:i}=e.selection;return!!n.parent.type.spec.code&&!!n.sameParent(i)&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function A(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let E=(e,t)=>{let{$head:n,$anchor:i}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(i))return!1;let r=n.node(-1),o=n.indexAfter(-1),s=A(r.contentMatchAt(o));if(!s||!r.canReplaceWith(o,o,s))return!1;if(t){let i=n.after(),r=e.tr.replaceWith(i,i,s.createAndFill());r.setSelection(a.LN.near(r.doc.resolve(i),1)),t(r.scrollIntoView())}return!0},M=(e,t)=>{let n=e.selection,{$from:i,$to:r}=n;if(n instanceof a.i5||i.parent.inlineContent||r.parent.inlineContent)return!1;let o=A(r.parent.contentMatchAt(r.indexAfter()));if(!o||!o.isTextblock)return!1;if(t){let n=(!i.parentOffset&&r.index()<r.parent.childCount?i:r).pos,s=e.tr.insert(n,o.createAndFill());s.setSelection(a.U3.create(s.doc,n+1)),t(s.scrollIntoView())}return!0},C=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let i=n.before();if((0,r.zy)(e.doc,i))return t&&t(e.tr.split(i).scrollIntoView()),!0}let i=n.blockRange(),o=i&&(0,r.jP)(i);return null!=o&&(t&&t(e.tr.lift(i,o).scrollIntoView()),!0)},T=(e,t)=>{let{$from:n,to:i}=e.selection,r,o=n.sharedDepth(i);return 0!=o&&(r=n.before(o),t&&t(e.tr.setSelection(a.nh.create(e.doc,r))),!0)};function O(e,t,n,i){let s,l,c,p=t.nodeBefore,d=t.nodeAfter,h,f,m=p.type.spec.isolating||d.type.spec.isolating;if(!m&&(s=t.nodeBefore,l=t.nodeAfter,c=t.index(),s&&l&&s.type.compatibleContent(l.type)&&(!s.content.size&&t.parent.canReplace(c-1,c)?(n&&n(e.tr.delete(t.pos-s.nodeSize,t.pos).scrollIntoView()),!0):!!t.parent.canReplace(c,c+1)&&!!(l.isTextblock||(0,r.n9)(e.doc,t.pos))&&(n&&n(e.tr.join(t.pos).scrollIntoView()),!0))))return!0;let g=!m&&t.parent.canReplace(t.index(),t.index()+1);if(g&&(h=(f=p.contentMatchAt(p.childCount)).findWrapping(d.type))&&f.matchType(h[0]||d.type).validEnd){if(n){let i=t.pos+d.nodeSize,a=o.FK.empty;for(let e=h.length-1;e>=0;e--)a=o.FK.from(h[e].create(null,a));a=o.FK.from(p.copy(a));let s=e.tr.step(new r.Wg(t.pos-1,i,t.pos,i,new o.Ji(a,1,0),h.length,!0)),l=s.doc.resolve(i+2*h.length);l.nodeAfter&&l.nodeAfter.type==p.type&&(0,r.n9)(s.doc,l.pos)&&s.join(l.pos),n(s.scrollIntoView())}return!0}let v=d.type.spec.isolating||i>0&&m?null:a.LN.findFrom(t,1),b=v&&v.$from.blockRange(v.$to),y=b&&(0,r.jP)(b);if(null!=y&&y>=t.depth)return n&&n(e.tr.lift(b,y).scrollIntoView()),!0;if(g&&u(d,"start",!0)&&u(p,"end")){let i=p,a=[];for(;a.push(i),!i.isTextblock;)i=i.lastChild;let s=d,l=1;for(;!s.isTextblock;s=s.firstChild)l++;if(i.canReplace(i.childCount,i.childCount,s.content)){if(n){let i=o.FK.empty;for(let e=a.length-1;e>=0;e--)i=o.FK.from(a[e].copy(i));n(e.tr.step(new r.Wg(t.pos-a.length,t.pos+d.nodeSize,t.pos+l,t.pos+d.nodeSize-l,new o.Ji(i,a.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function N(e){return function(t,n){let i=t.selection,r=e<0?i.$from:i.$to,o=r.depth;for(;r.node(o).isInline;){if(!o)return!1;o--}return!!r.node(o).isTextblock&&(n&&n(t.tr.setSelection(a.U3.create(t.doc,e<0?r.start(o):r.end(o)))),!0)}}let R=N(-1),L=N(1);function z(e,t=null){return function(n,i){let{$from:o,$to:a}=n.selection,s=o.blockRange(a),l=s&&(0,r.oM)(s,e,t);return!!l&&(i&&i(n.tr.wrap(s,l).scrollIntoView()),!0)}}function I(e,t=null){return function(n,i){let r=!1;for(let i=0;i<n.selection.ranges.length&&!r;i++){let{$from:{pos:o},$to:{pos:a}}=n.selection.ranges[i];n.doc.nodesBetween(o,a,(i,o)=>{if(r)return!1;if(!(!i.isTextblock||i.hasMarkup(e,t)))if(i.type==e)r=!0;else{let t=n.doc.resolve(o),i=t.index();r=t.parent.canReplaceWith(i,i+1,e)}})}if(!r)return!1;if(i){let r=n.tr;for(let i=0;i<n.selection.ranges.length;i++){let{$from:{pos:o},$to:{pos:a}}=n.selection.ranges[i];r.setBlockType(o,a,e,t)}i(r.scrollIntoView())}return!0}}function j(...e){return function(t,n,i){for(let r=0;r<e.length;r++)if(e[r](t,n,i))return!0;return!1}}let _=j(s,c,f),D=j(s,v,b),$={Enter:j(S,M,C,(e,t)=>{let{$from:n,$to:i}=e.selection;if(e.selection instanceof a.nh&&e.selection.node.isBlock)return!!n.parentOffset&&!!(0,r.zy)(e.doc,n.pos)&&(t&&t(e.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let o=[],s,l,c=!1,p=!1;for(let e=n.depth;;e--){if(n.node(e).isBlock){let t;c=n.end(e)==n.pos+(n.depth-e),p=n.start(e)==n.pos-(n.depth-e),l=A(n.node(e-1).contentMatchAt(n.indexAfter(e-1)));o.unshift(t||(c&&l?{type:l}:null)),s=e;break}if(1==e)return!1;o.unshift(null)}let d=e.tr;(e.selection instanceof a.U3||e.selection instanceof a.i5)&&d.deleteSelection();let h=d.mapping.map(n.pos),u=(0,r.zy)(d.doc,h,o.length,o);if(u||(o[0]=l?{type:l}:null,u=(0,r.zy)(d.doc,h,o.length,o)),!u)return!1;if(d.split(h,o.length,o),!c&&p&&n.node(s).type!=l){let e=d.mapping.map(n.before(s)),t=d.doc.resolve(e);l&&n.node(s-1).canReplaceWith(t.index(),t.index()+1,l)&&d.setNodeMarkup(d.mapping.map(n.before(s)),l)}return t&&t(d.scrollIntoView()),!0}),"Mod-Enter":E,Backspace:_,"Mod-Backspace":_,"Shift-Backspace":_,Delete:D,"Mod-Delete":D,"Mod-a":(e,t)=>(t&&t(e.tr.setSelection(new a.i5(e.doc))),!0)},P={"Ctrl-h":$.Backspace,"Alt-Backspace":$["Mod-Backspace"],"Ctrl-d":$.Delete,"Ctrl-Alt-Backspace":$["Mod-Delete"],"Alt-Delete":$["Mod-Delete"],"Alt-d":$["Mod-Delete"],"Ctrl-a":R,"Ctrl-e":L};for(let e in $)P[e]=$[e];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform()},353:(e,t,n)=>{"use strict";n.d(t,{A:()=>em});var i=n(4701);let r=/^\s*>\s$/,o=i.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return["blockquote",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,i.tG)({find:r,type:this.type})]}}),a=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,s=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,l=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,c=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,p=i.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return["strong",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,i.OX)({find:a,type:this.type}),(0,i.OX)({find:l,type:this.type})]},addPasteRules(){return[(0,i.Zc)({find:s,type:this.type}),(0,i.Zc)({find:c,type:this.type})]}}),d="textStyle",h=/^\s*([-+*])\s$/,u=i.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(d)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=(0,i.tG)({find:h,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,i.tG)({find:h,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(d),editor:this.editor})),[e]}}),f=/(^|[^`])`([^`]+)`(?!`)/,m=/(^|[^`])`([^`]+)`(?!`)/g,g=i.CU.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:e}){return["code",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setCode:()=>({commands:e})=>e.setMark(this.name),toggleCode:()=>({commands:e})=>e.toggleMark(this.name),unsetCode:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,i.OX)({find:f,type:this.type})]},addPasteRules(){return[(0,i.Zc)({find:m,type:this.type})]}});var v=n(5383);let b=i.bP.create({name:"doc",topNode:!0,content:"block+"});var y=n(2571),x=n(808);class w{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(n=t.width)?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t,n=this.editorView.state.doc.resolve(this.cursorPos),i=!n.parent.inlineContent,r,o=this.editorView.dom,a=o.getBoundingClientRect(),s=a.width/o.offsetWidth,l=a.height/o.offsetHeight;if(i){let e=n.nodeBefore,t=n.nodeAfter;if(e||t){let n=this.editorView.nodeDOM(this.cursorPos-(e?e.nodeSize:0));if(n){let i=n.getBoundingClientRect(),o=e?i.bottom:i.top;e&&t&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let a=this.width/2*l;r={left:i.left,right:i.right,top:o-a,bottom:o+a}}}}if(!r){let e=this.editorView.coordsAtPos(this.cursorPos),t=this.width/2*s;r={left:e.left-t,right:e.left+t,top:e.top,bottom:e.bottom}}let c=this.editorView.dom.offsetParent;if(!this.element&&(this.element=c.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",i),this.element.classList.toggle("prosemirror-dropcursor-inline",!i),c&&(c!=document.body||"static"!=getComputedStyle(c).position)){let n=c.getBoundingClientRect(),i=n.width/c.offsetWidth,r=n.height/c.offsetHeight;e=n.left-c.scrollLeft*i,t=n.top-c.scrollTop*r}else e=-pageXOffset,t=-pageYOffset;this.element.style.left=(r.left-e)/s+"px",this.element.style.top=(r.top-t)/l+"px",this.element.style.width=(r.right-r.left)/s+"px",this.element.style.height=(r.bottom-r.top)/l+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),i=n&&n.type.spec.disableDropCursor,r="function"==typeof i?i(this.editorView,t,e):i;if(t&&!r){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=(0,x.Um)(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}let k=i.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(e={}){return new y.k_({view:t=>new w(t,e)})}(this.options)]}});var S=n(6770),A=n(156),E=n(2695);class M extends y.LN{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return M.valid(n)?new M(n):y.LN.near(n)}content(){return A.Ji.empty}eq(e){return e instanceof M&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new M(e.resolve(t.pos))}getBookmark(){return new C(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),i=e.node(t);if(0==n){if(i.type.spec.isolating)return!0;continue}for(let e=i.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),i=e.node(t);if(n==i.childCount){if(i.type.spec.isolating)return!0;continue}for(let e=i.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let i=t.contentMatchAt(e.index()).defaultType;return i&&i.isTextblock}static findGapCursorFrom(e,t,n=!1){n:for(;;){if(!n&&M.valid(e))return e;let i=e.pos,r=null;for(let n=e.depth;;n--){let o=e.node(n);if(t>0?e.indexAfter(n)<o.childCount:e.index(n)>0){r=o.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;i+=t;let a=e.doc.resolve(i);if(M.valid(a))return a}for(;;){let o=t>0?r.firstChild:r.lastChild;if(!o){if(r.isAtom&&!r.isText&&!y.nh.isSelectable(r)){e=e.doc.resolve(i+r.nodeSize*t),n=!1;continue n}break}r=o,i+=t;let a=e.doc.resolve(i);if(M.valid(a))return a}return null}}}M.prototype.visible=!1,M.findFrom=M.findGapCursorFrom,y.LN.jsonID("gapcursor",M);class C{constructor(e){this.pos=e}map(e){return new C(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return M.valid(t)?new M(t):y.LN.near(t)}}let T=(0,S.K)({ArrowLeft:O("horiz",-1),ArrowRight:O("horiz",1),ArrowUp:O("vert",-1),ArrowDown:O("vert",1)});function O(e,t){let n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,i,r){let o=e.selection,a=t>0?o.$to:o.$from,s=o.empty;if(o instanceof y.U3){if(!r.endOfTextblock(n)||0==a.depth)return!1;s=!1,a=e.doc.resolve(t>0?a.after():a.before())}let l=M.findGapCursorFrom(a,t,s);return!!l&&(i&&i(e.tr.setSelection(new M(l))),!0)}}function N(e,t,n){if(!e||!e.editable)return!1;let i=e.state.doc.resolve(t);if(!M.valid(i))return!1;let r=e.posAtCoords({left:n.clientX,top:n.clientY});return!(r&&r.inside>-1&&y.nh.isSelectable(e.state.doc.nodeAt(r.inside)))&&(e.dispatch(e.state.tr.setSelection(new M(i))),!0)}function R(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof M))return!1;let{$from:n}=e.state.selection,i=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!i)return!1;let r=A.FK.empty;for(let e=i.length-1;e>=0;e--)r=A.FK.from(i[e].createAndFill(null,r));let o=e.state.tr.replace(n.pos,n.pos,new A.Ji(r,0,0));return o.setSelection(y.U3.near(o.doc.resolve(n.pos+1))),e.dispatch(o),!1}function L(e){if(!(e.selection instanceof M))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",E.zF.create(e.doc,[E.NZ.widget(e.selection.head,t,{key:"gapcursor"})])}let z=i.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new y.k_({props:{decorations:L,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&M.valid(n)?new M(n):null,handleClick:N,handleKeyDown:T,handleDOMEvents:{beforeinput:R}}})],extendNodeSchema(e){var t;let n={name:e.name,options:e.options,storage:e.storage};return{allowGapCursor:null!=(t=(0,i.gk)((0,i.iI)(e,"allowGapCursor",n)))?t:null}}}),I=i.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",(0,i.KV)(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:i})=>e.first([()=>e.exitCode(),()=>e.command(()=>{let{selection:e,storedMarks:r}=n;if(e.$from.parent.type.spec.isolating)return!1;let{keepMarks:o}=this.options,{splittableMarks:a}=i.extensionManager,s=r||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command(({tr:e,dispatch:t})=>{if(t&&s&&o){let t=s.filter(e=>a.includes(e.type.name));e.ensureMarks(t)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),j=i.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(e=>({tag:`h${e}`,attrs:{level:e}}))},renderHTML({node:e,HTMLAttributes:t}){let n=this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0];return[`h${n}`,(0,i.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce((e,t)=>({...e,...{[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})}}),{})},addInputRules(){return this.options.levels.map(e=>(0,i.JJ)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}}))}});var _=function(){};_.prototype.append=function(e){return e.length?(e=_.from(e),!this.length&&e||e.length<200&&this.leafAppend(e)||this.length<200&&e.leafPrepend(this)||this.appendInner(e)):this},_.prototype.prepend=function(e){return e.length?_.from(e).append(this):this},_.prototype.appendInner=function(e){return new $(this,e)},_.prototype.slice=function(e,t){return(void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t)?_.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},_.prototype.get=function(e){if(!(e<0)&&!(e>=this.length))return this.getInner(e)},_.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},_.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var i=[];return this.forEach(function(t,n){return i.push(e(t,n))},t,n),i},_.from=function(e){return e instanceof _?e:e&&e.length?new D(e):_.empty};var D=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,i){for(var r=t;r<n;r++)if(!1===e(this.values[r],i+r))return!1},t.prototype.forEachInvertedInner=function(e,t,n,i){for(var r=t-1;r>=n;r--)if(!1===e(this.values[r],i+r))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=200)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=200)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}(_);_.empty=new D([]);var $=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,i){var r=this.left.length;if(t<r&&!1===this.left.forEachInner(e,t,Math.min(n,r),i)||n>r&&!1===this.right.forEachInner(e,Math.max(t-r,0),Math.min(this.length,n)-r,i+r))return!1},t.prototype.forEachInvertedInner=function(e,t,n,i){var r=this.left.length;if(t>r&&!1===this.right.forEachInvertedInner(e,t-r,Math.max(n,r)-r,i+r)||n<r&&!1===this.left.forEachInvertedInner(e,Math.min(t,r),n,i))return!1},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}(_);class P{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){let n,i,r,o;if(0==this.eventCount)return null;let a=this.items.length;for(;;a--)if(this.items.get(a-1).selection){--a;break}t&&(i=(n=this.remapping(a,this.items.length)).maps.length);let s=e.tr,l=[],c=[];return this.items.forEach((e,t)=>{if(!e.step){n||(i=(n=this.remapping(a,t+1)).maps.length),i--,c.push(e);return}if(n){c.push(new B(e.map));let t=e.step.map(n.slice(i)),r;t&&s.maybeStep(t).doc&&(r=s.mapping.maps[s.mapping.maps.length-1],l.push(new B(r,void 0,void 0,l.length+c.length))),i--,r&&n.appendMap(r,i)}else s.maybeStep(e.step);if(e.selection)return r=n?e.selection.map(n.slice(i)):e.selection,o=new P(this.items.slice(0,a).append(c.reverse().concat(l)),this.eventCount-1),!1},this.items.length,0),{remaining:o,transform:s,selection:r}}addTransform(e,t,n,i){var r,o;let a,s=[],l=this.eventCount,c=this.items,p=!i&&c.length?c.get(c.length-1):null;for(let n=0;n<e.steps.length;n++){let r=e.steps[n].invert(e.docs[n]),o=new B(e.mapping.maps[n],r,t),a;(a=p&&p.merge(o))&&(o=a,n?s.pop():c=c.slice(0,c.length-1)),s.push(o),t&&(l++,t=void 0),i||(p=o)}let d=l-n.depth;return d>H&&(r=c,o=d,r.forEach((e,t)=>{if(e.selection&&0==o--)return a=t,!1}),c=r.slice(a),l-=d),new P(c.append(s),l)}remapping(e,t){let n=new x.X9;return this.items.forEach((t,i)=>{let r=null!=t.mirrorOffset&&i-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,r)},e,t),n}addMaps(e){return 0==this.eventCount?this:new P(this.items.append(e.map(e=>new B(e))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],i=Math.max(0,this.items.length-t),r=e.mapping,o=e.steps.length,a=this.eventCount;this.items.forEach(e=>{e.selection&&a--},i);let s=t;this.items.forEach(t=>{let i=r.getMirror(--s);if(null==i)return;o=Math.min(o,i);let l=r.maps[i];if(t.step){let o=e.steps[i].invert(e.docs[i]),c=t.selection&&t.selection.map(r.slice(s+1,i));c&&a++,n.push(new B(l,o,c))}else n.push(new B(l))},i);let l=[];for(let e=t;e<o;e++)l.push(new B(r.maps[e]));let c=new P(this.items.slice(0,i).append(l).append(n),a);return c.emptyItemCount()>500&&(c=c.compress(this.items.length-n.length)),c}emptyItemCount(){let e=0;return this.items.forEach(t=>{!t.step&&e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,i=[],r=0;return this.items.forEach((o,a)=>{if(a>=e)i.push(o),o.selection&&r++;else if(o.step){let e=o.step.map(t.slice(n)),a=e&&e.getMap();if(n--,a&&t.appendMap(a,n),e){let s=o.selection&&o.selection.map(t.slice(n));s&&r++;let l=new B(a.invert(),e,s),c,p=i.length-1;(c=i.length&&i[p].merge(l))?i[p]=c:i.push(l)}}else o.map&&n--},this.items.length,0),new P(_.from(i.reverse()),r)}}P.empty=new P(_.empty,0);class B{constructor(e,t,n,i){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=i}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new B(t.getMap().invert(),t,this.selection)}}}class F{constructor(e,t,n,i,r){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=i,this.prevComposition=r}}let H=20;function K(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach((e,n,i,r)=>t.push(i,r));return t}function U(e,t){if(!e)return null;let n=[];for(let i=0;i<e.length;i+=2){let r=t.map(e[i],1),o=t.map(e[i+1],-1);r<=o&&n.push(r,o)}return n}let J=!1,q=null;function W(e){let t=e.plugins;if(q!=t){J=!1,q=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){J=!0;break}}return J}let V=new y.hs("history"),G=new y.hs("closeHistory");function Z(e,t){return(n,i)=>{let r=V.getState(n);if(!r||0==(e?r.undone:r.done).eventCount)return!1;if(i){let o=function(e,t,n){let i=W(t),r=V.get(t).spec.config,o=(n?e.undone:e.done).popEvent(t,i);if(!o)return null;let a=o.selection.resolve(o.transform.doc),s=(n?e.done:e.undone).addTransform(o.transform,t.selection.getBookmark(),r,i),l=new F(n?s:o.remaining,n?o.remaining:s,null,0,-1);return o.transform.setSelection(a).setMeta(V,{redo:n,historyState:l})}(r,n,e);o&&i(t?o.scrollIntoView():o)}return!0}}let X=Z(!1,!0),Y=Z(!0,!0);Z(!1,!1),Z(!0,!1);let Q=i.YY.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>X(e,t),redo:()=>({state:e,dispatch:t})=>Y(e,t)}),addProseMirrorPlugins(){return[function(e={}){return e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},new y.k_({key:V,state:{init:()=>new F(P.empty,P.empty,null,0,-1),apply:(t,n,i)=>(function(e,t,n,i){let r=n.getMeta(V),o;if(r)return r.historyState;n.getMeta(G)&&(e=new F(e.done,e.undone,null,0,-1));let a=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(a&&a.getMeta(V))if(a.getMeta(V).redo)return new F(e.done.addTransform(n,void 0,i,W(t)),e.undone,K(n.mapping.maps),e.prevTime,e.prevComposition);else return new F(e.done,e.undone.addTransform(n,void 0,i,W(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||a&&!1===a.getMeta("addToHistory"))if(o=n.getMeta("rebased"))return new F(e.done.rebased(n,o),e.undone.rebased(n,o),U(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);else return new F(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),U(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let r=n.getMeta("composition"),o=0==e.prevTime||!a&&e.prevComposition!=r&&(e.prevTime<(n.time||0)-i.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach((e,i)=>{for(let r=0;r<t.length;r+=2)e<=t[r+1]&&i>=t[r]&&(n=!0)}),n}(n,e.prevRanges)),s=a?U(e.prevRanges,n.mapping):K(n.mapping.maps);return new F(e.done.addTransform(n,o?t.selection.getBookmark():void 0,i,W(t)),P.empty,s,n.time,null==r?e.prevComposition:r)}})(n,i,t,e)},config:e,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,i="historyUndo"==n?X:"historyRedo"==n?Y:null;return!!i&&(t.preventDefault(),i(e.state,e.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),ee=i.bP.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:e}){return["hr",(0,i.KV)(this.options.HTMLAttributes,e)]},addCommands(){return{setHorizontalRule:()=>({chain:e,state:t})=>{let{selection:n}=t,{$from:r,$to:o}=n,a=e();return 0===r.parentOffset?a.insertContentAt({from:Math.max(r.pos-1,0),to:o.pos},{type:this.name}):(0,i.BQ)(n)?a.insertContentAt(o.pos,{type:this.name}):a.insertContent({type:this.name}),a.command(({tr:e,dispatch:t})=>{var n;if(t){let{$to:t}=e.selection,i=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?e.setSelection(y.U3.create(e.doc,t.pos+1)):t.nodeAfter.isBlock?e.setSelection(y.nh.create(e.doc,t.pos)):e.setSelection(y.U3.create(e.doc,t.pos));else{let r=null==(n=t.parent.type.contentMatch.defaultType)?void 0:n.create();r&&(e.insert(i,r),e.setSelection(y.U3.create(e.doc,i+1)))}e.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,i.jT)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),et=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,en=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,ei=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,er=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,eo=i.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,i.OX)({find:et,type:this.type}),(0,i.OX)({find:ei,type:this.type})]},addPasteRules(){return[(0,i.Zc)({find:en,type:this.type}),(0,i.Zc)({find:er,type:this.type})]}}),ea=i.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",(0,i.KV)(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),es="textStyle",el=/^(\d+)\.\s$/,ec=i.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:null,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){let{start:t,...n}=e;return 1===t?["ol",(0,i.KV)(this.options.HTMLAttributes,n),0]:["ol",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(es)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=(0,i.tG)({find:el,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,i.tG)({find:el,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(es)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),ep=i.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),ed=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,eh=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,eu=i.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,i.OX)({find:ed,type:this.type})]},addPasteRules(){return[(0,i.Zc)({find:eh,type:this.type})]}}),ef=i.bP.create({name:"text",group:"inline"}),em=i.YY.create({name:"starterKit",addExtensions(){let e=[];return!1!==this.options.bold&&e.push(p.configure(this.options.bold)),!1!==this.options.blockquote&&e.push(o.configure(this.options.blockquote)),!1!==this.options.bulletList&&e.push(u.configure(this.options.bulletList)),!1!==this.options.code&&e.push(g.configure(this.options.code)),!1!==this.options.codeBlock&&e.push(v.NG.configure(this.options.codeBlock)),!1!==this.options.document&&e.push(b.configure(this.options.document)),!1!==this.options.dropcursor&&e.push(k.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&e.push(z.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&e.push(I.configure(this.options.hardBreak)),!1!==this.options.heading&&e.push(j.configure(this.options.heading)),!1!==this.options.history&&e.push(Q.configure(this.options.history)),!1!==this.options.horizontalRule&&e.push(ee.configure(this.options.horizontalRule)),!1!==this.options.italic&&e.push(eo.configure(this.options.italic)),!1!==this.options.listItem&&e.push(ea.configure(this.options.listItem)),!1!==this.options.orderedList&&e.push(ec.configure(this.options.orderedList)),!1!==this.options.paragraph&&e.push(ep.configure(this.options.paragraph)),!1!==this.options.strike&&e.push(eu.configure(this.options.strike)),!1!==this.options.text&&e.push(ef.configure(this.options.text)),e}})},423:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var i=n(4701);let r=i.bP.create({name:"tableRow",addOptions:()=>({HTMLAttributes:{}}),content:"(tableCell | tableHeader)*",tableRole:"row",parseHTML:()=>[{tag:"tr"}],renderHTML({HTMLAttributes:e}){return["tr",(0,i.KV)(this.options.HTMLAttributes,e),0]}})},646:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},808:(e,t,n)=>{"use strict";n.d(t,{$L:()=>O,Ln:()=>m,N0:()=>C,Um:()=>T,Wg:()=>g,X9:()=>a,dL:()=>B,jP:()=>y,n9:()=>E,oM:()=>x,zy:()=>A});var i=n(156);class r{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class o{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&o.empty)return o.empty}recover(e){let t=0,n=65535&e;if(!this.inverted)for(let e=0;e<n;e++)t+=this.ranges[3*e+2]-this.ranges[3*e+1];return this.ranges[3*n]+t+(e-(65535&e))/65536}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let i=0,o=this.inverted?2:1,a=this.inverted?1:2;for(let s=0;s<this.ranges.length;s+=3){let l=this.ranges[s]-(this.inverted?i:0);if(l>e)break;let c=this.ranges[s+o],p=this.ranges[s+a],d=l+c;if(e<=d){let o=c?e==l?-1:e==d?1:t:t,a=l+i+(o<0?0:p);if(n)return a;let h=e==(t<0?l:d)?null:s/3+(e-l)*65536,u=e==l?2:e==d?1:4;return(t<0?e!=l:e!=d)&&(u|=8),new r(a,u,h)}i+=p-c}return n?e+i:new r(e+i,0,null)}touches(e,t){let n=0,i=65535&t,r=this.inverted?2:1,o=this.inverted?1:2;for(let t=0;t<this.ranges.length;t+=3){let a=this.ranges[t]-(this.inverted?n:0);if(a>e)break;let s=this.ranges[t+r];if(e<=a+s&&t==3*i)return!0;n+=this.ranges[t+o]-s}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let i=0,r=0;i<this.ranges.length;i+=3){let o=this.ranges[i],a=o-(this.inverted?r:0),s=o+(this.inverted?0:r),l=this.ranges[i+t],c=this.ranges[i+n];e(a,a+l,s,s+c),r+=c-l}}invert(){return new o(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?o.empty:new o(e<0?[0,-e,0]:[0,0,e])}}o.empty=new o([]);class a{constructor(e,t,n=0,i=e?e.length:0){this.mirror=t,this.from=n,this.to=i,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new a(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let i=e.getMirror(t);this.appendMap(e._maps[t],null!=i&&i<t?n+i:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let i=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=i&&i>t?n-i-1:void 0)}}invert(){let e=new a;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let i=0;for(let n=this.from;n<this.to;n++){let r=this._maps[n].mapResult(e,t);if(null!=r.recover){let t=this.getMirror(n);if(null!=t&&t>n&&t<this.to){n=t,e=this._maps[t].recover(r.recover);continue}}i|=r.delInfo,e=r.pos}return n?e:new r(e,i,null)}}let s=Object.create(null);class l{getMap(){return o.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=s[t.stepType];if(!n)throw RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in s)throw RangeError("Duplicate use of step JSON ID "+e);return s[e]=t,t.prototype.jsonID=e,t}}class c{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new c(e,null)}static fail(e){return new c(null,e)}static fromReplace(e,t,n,r){try{return c.ok(e.replace(t,n,r))}catch(e){if(e instanceof i.vI)return c.fail(e.message);throw e}}}function p(e,t,n){let r=[];for(let i=0;i<e.childCount;i++){let o=e.child(i);o.content.size&&(o=o.copy(p(o.content,t,o))),o.isInline&&(o=t(o,n,i)),r.push(o)}return i.FK.fromArray(r)}class d extends l{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),r=n.node(n.sharedDepth(this.to)),o=new i.Ji(p(t.content,(e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e,r),t.openStart,t.openEnd);return c.fromReplace(e,this.from,this.to,o)}invert(){return new h(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new d(t.pos,n.pos,this.mark)}merge(e){return e instanceof d&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new d(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new d(t.from,t.to,e.markFromJSON(t.mark))}}l.jsonID("addMark",d);class h extends l{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new i.Ji(p(t.content,e=>e.mark(this.mark.removeFromSet(e.marks)),e),t.openStart,t.openEnd);return c.fromReplace(e,this.from,this.to,n)}invert(){return new d(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new h(t.pos,n.pos,this.mark)}merge(e){return e instanceof h&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new h(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new h(t.from,t.to,e.markFromJSON(t.mark))}}l.jsonID("removeMark",h);class u extends l{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return c.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return c.fromReplace(e,this.pos,this.pos+1,new i.Ji(i.FK.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new u(this.pos,t.marks[n]);return new u(this.pos,this.mark)}}return new f(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new u(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new u(t.pos,e.markFromJSON(t.mark))}}l.jsonID("addNodeMark",u);class f extends l{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return c.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return c.fromReplace(e,this.pos,this.pos+1,new i.Ji(i.FK.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new u(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new f(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new f(t.pos,e.markFromJSON(t.mark))}}l.jsonID("removeNodeMark",f);class m extends l{constructor(e,t,n,i=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=i}apply(e){return this.structure&&v(e,this.from,this.to)?c.fail("Structure replace would overwrite content"):c.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new o([this.from,this.to-this.from,this.slice.size])}invert(e){return new m(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new m(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof m)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart)if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;else{let t=this.slice.size+e.slice.size==0?i.Ji.empty:new i.Ji(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new m(e.from,this.to,t,this.structure)}{let t=this.slice.size+e.slice.size==0?i.Ji.empty:new i.Ji(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new m(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new m(t.from,t.to,i.Ji.fromJSON(e,t.slice),!!t.structure)}}l.jsonID("replace",m);class g extends l{constructor(e,t,n,i,r,o,a=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=i,this.slice=r,this.insert=o,this.structure=a}apply(e){if(this.structure&&(v(e,this.from,this.gapFrom)||v(e,this.gapTo,this.to)))return c.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return c.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?c.fromReplace(e,this.from,this.to,n):c.fail("Content does not fit in gap")}getMap(){return new o([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new g(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),i=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),r=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||i<t.pos||r>n.pos?null:new g(t.pos,n.pos,i,r,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new g(t.from,t.to,t.gapFrom,t.gapTo,i.Ji.fromJSON(e,t.slice),t.insert,!!t.structure)}}function v(e,t,n){let i=e.resolve(t),r=n-t,o=i.depth;for(;r>0&&o>0&&i.indexAfter(o)==i.node(o).childCount;)o--,r--;if(r>0){let e=i.node(o).maybeChild(i.indexAfter(o));for(;r>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,r--}}return!1}function b(e,t,n,r=n.contentMatch,o=!0){let a=e.doc.nodeAt(t),s=[],l=t+1;for(let t=0;t<a.childCount;t++){let c=a.child(t),p=l+c.nodeSize,d=r.matchType(c.type);if(d){r=d;for(let t=0;t<c.marks.length;t++)n.allowsMarkType(c.marks[t].type)||e.step(new h(l,p,c.marks[t]));if(o&&c.isText&&"pre"!=n.whitespace){let e,t=/\r?\n|\r/g,r;for(;e=t.exec(c.text);)r||(r=new i.Ji(i.FK.from(n.schema.text(" ",n.allowedMarks(c.marks))),0,0)),s.push(new m(l+e.index,l+e.index+e[0].length,r))}}else s.push(new m(l,p,i.Ji.empty));l=p}if(!r.validEnd){let t=r.fillBefore(i.FK.empty,!0);e.replace(l,l,new i.Ji(t,0,0))}for(let t=s.length-1;t>=0;t--)e.step(s[t])}function y(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let i=e.$from.node(n),r=e.$from.index(n),o=e.$to.indexAfter(n);if(n<e.depth&&i.canReplace(r,o,t))return n;if(0==n||i.type.spec.isolating||!((0==r||i.canReplace(r,i.childCount))&&(o==i.childCount||i.canReplace(0,o))))break}return null}function x(e,t,n=null,i=e){let r=function(e,t){let{parent:n,startIndex:i,endIndex:r}=e,o=n.contentMatchAt(i).findWrapping(t);if(!o)return null;let a=o.length?o[0]:t;return n.canReplaceWith(i,r,a)?o:null}(e,t),o=r&&function(e,t){let{parent:n,startIndex:i,endIndex:r}=e,o=n.child(i),a=t.contentMatch.findWrapping(o.type);if(!a)return null;let s=(a.length?a[a.length-1]:t).contentMatch;for(let e=i;s&&e<r;e++)s=s.matchType(n.child(e).type);return s&&s.validEnd?a:null}(i,t);return o?r.map(w).concat({type:t,attrs:n}).concat(o.map(w)):null}function w(e){return{type:e,attrs:null}}function k(e,t,n,i){t.forEach((r,o)=>{if(r.isText){let a,s=/\r?\n|\r/g;for(;a=s.exec(r.text);){let r=e.mapping.slice(i).map(n+1+o+a.index);e.replaceWith(r,r+1,t.type.schema.linebreakReplacement.create())}}})}function S(e,t,n,i){t.forEach((r,o)=>{if(r.type==r.type.schema.linebreakReplacement){let r=e.mapping.slice(i).map(n+1+o);e.replaceWith(r,r+1,t.type.schema.text("\n"))}})}function A(e,t,n=1,i){let r=e.resolve(t),o=r.depth-n,a=i&&i[i.length-1]||r.parent;if(o<0||r.parent.type.spec.isolating||!r.parent.canReplace(r.index(),r.parent.childCount)||!a.type.validContent(r.parent.content.cutByIndex(r.index(),r.parent.childCount)))return!1;for(let e=r.depth-1,t=n-2;e>o;e--,t--){let n=r.node(e),o=r.index(e);if(n.type.spec.isolating)return!1;let a=n.content.cutByIndex(o,n.childCount),s=i&&i[t+1];s&&(a=a.replaceChild(0,s.type.create(s.attrs)));let l=i&&i[t]||n;if(!n.canReplace(o+1,n.childCount)||!l.type.validContent(a))return!1}let s=r.indexAfter(o),l=i&&i[0];return r.node(o).canReplaceWith(s,s,l?l.type:r.node(o+1).type)}function E(e,t){let n=e.resolve(t),i=n.index();return M(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(i,i+1)}function M(e,t){return!!(e&&t&&!e.isLeaf&&function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:i}=e.type.schema;for(let r=0;r<t.childCount;r++){let o=t.child(r),a=o.type==i?e.type.schema.nodes.text:o.type;if(!(n=n.matchType(a))||!e.type.allowsMarks(o.marks))return!1}return n.validEnd}(e,t))}function C(e,t,n=-1){let i=e.resolve(t);for(let e=i.depth;;e--){let r,o,a=i.index(e);if(e==i.depth?(r=i.nodeBefore,o=i.nodeAfter):n>0?(r=i.node(e+1),a++,o=i.node(e).maybeChild(a)):(r=i.node(e).maybeChild(a-1),o=i.node(e+1)),r&&!r.isTextblock&&M(r,o)&&i.node(e).canReplace(a,a+1))return t;if(0==e)break;t=n<0?i.before(e):i.after(e)}}function T(e,t,n){let i=e.resolve(t);if(!n.content.size)return t;let r=n.content;for(let e=0;e<n.openStart;e++)r=r.firstChild.content;for(let e=1;e<=(0==n.openStart&&n.size?2:1);e++)for(let t=i.depth;t>=0;t--){let n=t==i.depth?0:i.pos<=(i.start(t+1)+i.end(t+1))/2?-1:1,o=i.index(t)+ +(n>0),a=i.node(t),s=!1;if(1==e)s=a.canReplace(o,o,r);else{let e=a.contentMatchAt(o).findWrapping(r.firstChild.type);s=e&&a.canReplaceWith(o,o,e[0])}if(s)return 0==n?i.pos:n<0?i.before(t+1):i.after(t+1)}return null}function O(e,t,n=t,r=i.Ji.empty){if(t==n&&!r.size)return null;let o=e.resolve(t),a=e.resolve(n);return N(o,a,r)?new m(t,n,r):new R(o,a,r).fit()}function N(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}l.jsonID("replaceAround",g);class R{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=i.FK.empty;for(let t=0;t<=e.depth;t++){let n=e.node(t);this.frontier.push({type:n.type,match:n.contentMatchAt(e.indexAfter(t))})}for(let t=e.depth;t>0;t--)this.placed=i.FK.from(e.node(t).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,r=this.close(e<0?this.$to:n.doc.resolve(e));if(!r)return null;let o=this.placed,a=n.depth,s=r.depth;for(;a&&s&&1==o.childCount;)o=o.firstChild.content,a--,s--;let l=new i.Ji(o,a,s);return e>-1?new g(n.pos,e,this.$to.pos,this.$to.end(),l,t):l.size||n.pos!=this.$to.pos?new m(n.pos,r.pos,l):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,i=this.unplaced.openEnd;n<e;n++){let r=t.firstChild;if(t.childCount>1&&(i=0),r.type.spec.isolating&&i<=n){e=n;break}t=r.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e,r=null,o=(n?(r=I(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let e=this.depth;e>=0;e--){let{type:a,match:s}=this.frontier[e],l,c=null;if(1==t&&(o?s.matchType(o.type)||(c=s.fillBefore(i.FK.from(o),!1)):r&&a.compatibleContent(r.type)))return{sliceDepth:n,frontierDepth:e,parent:r,inject:c};if(2==t&&o&&(l=s.findWrapping(o.type)))return{sliceDepth:n,frontierDepth:e,parent:r,wrap:l};if(r&&s.matchType(r.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=I(e,t);return!!r.childCount&&!r.firstChild.isLeaf&&(this.unplaced=new i.Ji(e,t+1,Math.max(n,r.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=I(e,t);if(r.childCount<=1&&t>0){let o=e.size-t<=t+r.size;this.unplaced=new i.Ji(L(e,t-1,1),t-1,o?t-1:n)}else this.unplaced=new i.Ji(L(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:r,wrap:o}){for(;this.depth>t;)this.closeFrontierNode();if(o)for(let e=0;e<o.length;e++)this.openFrontierNode(o[e]);let a=this.unplaced,s=n?n.content:a.content,l=a.openStart-e,c=0,p=[],{match:d,type:h}=this.frontier[t];if(r){for(let e=0;e<r.childCount;e++)p.push(r.child(e));d=d.matchFragment(r)}let u=s.size+e-(a.content.size-a.openEnd);for(;c<s.childCount;){let e=s.child(c),t=d.matchType(e.type);if(!t)break;(++c>1||0==l||e.content.size)&&(d=t,p.push(function e(t,n,r){if(n<=0)return t;let o=t.content;return n>1&&(o=o.replaceChild(0,e(o.firstChild,n-1,1==o.childCount?r-1:0))),n>0&&(o=t.type.contentMatch.fillBefore(o).append(o),r<=0&&(o=o.append(t.type.contentMatch.matchFragment(o).fillBefore(i.FK.empty,!0)))),t.copy(o)}(e.mark(h.allowedMarks(e.marks)),1==c?l:0,c==s.childCount?u:-1)))}let f=c==s.childCount;f||(u=-1),this.placed=z(this.placed,t,i.FK.from(p)),this.frontier[t].match=d,f&&u<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let e=0,t=s;e<u;e++){let e=t.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),t=e.content}this.unplaced=f?0==e?i.Ji.empty:new i.Ji(L(a.content,e-1,1),e-1,u<0?a.openEnd:e-1):new i.Ji(L(a.content,e,c),a.openStart,a.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!j(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return -1;let{depth:n}=this.$to,i=this.$to.after(n);for(;n>1&&i==this.$to.end(--n);)++i;return i}findCloseLevel(e){i:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:i}=this.frontier[t],r=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=j(e,t,i,n,r);if(o){for(let n=t-1;n>=0;n--){let{match:t,type:i}=this.frontier[n],r=j(e,n,i,t,!0);if(!r||r.childCount)continue i}return{depth:t,fit:o,move:r?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=z(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),i=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,i)}return e}openFrontierNode(e,t=null,n){let r=this.frontier[this.depth];r.match=r.match.matchType(e),this.placed=z(this.placed,this.depth,i.FK.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(i.FK.empty,!0);e.childCount&&(this.placed=z(this.placed,this.frontier.length,e))}}function L(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(L(e.firstChild.content,t-1,n)))}function z(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(z(e.lastChild.content,t-1,n)))}function I(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function j(e,t,n,i,r){let o=e.node(t),a=r?e.indexAfter(t):e.index(t);if(a==o.childCount&&!n.compatibleContent(o.type))return null;let s=i.fillBefore(o.content,!0,a);return s&&!function(e,t,n){for(let i=n;i<t.childCount;i++)if(!e.allowsMarks(t.child(i).marks))return!0;return!1}(n,o.content,a)?s:null}function _(e,t){let n=[],i=Math.min(e.depth,t.depth);for(let r=i;r>=0;r--){let i=e.start(r);if(i<e.pos-(e.depth-r)||t.end(r)>t.pos+(t.depth-r)||e.node(r).type.spec.isolating||t.node(r).type.spec.isolating)break;(i==t.start(r)||r==e.depth&&r==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&r&&t.start(r-1)==i-1)&&n.push(r)}return n}class D extends l{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return c.fail("No node at attribute step's position");let n=Object.create(null);for(let e in t.attrs)n[e]=t.attrs[e];n[this.attr]=this.value;let r=t.type.create(n,null,t.marks);return c.fromReplace(e,this.pos,this.pos+1,new i.Ji(i.FK.from(r),0,+!t.isLeaf))}getMap(){return o.empty}invert(e){return new D(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new D(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new D(t.pos,t.attr,t.value)}}l.jsonID("attr",D);class $ extends l{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let n in e.attrs)t[n]=e.attrs[n];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return c.ok(n)}getMap(){return o.empty}invert(e){return new $(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new $(t.attr,t.value)}}l.jsonID("docAttr",$);let P=class extends Error{};(P=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n}).prototype=Object.create(Error.prototype),P.prototype.constructor=P,P.prototype.name="TransformError";class B{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new a}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new P(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=i.Ji.empty){let r=O(this.doc,e,t,n);return r&&this.step(r),this}replaceWith(e,t,n){return this.replace(e,t,new i.Ji(i.FK.from(n),0,0))}delete(e,t){return this.replace(e,t,i.Ji.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return!function(e,t,n,r){if(!r.size)return e.deleteRange(t,n);let o=e.doc.resolve(t),a=e.doc.resolve(n);if(N(o,a,r))return e.step(new m(t,n,r));let s=_(o,e.doc.resolve(n));0==s[s.length-1]&&s.pop();let l=-(o.depth+1);s.unshift(l);for(let e=o.depth,t=o.pos-1;e>0;e--,t--){let n=o.node(e).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;s.indexOf(e)>-1?l=e:o.before(e)==t&&s.splice(1,0,-e)}let c=s.indexOf(l),p=[],d=r.openStart;for(let e=r.content,t=0;;t++){let n=e.firstChild;if(p.push(n),t==r.openStart)break;e=n.content}for(let e=d-1;e>=0;e--){var h;let t=p[e],n=(h=t.type).spec.defining||h.spec.definingForContent;if(n&&!t.sameMarkup(o.node(Math.abs(l)-1)))d=e;else if(n||!t.type.isTextblock)break}for(let t=r.openStart;t>=0;t--){let l=(t+d+1)%(r.openStart+1),h=p[l];if(h)for(let t=0;t<s.length;t++){let p=s[(t+c)%s.length],d=!0;p<0&&(d=!1,p=-p);let u=o.node(p-1),f=o.index(p-1);if(u.canReplaceWith(f,f,h.type,h.marks))return e.replace(o.before(p),d?a.after(p):n,new i.Ji(function e(t,n,r,o,a){if(n<r){let i=t.firstChild;t=t.replaceChild(0,i.copy(e(i.content,n+1,r,o,i)))}if(n>o){let e=a.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(i.FK.empty,!0))}return t}(r.content,0,r.openStart,l),l,r.openEnd))}}let u=e.steps.length;for(let i=s.length-1;i>=0&&(e.replace(t,n,r),!(e.steps.length>u));i--){let e=s[i];e<0||(t=o.before(e),n=a.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){var r=e,o=t;if(!n.isInline&&r==o&&this.doc.resolve(r).parent.content.size){let e=function(e,t,n){let i=e.resolve(t);if(i.parent.canReplaceWith(i.index(),i.index(),n))return t;if(0==i.parentOffset)for(let e=i.depth-1;e>=0;e--){let t=i.index(e);if(i.node(e).canReplaceWith(t,t,n))return i.before(e+1);if(t>0)return null}if(i.parentOffset==i.parent.content.size)for(let e=i.depth-1;e>=0;e--){let t=i.indexAfter(e);if(i.node(e).canReplaceWith(t,t,n))return i.after(e+1);if(t<i.node(e).childCount)break}return null}(this.doc,r,n.type);null!=e&&(r=o=e)}return this.replaceRange(r,o,new i.Ji(i.FK.from(n),0,0)),this}deleteRange(e,t){return!function(e,t,n){let i=e.doc.resolve(t),r=e.doc.resolve(n),o=_(i,r);for(let t=0;t<o.length;t++){let n=o[t],a=t==o.length-1;if(a&&0==n||i.node(n).type.contentMatch.validEnd)return e.delete(i.start(n),r.end(n));if(n>0&&(a||i.node(n-1).canReplace(i.index(n-1),r.indexAfter(n-1))))return e.delete(i.before(n),r.after(n))}for(let o=1;o<=i.depth&&o<=r.depth;o++)if(t-i.start(o)==i.depth-o&&n>i.end(o)&&r.end(o)-n!=r.depth-o&&i.start(o-1)==r.start(o-1)&&i.node(o-1).canReplace(i.index(o-1),r.index(o-1)))return e.delete(i.before(o),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return!function(e,t,n){let{$from:r,$to:o,depth:a}=t,s=r.before(a+1),l=o.after(a+1),c=s,p=l,d=i.FK.empty,h=0;for(let e=a,t=!1;e>n;e--)t||r.index(e)>0?(t=!0,d=i.FK.from(r.node(e).copy(d)),h++):c--;let u=i.FK.empty,f=0;for(let e=a,t=!1;e>n;e--)t||o.after(e+1)<o.end(e)?(t=!0,u=i.FK.from(o.node(e).copy(u)),f++):p++;e.step(new g(c,p,s,l,new i.Ji(d.append(u),h,f),d.size-h,!0))}(this,e,t),this}join(e,t=1){return!function(e,t,n){let r=null,{linebreakReplacement:o}=e.doc.type.schema,a=e.doc.resolve(t-n),s=a.node().type;if(o&&s.inlineContent){let e="pre"==s.whitespace,t=!!s.contentMatch.matchType(o);e&&!t?r=!1:!e&&t&&(r=!0)}let l=e.steps.length;if(!1===r){let i=e.doc.resolve(t+n);S(e,i.node(),i.before(),l)}s.inlineContent&&b(e,t+n-1,s,a.node().contentMatchAt(a.index()),null==r);let c=e.mapping.slice(l),p=c.map(t-n);if(e.step(new m(p,c.map(t+n,-1),i.Ji.empty,!0)),!0===r){let t=e.doc.resolve(p);k(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return!function(e,t,n){let r=i.FK.empty;for(let e=n.length-1;e>=0;e--){if(r.size){let t=n[e].type.contentMatch.matchFragment(r);if(!t||!t.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=i.FK.from(n[e].type.create(n[e].attrs,r))}let o=t.start,a=t.end;e.step(new g(o,a,o,a,new i.Ji(r,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,r=null){var o=this;if(!n.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let a=o.steps.length;return o.doc.nodesBetween(e,t,(e,t)=>{var s,l,c;let p,d,h="function"==typeof r?r(e):r;if(e.isTextblock&&!e.hasMarkup(n,h)&&(s=o.doc,l=o.mapping.slice(a).map(t),c=n,d=(p=s.resolve(l)).index(),p.parent.canReplaceWith(d,d+1,c))){let r=null;if(n.schema.linebreakReplacement){let e="pre"==n.whitespace,t=!!n.contentMatch.matchType(n.schema.linebreakReplacement);e&&!t?r=!1:!e&&t&&(r=!0)}!1===r&&S(o,e,t,a),b(o,o.mapping.slice(a).map(t,1),n,void 0,null===r);let s=o.mapping.slice(a),l=s.map(t,1),c=s.map(t+e.nodeSize,1);return o.step(new g(l,c,l+1,c-1,new i.Ji(i.FK.from(n.create(h,null,e.marks)),0,0),1,!0)),!0===r&&k(o,e,t,a),!1}}),this}setNodeMarkup(e,t,n=null,r){return!function(e,t,n,r,o){let a=e.doc.nodeAt(t);if(!a)throw RangeError("No node at given position");n||(n=a.type);let s=n.create(r,null,o||a.marks);if(a.isLeaf)return e.replaceWith(t,t+a.nodeSize,s);if(!n.validContent(a.content))throw RangeError("Invalid content for node type "+n.name);e.step(new g(t,t+a.nodeSize,t+1,t+a.nodeSize-1,new i.Ji(i.FK.from(s),0,0),1,!0))}(this,e,t,n,r),this}setNodeAttribute(e,t,n){return this.step(new D(e,t,n)),this}setDocAttribute(e,t){return this.step(new $(e,t)),this}addNodeMark(e,t){return this.step(new u(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw RangeError("No node at position "+e);if(t instanceof i.CU)t.isInSet(n.marks)&&this.step(new f(e,t));else{let i=n.marks,r,o=[];for(;r=t.isInSet(i);)o.push(new f(e,r)),i=r.removeFromSet(i);for(let e=o.length-1;e>=0;e--)this.step(o[e])}return this}split(e,t=1,n){return!function(e,t,n=1,r){let o=e.doc.resolve(t),a=i.FK.empty,s=i.FK.empty;for(let e=o.depth,t=o.depth-n,l=n-1;e>t;e--,l--){a=i.FK.from(o.node(e).copy(a));let t=r&&r[l];s=i.FK.from(t?t.type.create(t.attrs,s):o.node(e).copy(s))}e.step(new m(t,t,new i.Ji(a.append(s),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){var i;let r,o,a,s;return i=this,a=[],s=[],i.doc.nodesBetween(e,t,(i,l,c)=>{if(!i.isInline)return;let p=i.marks;if(!n.isInSet(p)&&c.type.allowsMarkType(n.type)){let c=Math.max(l,e),u=Math.min(l+i.nodeSize,t),f=n.addToSet(p);for(let e=0;e<p.length;e++)p[e].isInSet(f)||(r&&r.to==c&&r.mark.eq(p[e])?r.to=u:a.push(r=new h(c,u,p[e])));o&&o.to==c?o.to=u:s.push(o=new d(c,u,n))}}),a.forEach(e=>i.step(e)),s.forEach(e=>i.step(e)),this}removeMark(e,t,n){var r;let o,a;return r=this,o=[],a=0,r.doc.nodesBetween(e,t,(r,s)=>{if(!r.isInline)return;a++;let l=null;if(n instanceof i.sX){let e=r.marks,t;for(;t=n.isInSet(e);)(l||(l=[])).push(t),e=t.removeFromSet(e)}else n?n.isInSet(r.marks)&&(l=[n]):l=r.marks;if(l&&l.length){let n=Math.min(s+r.nodeSize,t);for(let t=0;t<l.length;t++){let i=l[t],r;for(let e=0;e<o.length;e++){let t=o[e];t.step==a-1&&i.eq(o[e].style)&&(r=t)}r?(r.to=n,r.step=a):o.push({style:i,from:Math.max(s,e),to:n,step:a})}}}),o.forEach(e=>r.step(new h(e.from,e.to,e.style))),this}clearIncompatible(e,t,n){return b(this,e,t,n),this}}},1514:(e,t,n)=>{"use strict";n.d(t,{$B:()=>p,Sd:()=>l,T2:()=>c});var i=n(808),r=n(156);let o=["ol",0],a=["ul",0],s=["li",0];function l(e,t=null){return function(n,o){let{$from:a,$to:s}=n.selection,l=a.blockRange(s);if(!l)return!1;let c=o?n.tr:null;return!!function(e,t,n,o=null){let a=!1,s=t,l=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=l.resolve(t.start-2);s=new r.u$(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new r.u$(t.$from,l.resolve(t.$to.end(t.depth)),t.depth)),a=!0}let c=(0,i.oM)(s,n,o,t);return!!c&&(e&&function(e,t,n,o,a){let s=r.FK.empty;for(let e=n.length-1;e>=0;e--)s=r.FK.from(n[e].type.create(n[e].attrs,s));e.step(new i.Wg(t.start-2*!!o,t.end,t.start,t.end,new r.Ji(s,0,0),n.length,!0));let l=0;for(let e=0;e<n.length;e++)n[e].type==a&&(l=e+1);let c=n.length-l,p=t.start+n.length-2*!!o,d=t.parent;for(let n=t.startIndex,r=t.endIndex,o=!0;n<r;n++,o=!1)!o&&(0,i.zy)(e.doc,p,c)&&(e.split(p,c),p+=2*c),p+=d.child(n).nodeSize}(e,t,c,a,n),!0)}(c,l,e,t)&&(o&&o(c.scrollIntoView()),!0)}}function c(e){return function(t,n){let{$from:o,$to:a}=t.selection,s=o.blockRange(a,t=>t.childCount>0&&t.firstChild.type==e);return!!s&&(!n||(o.node(s.depth-1).type==e?function(e,t,n,o){let a=e.tr,s=o.end,l=o.$to.end(o.depth);s<l&&(a.step(new i.Wg(s-1,l,s,l,new r.Ji(r.FK.from(n.create(null,o.parent.copy())),1,0),1,!0)),o=new r.u$(a.doc.resolve(o.$from.pos),a.doc.resolve(l),o.depth));let c=(0,i.jP)(o);if(null==c)return!1;a.lift(o,c);let p=a.doc.resolve(a.mapping.map(s,-1)-1);return(0,i.n9)(a.doc,p.pos)&&p.nodeBefore.type==p.nodeAfter.type&&a.join(p.pos),t(a.scrollIntoView()),!0}(t,n,e,s):function(e,t,n){let o=e.tr,a=n.parent;for(let e=n.end,t=n.endIndex-1,i=n.startIndex;t>i;t--)e-=a.child(t).nodeSize,o.delete(e-1,e+1);let s=o.doc.resolve(n.start),l=s.nodeAfter;if(o.mapping.map(n.end)!=n.start+s.nodeAfter.nodeSize)return!1;let c=0==n.startIndex,p=n.endIndex==a.childCount,d=s.node(-1),h=s.index(-1);if(!d.canReplace(h+ +!c,h+1,l.content.append(p?r.FK.empty:r.FK.from(a))))return!1;let u=s.pos,f=u+l.nodeSize;return o.step(new i.Wg(u-!!c,f+ +!!p,u+1,f-1,new r.Ji((c?r.FK.empty:r.FK.from(a.copy(r.FK.empty))).append(p?r.FK.empty:r.FK.from(a.copy(r.FK.empty))),+!c,+!p),+!c)),t(o.scrollIntoView()),!0}(t,n,s)))}}function p(e){return function(t,n){let{$from:o,$to:a}=t.selection,s=o.blockRange(a,t=>t.childCount>0&&t.firstChild.type==e);if(!s)return!1;let l=s.startIndex;if(0==l)return!1;let c=s.parent,p=c.child(l-1);if(p.type!=e)return!1;if(n){let o=p.lastChild&&p.lastChild.type==c.type,a=r.FK.from(o?e.create():null),l=new r.Ji(r.FK.from(e.create(null,r.FK.from(c.type.create(null,a)))),o?3:1,0),d=s.start,h=s.end;n(t.tr.step(new i.Wg(d-(o?3:1),h,d,h,l,1,!0)).scrollIntoView())}return!0}}},1580:(e,t,n)=>{"use strict";n.d(t,{A:()=>i}),n(6377);let i=n(4701).YY.create({name:"fontFamily",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{fontFamily:{default:null,parseHTML:e=>e.style.fontFamily,renderHTML:e=>e.fontFamily?{style:`font-family: ${e.fontFamily}`}:{}}}}]},addCommands:()=>({setFontFamily:e=>({chain:t})=>t().setMark("textStyle",{fontFamily:e}).run(),unsetFontFamily:()=>({chain:e})=>e().setMark("textStyle",{fontFamily:null}).removeEmptyTextStyle().run()})})},1891:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>o});var i=n(4701);let r=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,o=i.bP.create({name:"image",addOptions:()=>({inline:!1,allowBase64:!1,HTMLAttributes:{}}),inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes:()=>({src:{default:null},alt:{default:null},title:{default:null}}),parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:e}){return["img",(0,i.KV)(this.options.HTMLAttributes,e)]},addCommands(){return{setImage:e=>({commands:t})=>t.insertContent({type:this.name,attrs:e})}},addInputRules(){return[(0,i.jT)({find:r,type:this.type,getAttributes:e=>{let[,,t,n,i]=e;return{src:n,alt:t,title:i}}})]}})},2406:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("heading-3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]])},2462:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var n=Array.isArray(t)?t:t.split(",");if(0===n.length)return!0;var i=e.name||"",r=(e.type||"").toLowerCase(),o=r.replace(/\/.*$/,"");return n.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?o===t.replace(/\/.*$/,""):r===t})}return!0}},2571:(e,t,n)=>{"use strict";n.d(t,{$t:()=>S,LN:()=>a,U3:()=>p,hs:()=>C,i5:()=>f,k_:()=>A,nh:()=>h,yn:()=>s});var i=n(156),r=n(808);let o=Object.create(null);class a{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new s(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=i.Ji.empty){let n=t.content.lastChild,r=null;for(let e=0;e<t.openEnd;e++)r=n,n=n.lastChild;let o=e.steps.length,a=this.ranges;for(let s=0;s<a.length;s++){let{$from:l,$to:c}=a[s],p=e.mapping.slice(o);e.replaceRange(p.map(l.pos),p.map(c.pos),s?i.Ji.empty:t),0==s&&v(e,o,(n?n.isInline:r&&r.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,i=this.ranges;for(let r=0;r<i.length;r++){let{$from:o,$to:a}=i[r],s=e.mapping.slice(n),l=s.map(o.pos),c=s.map(a.pos);r?e.deleteRange(l,c):(e.replaceRangeWith(l,c,t),v(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let i=e.parent.inlineContent?new p(e):g(e.node(0),e.parent,e.pos,e.index(),t,n);if(i)return i;for(let i=e.depth-1;i>=0;i--){let r=t<0?g(e.node(0),e.node(i),e.before(i+1),e.index(i),t,n):g(e.node(0),e.node(i),e.after(i+1),e.index(i)+1,t,n);if(r)return r}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new f(e.node(0))}static atStart(e){return g(e,e,0,0,1)||new f(e)}static atEnd(e){return g(e,e,e.content.size,e.childCount,-1)||new f(e)}static fromJSON(e,t){if(!t||!t.type)throw RangeError("Invalid input for Selection.fromJSON");let n=o[t.type];if(!n)throw RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in o)throw RangeError("Duplicate use of selection JSON ID "+e);return o[e]=t,t.prototype.jsonID=e,t}getBookmark(){return p.between(this.$anchor,this.$head).getBookmark()}}a.prototype.visible=!0;class s{constructor(e,t){this.$from=e,this.$to=t}}let l=!1;function c(e){l||e.parent.inlineContent||(l=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class p extends a{constructor(e,t=e){c(e),c(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return a.near(n);let i=e.resolve(t.map(this.anchor));return new p(i.parent.inlineContent?i:n,n)}replace(e,t=i.Ji.empty){if(super.replace(e,t),t==i.Ji.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof p&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new d(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new p(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let i=e.resolve(t);return new this(i,n==t?i:e.resolve(n))}static between(e,t,n){let i=e.pos-t.pos;if((!n||i)&&(n=i>=0?1:-1),!t.parent.inlineContent){let e=a.findFrom(t,n,!0)||a.findFrom(t,-n,!0);if(!e)return a.near(t,n);t=e.$head}return e.parent.inlineContent||(0==i?e=t:(e=(a.findFrom(e,-n,!0)||a.findFrom(e,n,!0)).$anchor).pos<t.pos!=i<0&&(e=t)),new p(e,t)}}a.jsonID("text",p);class d{constructor(e,t){this.anchor=e,this.head=t}map(e){return new d(e.map(this.anchor),e.map(this.head))}resolve(e){return p.between(e.resolve(this.anchor),e.resolve(this.head))}}class h extends a{constructor(e){let t=e.nodeAfter;super(e,e.node(0).resolve(e.pos+t.nodeSize)),this.node=t}map(e,t){let{deleted:n,pos:i}=t.mapResult(this.anchor),r=e.resolve(i);return n?a.near(r):new h(r)}content(){return new i.Ji(i.FK.from(this.node),0,0)}eq(e){return e instanceof h&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new u(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new h(e.resolve(t.anchor))}static create(e,t){return new h(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}h.prototype.visible=!1,a.jsonID("node",h);class u{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new d(n,n):new u(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&h.isSelectable(n)?new h(t):a.near(t)}}class f extends a{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=i.Ji.empty){if(t==i.Ji.empty){e.delete(0,e.doc.content.size);let t=a.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new f(e)}map(e){return new f(e)}eq(e){return e instanceof f}getBookmark(){return m}}a.jsonID("all",f);let m={map(){return this},resolve:e=>new f(e)};function g(e,t,n,i,r,o=!1){if(t.inlineContent)return p.create(e,n);for(let a=i-(r>0?0:1);r>0?a<t.childCount:a>=0;a+=r){let i=t.child(a);if(i.isAtom){if(!o&&h.isSelectable(i))return h.create(e,n-(r<0?i.nodeSize:0))}else{let t=g(e,i,n+r,r<0?i.childCount:0,r,o);if(t)return t}n+=i.nodeSize*r}return null}function v(e,t,n){let i,o=e.steps.length-1;if(o<t)return;let s=e.steps[o];(s instanceof r.Ln||s instanceof r.Wg)&&(e.mapping.maps[o].forEach((e,t,n,r)=>{null==i&&(i=r)}),e.setSelection(a.near(e.doc.resolve(i),n)))}class b extends r.dL{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return i.CU.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||i.CU.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let i=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(i.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let r=this.storedMarks;if(!r){let e=this.doc.resolve(t);r=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,i.text(e,r)),this.selection.empty||this.setSelection(a.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function y(e,t){return t&&e?e.bind(t):e}class x{constructor(e,t,n){this.name=e,this.init=y(t.init,n),this.apply=y(t.apply,n)}}let w=[new x("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new x("selection",{init:(e,t)=>e.selection||a.atStart(t.doc),apply:e=>e.selection}),new x("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,i)=>i.selection.$cursor?e.storedMarks:null}),new x("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class k{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=w.slice(),t&&t.forEach(e=>{if(this.pluginsByKey[e.key])throw RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new x(e.key,e.spec.state,e))})}}class S{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),i=null;for(;;){let r=!1;for(let o=0;o<this.config.plugins.length;o++){let a=this.config.plugins[o];if(a.spec.appendTransaction){let s=i?i[o].n:0,l=i?i[o].state:this,c=s<t.length&&a.spec.appendTransaction.call(a,s?t.slice(s):t,l,n);if(c&&n.filterTransaction(c,o)){if(c.setMeta("appendedTransaction",e),!i){i=[];for(let e=0;e<this.config.plugins.length;e++)i.push(e<o?{state:n,n:t.length}:{state:this,n:0})}t.push(c),n=n.applyInner(c),r=!0}i&&(i[o]={state:n,n:t.length})}}if(!r)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let t=new S(this.config),n=this.config.fields;for(let i=0;i<n.length;i++){let r=n[i];t[r.name]=r.apply(e,this[r.name],this,t)}return t}get tr(){return new b(this)}static create(e){let t=new k(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new S(t);for(let i=0;i<t.fields.length;i++)n[t.fields[i].name]=t.fields[i].init(e,n);return n}reconfigure(e){let t=new k(this.schema,e.plugins),n=t.fields,i=new S(t);for(let t=0;t<n.length;t++){let r=n[t].name;i[r]=this.hasOwnProperty(r)?this[r]:n[t].init(e,i)}return i}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(e=>e.toJSON())),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let i=e[n],r=i.spec.state;r&&r.toJSON&&(t[n]=r.toJSON.call(i,this[i.key]))}return t}static fromJSON(e,t,n){if(!t)throw RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw RangeError("Required config field 'schema' missing");let r=new k(e.schema,e.plugins),o=new S(r);return r.fields.forEach(r=>{if("doc"==r.name)o.doc=i.bP.fromJSON(e.schema,t.doc);else if("selection"==r.name)o.selection=a.fromJSON(o.doc,t.selection);else if("storedMarks"==r.name)t.storedMarks&&(o.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let i in n){let a=n[i],s=a.spec.state;if(a.key==r.name&&s&&s.fromJSON&&Object.prototype.hasOwnProperty.call(t,i)){o[r.name]=s.fromJSON.call(a,e,t[i],o);return}}o[r.name]=r.init(e,o)}}),o}}class A{constructor(e){this.spec=e,this.props={},e.props&&function e(t,n,i){for(let r in t){let o=t[r];o instanceof Function?o=o.bind(n):"handleDOMEvents"==r&&(o=e(o,n,{})),i[r]=o}return i}(e.props,this,this.props),this.key=e.key?e.key.key:M("plugin")}getState(e){return e[this.key]}}let E=Object.create(null);function M(e){return e in E?e+"$"+ ++E[e]:(E[e]=0,e+"$")}class C{constructor(e="key"){this.key=M(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}},2643:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},2705:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("heading-2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]])},2713:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},2976:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("superscript",[["path",{d:"m4 19 8-8",key:"hr47gm"}],["path",{d:"m12 19-8-8",key:"1dhhmo"}],["path",{d:"M20 12h-4c0-1.5.442-2 1.5-2.5S20 8.334 20 7.002c0-.472-.17-.93-.484-1.29a2.105 2.105 0 0 0-2.617-.436c-.42.239-.738.614-.899 1.06",key:"1dfcux"}]])},3007:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});var i=n(4701);let r=/^((?:https?:)?\/\/)?((?:www|m|music)\.)?((?:youtube\.com|youtu.be|youtube-nocookie\.com))(\/(?:[\w-]+\?v=|embed\/|v\/)?)([\w-]+)(\S+)?$/,o=/^((?:https?:)?\/\/)?((?:www|m|music)\.)?((?:youtube\.com|youtu.be|youtube-nocookie\.com))(\/(?:[\w-]+\?v=|embed\/|v\/)?)([\w-]+)(\S+)?$/g,a=e=>e.match(r),s=(e,t)=>t?"https://www.youtube-nocookie.com/embed/videoseries?list=":e?"https://www.youtube-nocookie.com/embed/":"https://www.youtube.com/embed/",l=e=>{let{url:t,allowFullscreen:n,autoplay:i,ccLanguage:r,ccLoadPolicy:o,controls:l,disableKBcontrols:c,enableIFrameApi:p,endTime:d,interfaceLanguage:h,ivLoadPolicy:u,loop:f,modestBranding:m,nocookie:g,origin:v,playlist:b,progressBarColor:y,startAt:x,rel:w}=e;if(!a(t))return null;if(t.includes("/embed/"))return t;if(t.includes("youtu.be")){let e=t.split("/").pop();return e?`${s(g)}${e}`:null}let k=/(?:(v|list)=|shorts\/)([-\w]+)/gm.exec(t);if(!k||!k[2])return null;let S=`${s(g,"list"===k[1])}${k[2]}`,A=[];return!1===n&&A.push("fs=0"),i&&A.push("autoplay=1"),r&&A.push(`cc_lang_pref=${r}`),o&&A.push("cc_load_policy=1"),l||A.push("controls=0"),c&&A.push("disablekb=1"),p&&A.push("enablejsapi=1"),d&&A.push(`end=${d}`),h&&A.push(`hl=${h}`),u&&A.push(`iv_load_policy=${u}`),f&&A.push("loop=1"),m&&A.push("modestbranding=1"),v&&A.push(`origin=${v}`),b&&A.push(`playlist=${b}`),x&&A.push(`start=${x}`),y&&A.push(`color=${y}`),void 0!==w&&A.push(`rel=${w}`),A.length&&(S+=`${"v"===k[1]?"?":"&"}${A.join("&")}`),S},c=i.bP.create({name:"youtube",addOptions:()=>({addPasteHandler:!0,allowFullscreen:!0,autoplay:!1,ccLanguage:void 0,ccLoadPolicy:void 0,controls:!0,disableKBcontrols:!1,enableIFrameApi:!1,endTime:0,height:480,interfaceLanguage:void 0,ivLoadPolicy:0,loop:!1,modestBranding:!1,HTMLAttributes:{},inline:!1,nocookie:!1,origin:"",playlist:"",progressBarColor:void 0,width:640,rel:1}),inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes(){return{src:{default:null},start:{default:0},width:{default:this.options.width},height:{default:this.options.height}}},parseHTML:()=>[{tag:"div[data-youtube-video] iframe"}],addCommands(){return{setYoutubeVideo:e=>({commands:t})=>!!a(e.src)&&t.insertContent({type:this.name,attrs:e})}},addPasteRules(){return this.options.addPasteHandler?[(0,i.gg)({find:o,type:this.type,getAttributes:e=>({src:e.input})})]:[]},renderHTML({HTMLAttributes:e}){let t=l({url:e.src,allowFullscreen:this.options.allowFullscreen,autoplay:this.options.autoplay,ccLanguage:this.options.ccLanguage,ccLoadPolicy:this.options.ccLoadPolicy,controls:this.options.controls,disableKBcontrols:this.options.disableKBcontrols,enableIFrameApi:this.options.enableIFrameApi,endTime:this.options.endTime,interfaceLanguage:this.options.interfaceLanguage,ivLoadPolicy:this.options.ivLoadPolicy,loop:this.options.loop,modestBranding:this.options.modestBranding,nocookie:this.options.nocookie,origin:this.options.origin,playlist:this.options.playlist,progressBarColor:this.options.progressBarColor,startAt:e.start||0,rel:this.options.rel});return e.src=t,["div",{"data-youtube-video":""},["iframe",(0,i.KV)(this.options.HTMLAttributes,{width:this.options.width,height:this.options.height,allowfullscreen:this.options.allowFullscreen,autoplay:this.options.autoplay,ccLanguage:this.options.ccLanguage,ccLoadPolicy:this.options.ccLoadPolicy,disableKBcontrols:this.options.disableKBcontrols,enableIFrameApi:this.options.enableIFrameApi,endTime:this.options.endTime,interfaceLanguage:this.options.interfaceLanguage,ivLoadPolicy:this.options.ivLoadPolicy,loop:this.options.loop,modestBranding:this.options.modestBranding,origin:this.options.origin,playlist:this.options.playlist,progressBarColor:this.options.progressBarColor,rel:this.options.rel},e)]]}})},3083:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var i=n(4701),r=n(2571);let o=i.YY.create({name:"characterCount",addOptions:()=>({limit:null,mode:"textSize",textCounter:e=>e.length,wordCounter:e=>e.split(" ").filter(e=>""!==e).length}),addStorage:()=>({characters:()=>0,words:()=>0}),onBeforeCreate(){this.storage.characters=e=>{let t=(null==e?void 0:e.node)||this.editor.state.doc;if("textSize"===((null==e?void 0:e.mode)||this.options.mode)){let e=t.textBetween(0,t.content.size,void 0," ");return this.options.textCounter(e)}return t.nodeSize},this.storage.words=e=>{let t=(null==e?void 0:e.node)||this.editor.state.doc,n=t.textBetween(0,t.content.size," "," ");return this.options.wordCounter(n)}},addProseMirrorPlugins(){let e=!1;return[new r.k_({key:new r.hs("characterCount"),appendTransaction:(t,n,i)=>{if(e)return;let r=this.options.limit;if(null==r||0===r){e=!0;return}let o=this.storage.characters({node:i.doc});if(o>r){console.warn(`[CharacterCount] Initial content exceeded limit of ${r} characters. Content was automatically trimmed.`);let t=i.tr.deleteRange(0,o-r);return e=!0,t}e=!0},filterTransaction:(e,t)=>{let n=this.options.limit;if(!e.docChanged||0===n||null==n)return!0;let i=this.storage.characters({node:t.doc}),r=this.storage.characters({node:e.doc});if(r<=n||i>n&&r>n&&r<=i)return!0;if(i>n&&r>n&&r>i||!e.getMeta("paste"))return!1;let o=e.selection.$head.pos;return e.deleteRange(o-(r-n),o),!(this.storage.characters({node:e.doc})>n)}})]}})},3127:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},3332:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3356:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var i=n(4701);let r=i.bP.create({name:"taskList",addOptions:()=>({itemTypeName:"taskItem",HTMLAttributes:{}}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:`ul[data-type="${this.name}"]`,priority:51}]},renderHTML({HTMLAttributes:e}){return["ul",(0,i.KV)(this.options.HTMLAttributes,e,{"data-type":this.name}),0]},addCommands(){return{toggleTaskList:()=>({commands:e})=>e.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-9":()=>this.editor.commands.toggleTaskList()}}})},3500:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("type",[["path",{d:"M12 4v16",key:"1654pz"}],["path",{d:"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2",key:"e0r10z"}],["path",{d:"M9 20h6",key:"s66wpe"}]])},3557:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var i=n(4701);let r=i.bP.create({name:"tableCell",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:e=>{let t=e.getAttribute("colwidth");return t?t.split(",").map(e=>parseInt(e,10)):null}}}),tableRole:"cell",isolating:!0,parseHTML:()=>[{tag:"td"}],renderHTML({HTMLAttributes:e}){return["td",(0,i.KV)(this.options.HTMLAttributes,e),0]}})},3654:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},3709:(e,t,n)=>{"use strict";n.d(t,{A:()=>c});let i=e=>({IMPORTANT:{scope:"meta",begin:"!important"},BLOCK_COMMENT:e.C_BLOCK_COMMENT_MODE,HEXCOLOR:{scope:"number",begin:/#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\b/},FUNCTION_DISPATCH:{className:"built_in",begin:/[\w-]+(?=\()/},ATTRIBUTE_SELECTOR_MODE:{scope:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE]},CSS_NUMBER_MODE:{scope:"number",begin:e.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},CSS_VARIABLE:{className:"attr",begin:/--[A-Za-z_][A-Za-z0-9_-]*/}}),r=["a","abbr","address","article","aside","audio","b","blockquote","body","button","canvas","caption","cite","code","dd","del","details","dfn","div","dl","dt","em","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","html","i","iframe","img","input","ins","kbd","label","legend","li","main","mark","menu","nav","object","ol","optgroup","option","p","picture","q","quote","samp","section","select","source","span","strong","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","tr","ul","var","video","defs","g","marker","mask","pattern","svg","switch","symbol","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feFlood","feGaussianBlur","feImage","feMerge","feMorphology","feOffset","feSpecularLighting","feTile","feTurbulence","linearGradient","radialGradient","stop","circle","ellipse","image","line","path","polygon","polyline","rect","text","use","textPath","tspan","foreignObject","clipPath"],o=["any-hover","any-pointer","aspect-ratio","color","color-gamut","color-index","device-aspect-ratio","device-height","device-width","display-mode","forced-colors","grid","height","hover","inverted-colors","monochrome","orientation","overflow-block","overflow-inline","pointer","prefers-color-scheme","prefers-contrast","prefers-reduced-motion","prefers-reduced-transparency","resolution","scan","scripting","update","width","min-width","max-width","min-height","max-height"].sort().reverse(),a=["active","any-link","blank","checked","current","default","defined","dir","disabled","drop","empty","enabled","first","first-child","first-of-type","fullscreen","future","focus","focus-visible","focus-within","has","host","host-context","hover","indeterminate","in-range","invalid","is","lang","last-child","last-of-type","left","link","local-link","not","nth-child","nth-col","nth-last-child","nth-last-col","nth-last-of-type","nth-of-type","only-child","only-of-type","optional","out-of-range","past","placeholder-shown","read-only","read-write","required","right","root","scope","target","target-within","user-invalid","valid","visited","where"].sort().reverse(),s=["after","backdrop","before","cue","cue-region","first-letter","first-line","grammar-error","marker","part","placeholder","selection","slotted","spelling-error"].sort().reverse(),l=["accent-color","align-content","align-items","align-self","alignment-baseline","all","anchor-name","animation","animation-composition","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-range","animation-range-end","animation-range-start","animation-timeline","animation-timing-function","appearance","aspect-ratio","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","block-size","border","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-end-end-radius","border-end-start-radius","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-start-end-radius","border-start-start-radius","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-align","box-decoration-break","box-direction","box-flex","box-flex-group","box-lines","box-ordinal-group","box-orient","box-pack","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","color-scheme","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","contain-intrinsic-block-size","contain-intrinsic-height","contain-intrinsic-inline-size","contain-intrinsic-size","contain-intrinsic-width","container","container-name","container-type","content","content-visibility","counter-increment","counter-reset","counter-set","cue","cue-after","cue-before","cursor","cx","cy","direction","display","dominant-baseline","empty-cells","enable-background","field-sizing","fill","fill-opacity","fill-rule","filter","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","flood-color","flood-opacity","flow","font","font-display","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-palette","font-size","font-size-adjust","font-smooth","font-smoothing","font-stretch","font-style","font-synthesis","font-synthesis-position","font-synthesis-small-caps","font-synthesis-style","font-synthesis-weight","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-emoji","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","forced-color-adjust","gap","glyph-orientation-horizontal","glyph-orientation-vertical","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphenate-character","hyphenate-limit-chars","hyphens","icon","image-orientation","image-rendering","image-resolution","ime-mode","initial-letter","initial-letter-align","inline-size","inset","inset-area","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","kerning","left","letter-spacing","lighting-color","line-break","line-height","line-height-step","list-style","list-style-image","list-style-position","list-style-type","margin","margin-block","margin-block-end","margin-block-start","margin-bottom","margin-inline","margin-inline-end","margin-inline-start","margin-left","margin-right","margin-top","margin-trim","marker","marker-end","marker-mid","marker-start","marks","mask","mask-border","mask-border-mode","mask-border-outset","mask-border-repeat","mask-border-slice","mask-border-source","mask-border-width","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","masonry-auto-flow","math-depth","math-shift","math-style","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","nav-down","nav-index","nav-left","nav-right","nav-up","none","normal","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-anchor","overflow-block","overflow-clip-margin","overflow-inline","overflow-wrap","overflow-x","overflow-y","overlay","overscroll-behavior","overscroll-behavior-block","overscroll-behavior-inline","overscroll-behavior-x","overscroll-behavior-y","padding","padding-block","padding-block-end","padding-block-start","padding-bottom","padding-inline","padding-inline-end","padding-inline-start","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","paint-order","pause","pause-after","pause-before","perspective","perspective-origin","place-content","place-items","place-self","pointer-events","position","position-anchor","position-visibility","print-color-adjust","quotes","r","resize","rest","rest-after","rest-before","right","rotate","row-gap","ruby-align","ruby-position","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-stop","scroll-snap-type","scroll-timeline","scroll-timeline-axis","scroll-timeline-name","scrollbar-color","scrollbar-gutter","scrollbar-width","shape-image-threshold","shape-margin","shape-outside","shape-rendering","speak","speak-as","src","stop-color","stop-opacity","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","tab-size","table-layout","text-align","text-align-all","text-align-last","text-anchor","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-decoration-thickness","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-indent","text-justify","text-orientation","text-overflow","text-rendering","text-shadow","text-size-adjust","text-transform","text-underline-offset","text-underline-position","text-wrap","text-wrap-mode","text-wrap-style","timeline-scope","top","touch-action","transform","transform-box","transform-origin","transform-style","transition","transition-behavior","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-modify","user-select","vector-effect","vertical-align","view-timeline","view-timeline-axis","view-timeline-inset","view-timeline-name","view-transition-name","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","white-space","white-space-collapse","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","x","y","z-index","zoom"].sort().reverse();function c(e){let t=e.regex,n=i(e),c=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE];return{name:"CSS",case_insensitive:!0,illegal:/[=|'\$]/,keywords:{keyframePosition:"from to"},classNameAliases:{keyframePosition:"selector-tag"},contains:[n.BLOCK_COMMENT,{begin:/-(webkit|moz|ms|o)-(?=[a-z])/},n.CSS_NUMBER_MODE,{className:"selector-id",begin:/#[A-Za-z0-9_-]+/,relevance:0},{className:"selector-class",begin:"\\.[a-zA-Z-][a-zA-Z0-9_-]*",relevance:0},n.ATTRIBUTE_SELECTOR_MODE,{className:"selector-pseudo",variants:[{begin:":("+a.join("|")+")"},{begin:":(:)?("+s.join("|")+")"}]},n.CSS_VARIABLE,{className:"attribute",begin:"\\b("+l.join("|")+")\\b"},{begin:/:/,end:/[;}{]/,contains:[n.BLOCK_COMMENT,n.HEXCOLOR,n.IMPORTANT,n.CSS_NUMBER_MODE,...c,{begin:/(url|data-uri)\(/,end:/\)/,relevance:0,keywords:{built_in:"url data-uri"},contains:[...c,{className:"string",begin:/[^)]/,endsWithParent:!0,excludeEnd:!0}]},n.FUNCTION_DISPATCH]},{begin:t.lookahead(/@/),end:"[{;]",relevance:0,illegal:/:/,contains:[{className:"keyword",begin:/@-?\w[\w]*(-\w+)*/},{begin:/\s/,endsWithParent:!0,excludeEnd:!0,relevance:0,keywords:{$pattern:/[a-z-]+/,keyword:"and or not only",attribute:o.join(" ")},contains:[{begin:/[a-z-]+(?=:)/,className:"attribute"},...c,n.CSS_NUMBER_MODE]}]},{className:"selector-tag",begin:"\\b("+r.join("|")+")\\b"}]}}},4109:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=n(4701).YY.create({name:"textAlign",addOptions:()=>({types:[],alignments:["left","center","right","justify"],defaultAlignment:null}),addGlobalAttributes(){return[{types:this.options.types,attributes:{textAlign:{default:this.options.defaultAlignment,parseHTML:e=>{let t=e.style.textAlign;return this.options.alignments.includes(t)?t:this.options.defaultAlignment},renderHTML:e=>e.textAlign?{style:`text-align: ${e.textAlign}`}:{}}}}]},addCommands(){return{setTextAlign:e=>({commands:t})=>!!this.options.alignments.includes(e)&&this.options.types.map(n=>t.updateAttributes(n,{textAlign:e})).every(e=>e),unsetTextAlign:()=>({commands:e})=>this.options.types.map(t=>e.resetAttributes(t,"textAlign")).every(e=>e),toggleTextAlign:e=>({editor:t,commands:n})=>!!this.options.alignments.includes(e)&&(t.isActive({textAlign:e})?n.unsetTextAlign():n.setTextAlign(e))}},addKeyboardShortcuts(){return{"Mod-Shift-l":()=>this.editor.commands.setTextAlign("left"),"Mod-Shift-e":()=>this.editor.commands.setTextAlign("center"),"Mod-Shift-r":()=>this.editor.commands.setTextAlign("right"),"Mod-Shift-j":()=>this.editor.commands.setTextAlign("justify")}}})},4186:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4347:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("highlighter",[["path",{d:"m9 11-6 6v3h9l3-3",key:"1a3l36"}],["path",{d:"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4",key:"14a9rk"}]])},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4589:(e,t,n)=>{"use strict";n.d(t,{A:()=>i}),n(6377);let i=n(4701).YY.create({name:"color",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:e=>{var t;return null==(t=e.style.color)?void 0:t.replace(/['"]+/g,"")},renderHTML:e=>e.color?{style:`color: ${e.color}`}:{}}}}]},addCommands:()=>({setColor:e=>({chain:t})=>t().setMark("textStyle",{color:e}).run(),unsetColor:()=>({chain:e})=>e().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()})})},4652:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>a});var i=n(4701);let r=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,o=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,a=i.CU.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:e=>e.getAttribute("data-color")||e.style.backgroundColor,renderHTML:e=>e.color?{"data-color":e.color,style:`background-color: ${e.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:e}){return["mark",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setHighlight:e=>({commands:t})=>t.setMark(this.name,e),toggleHighlight:e=>({commands:t})=>t.toggleMark(this.name,e),unsetHighlight:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[(0,i.OX)({find:r,type:this.type})]},addPasteRules(){return[(0,i.Zc)({find:o,type:this.type})]}})},4923:(e,t,n)=>{"use strict";function i(e){let t=e.regex,n=t.concat(/[\p{L}_]/u,t.optional(/[\p{L}0-9_.-]*:/u),/[\p{L}0-9_.-]*/u),i={className:"symbol",begin:/&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/},r={begin:/\s/,contains:[{className:"keyword",begin:/#?[a-z_][a-z1-9_-]+/,illegal:/\n/}]},o=e.inherit(r,{begin:/\(/,end:/\)/}),a=e.inherit(e.APOS_STRING_MODE,{className:"string"}),s=e.inherit(e.QUOTE_STRING_MODE,{className:"string"}),l={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:/[\p{L}0-9._:-]+/u,relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[i]},{begin:/'/,end:/'/,contains:[i]},{begin:/[^\s"'=<>`]+/}]}]}]};return{name:"HTML, XML",aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,unicodeRegex:!0,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,relevance:10,contains:[r,s,a,o,{begin:/\[/,end:/\]/,contains:[{className:"meta",begin:/<![a-z]/,end:/>/,contains:[r,o,s,a]}]}]},e.COMMENT(/<!--/,/-->/,{relevance:10}),{begin:/<!\[CDATA\[/,end:/\]\]>/,relevance:10},i,{className:"meta",end:/\?>/,variants:[{begin:/<\?xml/,relevance:10,contains:[s]},{begin:/<\?[a-z][a-z0-9]+/}]},{className:"tag",begin:/<style(?=\s|>)/,end:/>/,keywords:{name:"style"},contains:[l],starts:{end:/<\/style>/,returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:/<script(?=\s|>)/,end:/>/,keywords:{name:"script"},contains:[l],starts:{end:/<\/script>/,returnEnd:!0,subLanguage:["javascript","handlebars","xml"]}},{className:"tag",begin:/<>|<\/>/},{className:"tag",begin:t.concat(/</,t.lookahead(t.concat(n,t.either(/\/>/,/>/,/\s/)))),end:/\/?>/,contains:[{className:"name",begin:n,relevance:0,starts:l}]},{className:"tag",begin:t.concat(/<\//,t.lookahead(t.concat(n,/>/))),contains:[{className:"name",begin:n,relevance:0},{begin:/>/,relevance:0,endsParent:!0}]}]}}n.d(t,{A:()=>i})},4927:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var i=n(4701);let r=i.CU.create({name:"subscript",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"sub"},{style:"vertical-align",getAttrs:e=>"sub"===e&&null}],renderHTML({HTMLAttributes:e}){return["sub",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setSubscript:()=>({commands:e})=>e.setMark(this.name),toggleSubscript:()=>({commands:e})=>e.toggleMark(this.name),unsetSubscript:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-,":()=>this.editor.commands.toggleSubscript()}}})},5109:(e,t,n)=>{"use strict";n.d(t,{$Z:()=>m,hG:()=>E});var i,r,o=n(2115),a=n(7650),s=n(4701),l={exports:{}},c={};l.exports=function(){if(i)return c;i=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=o.useState,n=o.useEffect,r=o.useLayoutEffect,a=o.useDebugValue;function s(t){var n=t.getSnapshot;t=t.value;try{var i=n();return!e(t,i)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,i){var o=i(),l=t({inst:{value:o,getSnapshot:i}}),c=l[0].inst,p=l[1];return r(function(){c.value=o,c.getSnapshot=i,s(c)&&p({inst:c})},[e,o,i]),n(function(){return s(c)&&p({inst:c}),e(function(){s(c)&&p({inst:c})})},[e]),a(o),o};return c.useSyncExternalStore=void 0!==o.useSyncExternalStore?o.useSyncExternalStore:l,c}();var p=l.exports;let d=(...e)=>t=>{e.forEach(e=>{"function"==typeof e?e(t):e&&(e.current=t)})},h=({contentComponent:e})=>{let t=p.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getServerSnapshot);return o.createElement(o.Fragment,null,Object.values(t))};class u extends o.Component{constructor(e){var t;super(e),this.editorContentRef=o.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(t=e.editor)?void 0:t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;let t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=function(){let e=new Set,t={};return{subscribe:t=>(e.add(t),()=>{e.delete(t)}),getSnapshot:()=>t,getServerSnapshot:()=>t,setRenderer(n,i){t={...t,[n]:a.createPortal(i.reactElement,i.element,n)},e.forEach(e=>e())},removeRenderer(n){let i={...t};delete i[n],t=i,e.forEach(e=>e())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe(()=>{this.setState(e=>e.hasContentComponentInitialized?e:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let e=this.props.editor;if(!e||(this.initialized=!1,e.isDestroyed||e.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),e.contentComponent=null,!e.options.element.firstChild))return;let t=document.createElement("div");t.append(...e.options.element.childNodes),e.setOptions({element:t})}render(){let{editor:e,innerRef:t,...n}=this.props;return o.createElement(o.Fragment,null,o.createElement("div",{ref:d(t,this.editorContentRef),...n}),(null==e?void 0:e.contentComponent)&&o.createElement(h,{contentComponent:e.contentComponent}))}}let f=(0,o.forwardRef)((e,t)=>{let n=o.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[e.editor]);return o.createElement(u,{key:n,innerRef:t,...e})}),m=o.memo(f);var g=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;if(Array.isArray(t)){if((i=t.length)!=n.length)return!1;for(r=i;0!=r--;)if(!e(t[r],n[r]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(r of t.entries())if(!n.has(r[0]))return!1;for(r of t.entries())if(!e(r[1],n.get(r[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(r of t.entries())if(!n.has(r[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((i=t.length)!=n.length)return!1;for(r=i;0!=r--;)if(t[r]!==n[r])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((i=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(r=i;0!=r--;)if(!Object.prototype.hasOwnProperty.call(n,o[r]))return!1;for(r=i;0!=r--;){var i,r,o,a=o[r];if(("_owner"!==a||!t.$$typeof)&&!e(t[a],n[a]))return!1}return!0}return t!=t&&n!=n}),v={exports:{}},b={};v.exports=function(){if(r)return b;r=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=p.useSyncExternalStore,n=o.useRef,i=o.useEffect,a=o.useMemo,s=o.useDebugValue;return b.useSyncExternalStoreWithSelector=function(r,o,l,c,p){var d=n(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;var u=t(r,(d=a(function(){function t(t){if(!r){if(r=!0,n=t,t=c(t),void 0!==p&&h.hasValue){var o=h.value;if(p(o,t))return i=o}return i=t}if(o=i,e(n,t))return o;var a=c(t);return void 0!==p&&p(o,a)?o:(n=t,i=a)}var n,i,r=!1,a=void 0===l?null:l;return[function(){return t(o())},null===a?void 0:function(){return t(a())}]},[o,l,c,p]))[0],d[1]);return i(function(){h.hasValue=!0,h.value=u},[u]),s(u),u},b}();var y=v.exports;let x="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;class w{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){let e=()=>{this.transactionNumber+=1,this.subscribers.forEach(e=>e())},t=this.editor;return t.on("transaction",e),()=>{t.off("transaction",e)}}}}let k="undefined"==typeof window,S=k||!!("undefined"!=typeof window&&window.next);class A{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(e=>e())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?k||S?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){let e={...this.options.current,onBeforeCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBeforeCreate)?void 0:n.call(t,...e)},onBlur:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBlur)?void 0:n.call(t,...e)},onCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onCreate)?void 0:n.call(t,...e)},onDestroy:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDestroy)?void 0:n.call(t,...e)},onFocus:(...e)=>{var t,n;return null==(n=(t=this.options.current).onFocus)?void 0:n.call(t,...e)},onSelectionUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onSelectionUpdate)?void 0:n.call(t,...e)},onTransaction:(...e)=>{var t,n;return null==(n=(t=this.options.current).onTransaction)?void 0:n.call(t,...e)},onUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onUpdate)?void 0:n.call(t,...e)},onContentError:(...e)=>{var t,n;return null==(n=(t=this.options.current).onContentError)?void 0:n.call(t,...e)},onDrop:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDrop)?void 0:n.call(t,...e)},onPaste:(...e)=>{var t,n;return null==(n=(t=this.options.current).onPaste)?void 0:n.call(t,...e)}};return new s.KE(e)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&e.extensions&&t.extensions?e.extensions.length===t.extensions.length&&e.extensions.every((e,n)=>{var i;return e===(null==(i=t.extensions)?void 0:i[n])}):e[n]===t[n]))}onRender(e){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===e.length?A.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(e),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=e;return}if(this.previousDeps.length===e.length&&this.previousDeps.every((t,n)=>t===e[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){let e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===e){t&&t.setOptions(this.options.current);return}t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))},1)}}function E(e={},t=[]){let n=(0,o.useRef)(e);n.current=e;let[i]=(0,o.useState)(()=>new A(n)),r=p.useSyncExternalStore(i.subscribe,i.getEditor,i.getServerSnapshot);return(0,o.useDebugValue)(r),(0,o.useEffect)(i.onRender(t)),!function(e){var t;let[n]=(0,o.useState)(()=>new w(e.editor)),i=y.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,null!=(t=e.equalityFn)?t:g);x(()=>n.watch(e.editor),[e.editor,n]),(0,o.useDebugValue)(i)}({editor:r,selector:({transactionNumber:t})=>!1===e.shouldRerenderOnTransaction?null:e.immediatelyRender&&0===t?0:t+1}),r}let M=((0,o.createContext)({editor:null}).Consumer,(0,o.createContext)({onDragStart:void 0})),C=()=>(0,o.useContext)(M);o.forwardRef((e,t)=>{let{onDragStart:n}=C(),i=e.as||"div";return o.createElement(i,{...e,ref:t,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...e.style}})});class T{constructor(e,{editor:t,props:n={},as:i="div",className:r=""}){this.ref=null,this.id=Math.floor(0xffffffff*Math.random()).toString(),this.component=e,this.editor=t,this.props=n,this.element=document.createElement(i),this.element.classList.add("react-renderer"),r&&this.element.classList.add(...r.split(" ")),this.editor.isInitialized?flushSync(()=>{this.render()}):this.render()}render(){var e,t;let n=this.component,i=this.props,r=this.editor;("function"==typeof n&&n.prototype&&n.prototype.isReactComponent||"object"==typeof n&&(null==(t=n.$$typeof)?void 0:t.toString())==="Symbol(react.forward_ref)")&&(i.ref=e=>{this.ref=e}),this.reactElement=React.createElement(n,{...i}),null==(e=null==r?void 0:r.contentComponent)||e.setRenderer(this.id,this)}updateProps(e={}){this.props={...this.props,...e},this.render()}destroy(){var e;let t=this.editor;null==(e=null==t?void 0:t.contentComponent)||e.removeRenderer(this.id)}updateAttributes(e){Object.keys(e).forEach(t=>{this.element.setAttribute(t,e[t])})}}},5112:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},5254:(e,t,n)=>{"use strict";n.d(t,{A:()=>er});var i=n(5383),r=n(4701),o=n(2571),a=n(2695);class s{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function l(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function c(e,...t){let n=Object.create(null);for(let t in e)n[t]=e[t];return t.forEach(function(e){for(let t in e)n[t]=e[t]}),n}let p=e=>!!e.scope,d=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){let n=e.split(".");return[`${t}${n.shift()}`,...n.map((e,t)=>`${e}${"_".repeat(t+1)}`)].join(" ")}return`${t}${e}`};class h{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=l(e)}openNode(e){if(!p(e))return;let t=d(e.scope,{prefix:this.classPrefix});this.span(t)}closeNode(e){p(e)&&(this.buffer+="</span>")}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}let u=(e={})=>{let t={children:[]};return Object.assign(t,e),t};class f{constructor(){this.rootNode=u(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){let t=u({scope:e});this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return"string"==typeof t?e.addText(t):t.children&&(e.openNode(t),t.children.forEach(t=>this._walk(e,t)),e.closeNode(t)),e}static _collapse(e){"string"!=typeof e&&e.children&&(e.children.every(e=>"string"==typeof e)?e.children=[e.children.join("")]:e.children.forEach(e=>{f._collapse(e)}))}}class m extends f{constructor(e){super(),this.options=e}addText(e){""!==e&&this.add(e)}startScope(e){this.openNode(e)}endScope(){this.closeNode()}__addSublanguage(e,t){let n=e.root;t&&(n.scope=`language:${t}`),this.add(n)}toHTML(){return new h(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function g(e){return e?"string"==typeof e?e:e.source:null}function v(e){return x("(?=",e,")")}function b(e){return x("(?:",e,")*")}function y(e){return x("(?:",e,")?")}function x(...e){return e.map(e=>g(e)).join("")}function w(...e){return"("+(function(e){let t=e[e.length-1];return"object"==typeof t&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}(e).capture?"":"?:")+e.map(e=>g(e)).join("|")+")"}function k(e){return RegExp(e.toString()+"|").exec("").length-1}let S=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function A(e,{joinWith:t}){let n=0;return e.map(e=>{let t=n+=1,i=g(e),r="";for(;i.length>0;){let e=S.exec(i);if(!e){r+=i;break}r+=i.substring(0,e.index),i=i.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?r+="\\"+String(Number(e[1])+t):(r+=e[0],"("===e[0]&&n++)}return r}).map(e=>`(${e})`).join(t)}let E="[a-zA-Z]\\w*",M="[a-zA-Z_]\\w*",C="\\b\\d+(\\.\\d+)?",T="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",O="\\b(0b[01]+)",N={begin:"\\\\[\\s\\S]",relevance:0},R=function(e,t,n={}){let i=c({scope:"comment",begin:e,end:t,contains:[]},n);i.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});let r=w("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return i.contains.push({begin:x(/[ ]+/,"(",r,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),i},L=R("//","$"),z=R("/\\*","\\*/"),I=R("#","$");var j=Object.freeze({__proto__:null,APOS_STRING_MODE:{scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[N]},BACKSLASH_ESCAPE:N,BINARY_NUMBER_MODE:{scope:"number",begin:O,relevance:0},BINARY_NUMBER_RE:O,COMMENT:R,C_BLOCK_COMMENT_MODE:z,C_LINE_COMMENT_MODE:L,C_NUMBER_MODE:{scope:"number",begin:T,relevance:0},C_NUMBER_RE:T,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})},HASH_COMMENT_MODE:I,IDENT_RE:E,MATCH_NOTHING_RE:/\b\B/,METHOD_GUARD:{begin:"\\.\\s*"+M,relevance:0},NUMBER_MODE:{scope:"number",begin:C,relevance:0},NUMBER_RE:C,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},QUOTE_STRING_MODE:{scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[N]},REGEXP_MODE:{scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[N,{begin:/\[/,end:/\]/,relevance:0,contains:[N]}]},RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(e={})=>{let t=/^#![ ]*\//;return e.binary&&(e.begin=x(t,/.*\b/,e.binary,/\b.*/)),c({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{0!==e.index&&t.ignoreMatch()}},e)},TITLE_MODE:{scope:"title",begin:E,relevance:0},UNDERSCORE_IDENT_RE:M,UNDERSCORE_TITLE_MODE:{scope:"title",begin:M,relevance:0}});function _(e,t){"."===e.input[e.index-1]&&t.ignoreMatch()}function D(e,t){void 0!==e.className&&(e.scope=e.className,delete e.className)}function $(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=_,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance&&(e.relevance=0))}function P(e,t){Array.isArray(e.illegal)&&(e.illegal=w(...e.illegal))}function B(e,t){if(e.match){if(e.begin||e.end)throw Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function F(e,t){void 0===e.relevance&&(e.relevance=1)}let H=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw Error("beforeMatch cannot be used with starts");let n=Object.assign({},e);Object.keys(e).forEach(t=>{delete e[t]}),e.keywords=n.keywords,e.begin=x(n.beforeMatch,v(n.begin)),e.starts={relevance:0,contains:[Object.assign(n,{endsParent:!0})]},e.relevance=0,delete n.beforeMatch},K=["of","and","for","in","not","or","if","then","parent","list","value"],U={},J=e=>{console.error(e)},q=(e,...t)=>{console.log(`WARN: ${e}`,...t)},W=(e,t)=>{U[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),U[`${e}/${t}`]=!0)},V=Error();function G(e,t,{key:n}){let i=0,r=e[n],o={},a={};for(let e=1;e<=t.length;e++)a[e+i]=r[e],o[e+i]=!0,i+=k(t[e-1]);e[n]=a,e[n]._emit=o,e[n]._multi=!0}function Z(e){if(e.scope&&"object"==typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope),"string"==typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"==typeof e.endScope&&(e.endScope={_wrap:e.endScope}),Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw J("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),V;if("object"!=typeof e.beginScope||null===e.beginScope)throw J("beginScope must be object"),V;G(e,e.begin,{key:"beginScope"}),e.begin=A(e.begin,{joinWith:""})}if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw J("skip, excludeEnd, returnEnd not compatible with endScope: {}"),V;if("object"!=typeof e.endScope||null===e.endScope)throw J("endScope must be object"),V;G(e,e.end,{key:"endScope"}),e.end=A(e.end,{joinWith:""})}}class X extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}let Y=Symbol("nomatch"),Q=function(e){let t=Object.create(null),n=Object.create(null),i=[],r=!0,o="Could not find the language '{}', did you forget to load/include a language module?",a={disableAutodetect:!0,name:"Plain text",contains:[]},p={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:m};function d(e){return p.noHighlightRe.test(e)}function h(e,t,n){let i="",r="";"object"==typeof t?(i=e,n=t.ignoreIllegals,r=t.language):(W("10.7.0","highlight(lang, code, ...args) has been deprecated."),W("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),r=e,i=t),void 0===n&&(n=!0);let o={code:i,language:r};N("before:highlight",o);let a=o.result?o.result:u(o.language,o.code,n);return a.code=o.code,N("after:highlight",a),a}function u(e,n,i,a){let d=Object.create(null);function h(){if(!T.keywords)return void N.addText(L);let e=0;T.keywordPatternRe.lastIndex=0;let t=T.keywordPatternRe.exec(L),n="";for(;t;){n+=L.substring(e,t.index);let i=S.case_insensitive?t[0].toLowerCase():t[0],r=T.keywords[i];if(r){let[e,o]=r;if(N.addText(n),n="",d[i]=(d[i]||0)+1,d[i]<=7&&(z+=o),e.startsWith("_"))n+=t[0];else{let n=S.classNameAliases[e]||e;v(t[0],n)}}else n+=t[0];e=T.keywordPatternRe.lastIndex,t=T.keywordPatternRe.exec(L)}n+=L.substring(e),N.addText(n)}function m(){null!=T.subLanguage?function(){if(""===L)return;let e=null;if("string"==typeof T.subLanguage){if(!t[T.subLanguage])return N.addText(L);e=u(T.subLanguage,L,!0,O[T.subLanguage]),O[T.subLanguage]=e._top}else e=f(L,T.subLanguage.length?T.subLanguage:null);T.relevance>0&&(z+=e.relevance),N.__addSublanguage(e._emitter,e.language)}():h(),L=""}function v(e,t){""!==e&&(N.startScope(t),N.addText(e),N.endScope())}function b(e,t){let n=1,i=t.length-1;for(;n<=i;){if(!e._emit[n]){n++;continue}let i=S.classNameAliases[e[n]]||e[n],r=t[n];i?v(r,i):(L=r,h(),L=""),n++}}function y(e,t){return e.scope&&"string"==typeof e.scope&&N.openNode(S.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(v(L,S.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),L=""):e.beginScope._multi&&(b(e.beginScope,t),L="")),T=Object.create(e,{parent:{value:T}})}let x={};function w(t,o){let a=o&&o[0];if(L+=t,null==a)return m(),0;if("begin"===x.type&&"end"===o.type&&x.index===o.index&&""===a){if(L+=n.slice(o.index,o.index+1),!r){let t=Error(`0 width match regex (${e})`);throw t.languageName=e,t.badRule=x.rule,t}return 1}if(x=o,"begin"===o.type){let e=o[0],t=o.rule,n=new s(t);for(let i of[t.__beforeBegin,t["on:begin"]])if(i&&(i(o,n),n.isMatchIgnored))return 0===T.matcher.regexIndex?(L+=e[0],1):(_=!0,0);return t.skip?L+=e:(t.excludeBegin&&(L+=e),m(),t.returnBegin||t.excludeBegin||(L=e)),y(t,o),t.returnBegin?0:e.length}if("illegal"!==o.type||i){if("end"===o.type){let e=function(e){let t=e[0],i=n.substring(e.index),r=function e(t,n,i){let r=function(e,t){let n=e&&e.exec(t);return n&&0===n.index}(t.endRe,i);if(r){if(t["on:end"]){let e=new s(t);t["on:end"](n,e),e.isMatchIgnored&&(r=!1)}if(r){for(;t.endsParent&&t.parent;)t=t.parent;return t}}if(t.endsWithParent)return e(t.parent,n,i)}(T,e,i);if(!r)return Y;let o=T;T.endScope&&T.endScope._wrap?(m(),v(t,T.endScope._wrap)):T.endScope&&T.endScope._multi?(m(),b(T.endScope,e)):o.skip?L+=t:(o.returnEnd||o.excludeEnd||(L+=t),m(),o.excludeEnd&&(L=t));do T.scope&&N.closeNode(),T.skip||T.subLanguage||(z+=T.relevance),T=T.parent;while(T!==r.parent);return r.starts&&y(r.starts,e),o.returnEnd?0:t.length}(o);if(e!==Y)return e}}else{let e=Error('Illegal lexeme "'+a+'" for mode "'+(T.scope||"<unnamed>")+'"');throw e.mode=T,e}if("illegal"===o.type&&""===a)return 1;if(j>1e5&&j>3*o.index)throw Error("potential infinite loop, way more iterations than matches");return L+=a,a.length}let S=C(e);if(!S)throw J(o.replace("{}",e)),Error('Unknown language: "'+e+'"');let E=function(e){function t(t,n){return RegExp(g(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,t){t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),this.matchAt+=k(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);let e=this.regexes.map(e=>e[1]);this.matcherRe=t(A(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;let t=this.matcherRe.exec(e);if(!t)return null;let n=t.findIndex((e,t)=>t>0&&void 0!==e),i=this.matchIndexes[n];return t.splice(0,n),Object.assign(t,i)}}class i{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];let t=new n;return this.rules.slice(e).forEach(([e,n])=>t.addRule(e,n)),t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){let t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex;let n=t.exec(e);if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{let t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,n=t.exec(e)}return n&&(this.regexIndex+=n.position+1,this.regexIndex===this.count&&this.considerAll()),n}}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=c(e.classNameAliases||{}),function n(r,o){if(r.isCompiled)return r;[D,B,Z,H].forEach(e=>e(r,o)),e.compilerExtensions.forEach(e=>e(r,o)),r.__beforeBegin=null,[$,P,F].forEach(e=>e(r,o)),r.isCompiled=!0;let a=null;return"object"==typeof r.keywords&&r.keywords.$pattern&&(r.keywords=Object.assign({},r.keywords),a=r.keywords.$pattern,delete r.keywords.$pattern),a=a||/\w+/,r.keywords&&(r.keywords=function e(t,n,i="keyword"){let r=Object.create(null);return"string"==typeof t?o(i,t.split(" ")):Array.isArray(t)?o(i,t):Object.keys(t).forEach(function(i){Object.assign(r,e(t[i],n,i))}),r;function o(e,t){n&&(t=t.map(e=>e.toLowerCase())),t.forEach(function(t){var n,i,o;let a=t.split("|");r[a[0]]=[e,(n=a[0],(i=a[1])?Number(i):+(o=n,!K.includes(o.toLowerCase())))]})}}(r.keywords,e.case_insensitive)),r.keywordPatternRe=t(a,!0),o&&(r.begin||(r.begin=/\B|\b/),r.beginRe=t(r.begin),r.end||r.endsWithParent||(r.end=/\B|\b/),r.end&&(r.endRe=t(r.end)),r.terminatorEnd=g(r.end)||"",r.endsWithParent&&o.terminatorEnd&&(r.terminatorEnd+=(r.end?"|":"")+o.terminatorEnd)),r.illegal&&(r.illegalRe=t(r.illegal)),r.contains||(r.contains=[]),r.contains=[].concat(...r.contains.map(function(e){var t;return((t="self"===e?r:e).variants&&!t.cachedVariants&&(t.cachedVariants=t.variants.map(function(e){return c(t,{variants:null},e)})),t.cachedVariants)?t.cachedVariants:!function e(t){return!!t&&(t.endsWithParent||e(t.starts))}(t)?Object.isFrozen(t)?c(t):t:c(t,{starts:t.starts?c(t.starts):null})})),r.contains.forEach(function(e){n(e,r)}),r.starts&&n(r.starts,o),r.matcher=function(e){let t=new i;return e.contains.forEach(e=>t.addRule(e.begin,{rule:e,type:"begin"})),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t}(r),r}(e)}(S),M="",T=a||E,O={},N=new p.__emitter(p),R=[];for(let e=T;e!==S;e=e.parent)e.scope&&R.unshift(e.scope);R.forEach(e=>N.openNode(e));let L="",z=0,I=0,j=0,_=!1;try{if(S.__emitTokens)S.__emitTokens(n,N);else{for(T.matcher.considerAll();;){j++,_?_=!1:T.matcher.considerAll(),T.matcher.lastIndex=I;let e=T.matcher.exec(n);if(!e)break;let t=n.substring(I,e.index),i=w(t,e);I=e.index+i}w(n.substring(I))}return N.finalize(),M=N.toHTML(),{language:e,value:M,relevance:z,illegal:!1,_emitter:N,_top:T}}catch(t){if(t.message&&t.message.includes("Illegal"))return{language:e,value:l(n),illegal:!0,relevance:0,_illegalBy:{message:t.message,index:I,context:n.slice(I-100,I+100),mode:t.mode,resultSoFar:M},_emitter:N};if(r)return{language:e,value:l(n),illegal:!1,relevance:0,errorRaised:t,_emitter:N,_top:T};throw t}}function f(e,n){n=n||p.languages||Object.keys(t);let i=function(e){let t={value:l(e),illegal:!1,relevance:0,_top:a,_emitter:new p.__emitter(p)};return t._emitter.addText(e),t}(e),r=n.filter(C).filter(O).map(t=>u(t,e,!1));r.unshift(i);let[o,s]=r.sort((e,t)=>{if(e.relevance!==t.relevance)return t.relevance-e.relevance;if(e.language&&t.language){if(C(e.language).supersetOf===t.language)return 1;else if(C(t.language).supersetOf===e.language)return -1}return 0});return o.secondBest=s,o}function S(e){let t=null,i=function(e){let t=e.className+" ";t+=e.parentNode?e.parentNode.className:"";let n=p.languageDetectRe.exec(t);if(n){let t=C(n[1]);return t||(q(o.replace("{}",n[1])),q("Falling back to no-highlight mode for this block.",e)),t?n[1]:"no-highlight"}return t.split(/\s+/).find(e=>d(e)||C(e))}(e);if(d(i))return;if(N("before:highlightElement",{el:e,language:i}),e.dataset.highlighted)return void console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",e);if(e.children.length>0&&(p.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(e)),p.throwUnescapedHTML))throw new X("One of your code blocks includes unescaped HTML.",e.innerHTML);let r=e.textContent,a=i?h(r,{language:i,ignoreIllegals:!0}):f(r);e.innerHTML=a.value,e.dataset.highlighted="yes";var s=a.language;let l=i&&n[i]||s;e.classList.add("hljs"),e.classList.add(`language-${l}`),e.result={language:a.language,re:a.relevance,relevance:a.relevance},a.secondBest&&(e.secondBest={language:a.secondBest.language,relevance:a.secondBest.relevance}),N("after:highlightElement",{el:e,result:a,text:r})}let E=!1;function M(){if("loading"===document.readyState){E=!0;return}document.querySelectorAll(p.cssSelector).forEach(S)}function C(e){return t[e=(e||"").toLowerCase()]||t[n[e]]}function T(e,{languageName:t}){"string"==typeof e&&(e=[e]),e.forEach(e=>{n[e.toLowerCase()]=t})}function O(e){let t=C(e);return t&&!t.disableAutodetect}function N(e,t){i.forEach(function(n){n[e]&&n[e](t)})}for(let o in"undefined"!=typeof window&&window.addEventListener&&window.addEventListener("DOMContentLoaded",function(){E&&M()},!1),Object.assign(e,{highlight:h,highlightAuto:f,highlightAll:M,highlightElement:S,highlightBlock:function(e){return W("10.7.0","highlightBlock will be removed entirely in v12.0"),W("10.7.0","Please use highlightElement now."),S(e)},configure:function(e){p=c(p,e)},initHighlighting:()=>{M(),W("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){M(),W("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(n,i){let o=null;try{o=i(e)}catch(e){if(J("Language definition for '{}' could not be registered.".replace("{}",n)),r)J(e);else throw e;o=a}o.name||(o.name=n),t[n]=o,o.rawDefinition=i.bind(null,e),o.aliases&&T(o.aliases,{languageName:n})},unregisterLanguage:function(e){for(let i of(delete t[e],Object.keys(n)))n[i]===e&&delete n[i]},listLanguages:function(){return Object.keys(t)},getLanguage:C,registerAliases:T,autoDetection:O,inherit:c,addPlugin:function(e){var t;(t=e)["before:highlightBlock"]&&!t["before:highlightElement"]&&(t["before:highlightElement"]=e=>{t["before:highlightBlock"](Object.assign({block:e.el},e))}),t["after:highlightBlock"]&&!t["after:highlightElement"]&&(t["after:highlightElement"]=e=>{t["after:highlightBlock"](Object.assign({block:e.el},e))}),i.push(e)},removePlugin:function(e){let t=i.indexOf(e);-1!==t&&i.splice(t,1)}}),e.debugMode=function(){r=!1},e.safeMode=function(){r=!0},e.versionString="11.10.0",e.regex={concat:x,lookahead:v,either:w,optional:y,anyNumberOfTimes:b},j)"object"==typeof j[o]&&function e(t){return t instanceof Map?t.clear=t.delete=t.set=function(){throw Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=function(){throw Error("set is read-only")}),Object.freeze(t),Object.getOwnPropertyNames(t).forEach(n=>{let i=t[n],r=typeof i;"object"!==r&&"function"!==r||Object.isFrozen(i)||e(i)}),t}(j[o]);return Object.assign(e,j),e},ee=Q({});ee.newInstance=()=>Q({}),ee.HighlightJS=ee,ee.default=ee;var et=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(ee);function en(e){return e.value||e.children||[]}function ei({doc:e,name:t,lowlight:n,defaultLanguage:i}){let o=[];return(0,r.xe)(e,e=>e.type.name===t).forEach(e=>{var t;let r=e.pos+1,s=e.node.attrs.language||i,l=n.listLanguages();(function e(t,n=[]){return t.map(t=>{let i=[...n,...t.properties?t.properties.className:[]];return t.children?e(t.children,i):{text:t.value,classes:i}}).flat()})(s&&(l.includes(s)||et.getLanguage(s)||(null==(t=n.registered)?void 0:t.call(n,s)))?en(n.highlight(s,e.node.textContent)):en(n.highlightAuto(e.node.textContent))).forEach(e=>{let t=r+e.text.length;if(e.classes.length){let n=a.NZ.inline(r,t,{class:e.classes.join(" ")});o.push(n)}r=t})}),a.zF.create(e,o)}let er=i.Ay.extend({addOptions(){var e;return{...null==(e=this.parent)?void 0:e.call(this),lowlight:{},languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},addProseMirrorPlugins(){var e;return[...(null==(e=this.parent)?void 0:e.call(this))||[],function({name:e,lowlight:t,defaultLanguage:n}){if(!["highlight","highlightAuto","listLanguages"].every(e=>"function"==typeof t[e]))throw Error("You should provide an instance of lowlight to use the code-block-lowlight extension");let i=new o.k_({key:new o.hs("lowlight"),state:{init:(i,{doc:r})=>ei({doc:r,name:e,lowlight:t,defaultLanguage:n}),apply:(i,o,a,s)=>{let l=a.selection.$head.parent.type.name,c=s.selection.$head.parent.type.name,p=(0,r.xe)(a.doc,t=>t.type.name===e),d=(0,r.xe)(s.doc,t=>t.type.name===e);return i.docChanged&&([l,c].includes(e)||d.length!==p.length||i.steps.some(e=>void 0!==e.from&&void 0!==e.to&&p.some(t=>t.pos>=e.from&&t.pos+t.node.nodeSize<=e.to)))?ei({doc:i.doc,name:e,lowlight:t,defaultLanguage:n}):o.map(i.mapping,i.doc)}},props:{decorations:e=>i.getState(e)}});return i}({name:this.name,lowlight:this.options.lowlight,defaultLanguage:this.options.defaultLanguage})]}})},5339:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5383:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>s,NG:()=>s});var i=n(4701),r=n(2571);let o=/^```([a-z]+)?[\s\n]$/,a=/^~~~([a-z]+)?[\s\n]$/,s=i.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;let{languageClassPrefix:n}=this.options,i=[...(null==(t=e.firstElementChild)?void 0:t.classList)||[]].filter(e=>e.startsWith(n)).map(e=>e.replace(n,""))[0];return i||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",(0,i.KV)(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!!e&&t.parent.type.name===this.name&&(!!n||!t.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:t}=e,{selection:n}=t,{$from:i,empty:r}=n;if(!r||i.parent.type!==this.type)return!1;let o=i.parentOffset===i.parent.nodeSize-2,a=i.parent.textContent.endsWith("\n\n");return!!o&&!!a&&e.chain().command(({tr:e})=>(e.delete(i.pos-2,i.pos),!0)).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;let{state:t}=e,{selection:n,doc:i}=t,{$from:o,empty:a}=n;if(!a||o.parent.type!==this.type||o.parentOffset!==o.parent.nodeSize-2)return!1;let s=o.after();return void 0!==s&&(i.nodeAt(s)?e.commands.command(({tr:e})=>(e.setSelection(r.LN.near(i.resolve(s))),!0)):e.commands.exitCode())}}},addInputRules(){return[(0,i.JJ)({find:o,type:this.type,getAttributes:e=>({language:e[1]})}),(0,i.JJ)({find:a,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new r.k_({key:new r.hs("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData||this.editor.isActive(this.type.name))return!1;let n=t.clipboardData.getData("text/plain"),i=t.clipboardData.getData("vscode-editor-data"),o=i?JSON.parse(i):void 0,a=null==o?void 0:o.mode;if(!n||!a)return!1;let{tr:s,schema:l}=e.state,c=l.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:a},c)),s.selection.$from.parent.type!==this.type&&s.setSelection(r.U3.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),e.dispatch(s),!0}}})]}})},5625:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});let i="[A-Za-z$_][0-9A-Za-z$_]*",r=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],o=["true","false","null","undefined","NaN","Infinity"],a=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],s=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],l=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],c=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],p=[].concat(l,a,s);function d(e){let t=e.regex,n=function(e){var t;let n=e.regex,d=(e,{after:t})=>{let n="</"+e[0].slice(1);return -1!==e.input.indexOf(n,t)},h=/<[A-Za-z0-9\\._:-]+/,u=/\/[A-Za-z0-9\\._:-]+>|\/>/,f={$pattern:i,keyword:r,literal:o,built_in:p,"variable.language":c},m="[0-9](_?[0-9])*",g=`\\.(${m})`,v="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",b={className:"number",variants:[{begin:`(\\b(${v})((${g})|\\.)?|(${g}))[eE][+-]?(${m})\\b`},{begin:`\\b(${v})\\b((${g})\\b|\\.)?|(${g})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},y={className:"subst",begin:"\\$\\{",end:"\\}",keywords:f,contains:[]},x={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,y],subLanguage:"xml"}},w={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,y],subLanguage:"css"}},k={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,y],subLanguage:"graphql"}},S={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,y]},A={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:i+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},E=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,x,w,k,S,{match:/\$\d+/},b];y.contains=E.concat({begin:/\{/,end:/\}/,keywords:f,contains:["self"].concat(E)});let M=[].concat(A,y.contains),C=M.concat([{begin:/(\s*)\(/,end:/\)/,keywords:f,contains:["self"].concat(M)}]),T={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:f,contains:C},O={variants:[{match:[/class/,/\s+/,i,/\s+/,/extends/,/\s+/,n.concat(i,"(",n.concat(/\./,i),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,i],scope:{1:"keyword",3:"title.class"}}]},N={relevance:0,match:n.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...a,...s]}},R={match:n.concat(/\b/,(t=[...l,"super","import"].map(e=>`${e}\\s*\\(`),n.concat("(?!",t.join("|"),")")),i,n.lookahead(/\s*\(/)),className:"title.function",relevance:0},L={begin:n.concat(/\./,n.lookahead(n.concat(i,/(?![0-9A-Za-z$_(])/))),end:i,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},z="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",I={match:[/const|var|let/,/\s+/,i,/\s*/,/=\s*/,/(async\s*)?/,n.lookahead(z)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[T]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:f,exports:{PARAMS_CONTAINS:C,CLASS_REFERENCE:N},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,x,w,k,S,A,{match:/\$\d+/},b,N,{scope:"attr",match:i+n.lookahead(":"),relevance:0},I,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[A,e.REGEXP_MODE,{className:"function",begin:z,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:f,contains:C}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:"<>",end:"</>"},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:h,"on:begin":(e,t)=>{let n,i=e[0].length+e.index,r=e.input[i];if("<"===r||","===r)return void t.ignoreMatch();">"!==r||d(e,{after:i})||t.ignoreMatch();let o=e.input.substring(i);if((n=o.match(/^\s*=/))||(n=o.match(/^\s+extends\s+/))&&0===n.index)return void t.ignoreMatch()},end:u}],subLanguage:"xml",contains:[{begin:h,end:u,skip:!0,contains:["self"]}]}]},{variants:[{match:[/function/,/\s+/,i,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[T],illegal:/%/},{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[T,e.inherit(e.TITLE_MODE,{begin:i,className:"title.function"})]},{match:/\.\.\./,relevance:0},L,{match:"\\$"+i,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[T]},R,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},O,{match:[/get|set/,/\s+/,i,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},T]},{match:/\$[(.]/}]}}(e),d=["any","void","number","boolean","string","object","never","symbol","bigint","unknown"],h={begin:[/namespace/,/\s+/,e.IDENT_RE],beginScope:{1:"keyword",3:"title.class"}},u={beginKeywords:"interface",end:/\{/,excludeEnd:!0,keywords:{keyword:"interface extends",built_in:d},contains:[n.exports.CLASS_REFERENCE]},f={$pattern:i,keyword:r.concat(["type","interface","public","private","protected","implements","declare","abstract","readonly","enum","override","satisfies"]),literal:o,built_in:p.concat(d),"variable.language":c},m={className:"meta",begin:"@"+i},g=(e,t,n)=>{let i=e.contains.findIndex(e=>e.label===t);if(-1===i)throw Error("can not find mode to replace");e.contains.splice(i,1,n)};Object.assign(n.keywords,f),n.exports.PARAMS_CONTAINS.push(m);let v=n.contains.find(e=>"attr"===e.scope),b=Object.assign({},v,{match:t.concat(i,t.lookahead(/\s*\?:/))});return n.exports.PARAMS_CONTAINS.push([n.exports.CLASS_REFERENCE,v,b]),n.contains=n.contains.concat([m,h,u,b]),g(n,"shebang",e.SHEBANG()),g(n,"use_strict",{className:"meta",relevance:10,begin:/^\s*['"]use strict['"]/}),n.contains.find(e=>"func.def"===e.label).relevance=0,Object.assign(n,{name:"TypeScript",aliases:["ts","tsx","mts","cts"]}),n}},5695:(e,t,n)=>{"use strict";var i=n(8999);n.o(i,"useParams")&&n.d(t,{useParams:function(){return i.useParams}}),n.o(i,"useRouter")&&n.d(t,{useRouter:function(){return i.useRouter}}),n.o(i,"useSearchParams")&&n.d(t,{useSearchParams:function(){return i.useSearchParams}})},5968:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6333:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>o});var i=n(4701);let r=/^\s*(\[([( |x])?\])\s$/,o=i.bP.create({name:"taskItem",addOptions:()=>({nested:!1,HTMLAttributes:{},taskListTypeName:"taskList"}),content(){return this.options.nested?"paragraph block*":"paragraph+"},defining:!0,addAttributes:()=>({checked:{default:!1,keepOnSplit:!1,parseHTML:e=>{let t=e.getAttribute("data-checked");return""===t||"true"===t},renderHTML:e=>({"data-checked":e.checked})}}),parseHTML(){return[{tag:`li[data-type="${this.name}"]`,priority:51}]},renderHTML({node:e,HTMLAttributes:t}){return["li",(0,i.KV)(this.options.HTMLAttributes,t,{"data-type":this.name}),["label",["input",{type:"checkbox",checked:e.attrs.checked?"checked":null}],["span"]],["div",0]]},addKeyboardShortcuts(){let e={Enter:()=>this.editor.commands.splitListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)};return this.options.nested?{...e,Tab:()=>this.editor.commands.sinkListItem(this.name)}:e},addNodeView(){return({node:e,HTMLAttributes:t,getPos:n,editor:i})=>{let r=document.createElement("li"),o=document.createElement("label"),a=document.createElement("span"),s=document.createElement("input"),l=document.createElement("div");return o.contentEditable="false",s.type="checkbox",s.addEventListener("mousedown",e=>e.preventDefault()),s.addEventListener("change",t=>{if(!i.isEditable&&!this.options.onReadOnlyChecked){s.checked=!s.checked;return}let{checked:r}=t.target;i.isEditable&&"function"==typeof n&&i.chain().focus(void 0,{scrollIntoView:!1}).command(({tr:e})=>{let t=n();if("number"!=typeof t)return!1;let i=e.doc.nodeAt(t);return e.setNodeMarkup(t,void 0,{...null==i?void 0:i.attrs,checked:r}),!0}).run(),i.isEditable||!this.options.onReadOnlyChecked||this.options.onReadOnlyChecked(e,r)||(s.checked=!s.checked)}),Object.entries(this.options.HTMLAttributes).forEach(([e,t])=>{r.setAttribute(e,t)}),r.dataset.checked=e.attrs.checked,s.checked=e.attrs.checked,o.append(s,a),r.append(o,l),Object.entries(t).forEach(([e,t])=>{r.setAttribute(e,t)}),{dom:r,contentDOM:l,update:e=>e.type===this.type&&(r.dataset.checked=e.attrs.checked,s.checked=e.attrs.checked,!0)}}},addInputRules(){return[(0,i.tG)({find:r,type:this.type,getAttributes:e=>({checked:"x"===e[e.length-1]})})]}})},6340:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("align-justify",[["path",{d:"M3 12h18",key:"1i2n21"}],["path",{d:"M3 18h18",key:"1h113x"}],["path",{d:"M3 6h18",key:"d0wm0j"}]])},6377:(e,t,n)=>{"use strict";var i=n(4701);let r=e=>{if(!e.children.length)return;let t=e.querySelectorAll("span");t&&t.forEach(e=>{var t,n;let i=e.getAttribute("style"),r=null==(n=null==(t=e.parentElement)?void 0:t.closest("span"))?void 0:n.getAttribute("style");e.setAttribute("style",`${r};${i}`)})};i.CU.create({name:"textStyle",priority:101,addOptions:()=>({HTMLAttributes:{},mergeNestedSpanStyles:!1}),parseHTML(){return[{tag:"span",getAttrs:e=>!!e.hasAttribute("style")&&(this.options.mergeNestedSpanStyles&&r(e),{})}]},renderHTML({HTMLAttributes:e}){return["span",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{removeEmptyTextStyle:()=>({tr:e})=>{let{selection:t}=e;return e.doc.nodesBetween(t.from,t.to,(t,n)=>{if(t.isTextblock)return!0;t.marks.filter(e=>e.type===this.type).some(e=>Object.values(e.attrs).some(e=>!!e))||e.removeMark(n,n+t.nodeSize,this.type)}),!0}}}})},6710:(e,t,n)=>{"use strict";n.d(t,{VB:()=>Q});var i=n(2115),r=n(8637);function o(e,t,n,i){return new(n||(n=Promise))(function(r,o){function a(e){try{l(i.next(e))}catch(e){o(e)}}function s(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,s)}l((i=i.apply(e,t||[])).next())})}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;let a=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function s(e,t,n){let i=function(e){let{name:t}=e;if(t&&-1!==t.lastIndexOf(".")&&!e.type){let n=t.split(".").pop().toLowerCase(),i=a.get(n);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:r}=e,o="string"==typeof t?t:"string"==typeof r&&r.length>0?r:`./${e.name}`;return"string"!=typeof i.path&&l(i,"path",o),void 0!==n&&Object.defineProperty(i,"handle",{value:n,writable:!1,configurable:!1,enumerable:!0}),l(i,"relativePath",o),i}function l(e,t,n){Object.defineProperty(e,t,{value:n,writable:!1,configurable:!1,enumerable:!0})}let c=[".DS_Store","Thumbs.db"];function p(e){return"object"==typeof e&&null!==e}function d(e){return e.filter(e=>-1===c.indexOf(e.name))}function h(e){if(null===e)return[];let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i)}return t}function u(e){if("function"!=typeof e.webkitGetAsEntry)return f(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?g(t):f(e,t)}function f(e,t){return o(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,s(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return s(i,null!=(n=null==t?void 0:t.fullPath)?n:void 0)})}function m(e){return o(this,void 0,void 0,function*(){return e.isDirectory?g(e):function(e){return o(this,void 0,void 0,function*(){return new Promise((t,n)=>{e.file(n=>{t(s(n,e.fullPath))},e=>{n(e)})})})}(e)})}function g(e){let t=e.createReader();return new Promise((e,n)=>{let i=[];!function r(){t.readEntries(t=>o(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(m));i.push(e),r()}else try{let t=yield Promise.all(i);e(t)}catch(e){n(e)}}),e=>{n(e)})}()})}var v=n(2462);function b(e){return function(e){if(Array.isArray(e))return A(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||S(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach(function(t){w(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function k(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,i,r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);a=!0);}catch(e){s=!0,i=e}finally{try{a||null==r.return||r.return()}finally{if(s)throw i}}return o}}(e,t)||S(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){if(e){if("string"==typeof e)return A(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return A(e,t)}}function A(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}var E="function"==typeof v?v:v.default,M=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),n=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(n)}},C=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},T=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},O={code:"too-many-files",message:"Too many files"};function N(e,t){var n="application/x-moz-file"===e.type||E(e,t);return[n,n?null:M(t)]}function R(e,t,n){if(L(e.size)){if(L(t)&&L(n)){if(e.size>n)return[!1,C(n)];if(e.size<t)return[!1,T(t)]}else if(L(t)&&e.size<t)return[!1,T(t)];else if(L(n)&&e.size>n)return[!1,C(n)]}return[!0,null]}function L(e){return null!=e}function z(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function I(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function j(e){e.preventDefault()}function _(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){for(var n=arguments.length,i=Array(n>1?n-1:0),r=1;r<n;r++)i[r-1]=arguments[r];return t.some(function(t){return!z(e)&&t&&t.apply(void 0,[e].concat(i)),z(e)})}}function D(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function $(e){return/^.*\.[\w]+$/.test(e)}var P=["children"],B=["open"],F=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],H=["refKey","onChange","onClick"];function K(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,i,r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var o=[],a=!0,s=!1;try{for(r=r.call(e);!(a=(n=r.next()).done)&&(o.push(n.value),!t||o.length!==t);a=!0);}catch(e){s=!0,i=e}finally{try{a||null==r.return||r.return()}finally{if(s)throw i}}return o}}(e,t)||U(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function U(e,t){if(e){if("string"==typeof e)return J(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return J(e,t)}}function J(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function W(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach(function(t){V(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function G(e,t){if(null==e)return{};var n,i,r=function(e,t){if(null==e)return{};var n,i,r={},o=Object.keys(e);for(i=0;i<o.length;i++)n=o[i],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)n=o[i],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}var Z=(0,i.forwardRef)(function(e,t){var n=e.children,r=Q(G(e,P)),o=r.open,a=G(r,B);return(0,i.useImperativeHandle)(t,function(){return{open:o}},[o]),i.createElement(i.Fragment,null,n(W(W({},a),{},{open:o})))});Z.displayName="Dropzone";var X={disabled:!1,getFilesFromEvent:function(e){return o(this,void 0,void 0,function*(){var t;if(p(e)&&p(e.dataTransfer))return function(e,t){return o(this,void 0,void 0,function*(){if(e.items){let n=h(e.items).filter(e=>"file"===e.kind);return"drop"!==t?n:d(function e(t){return t.reduce((t,n)=>[...t,...Array.isArray(n)?e(n):[n]],[])}((yield Promise.all(n.map(u)))))}return d(h(e.files).map(e=>s(e)))})}(e.dataTransfer,e.type);if(p(t=e)&&p(t.target))return h(e.target.files).map(e=>s(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return o(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>s(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};Z.defaultProps=X,Z.propTypes={children:r.func,accept:r.objectOf(r.arrayOf(r.string)),multiple:r.bool,preventDropOnDocument:r.bool,noClick:r.bool,noKeyboard:r.bool,noDrag:r.bool,noDragEventsBubbling:r.bool,minSize:r.number,maxSize:r.number,maxFiles:r.number,disabled:r.bool,getFilesFromEvent:r.func,onFileDialogCancel:r.func,onFileDialogOpen:r.func,useFsAccessApi:r.bool,autoFocus:r.bool,onDragEnter:r.func,onDragLeave:r.func,onDragOver:r.func,onDrop:r.func,onDropAccepted:r.func,onDropRejected:r.func,onError:r.func,validator:r.func};var Y={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Q(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=W(W({},X),e),n=t.accept,r=t.disabled,o=t.getFilesFromEvent,a=t.maxSize,s=t.minSize,l=t.multiple,c=t.maxFiles,p=t.onDragEnter,d=t.onDragLeave,h=t.onDragOver,u=t.onDrop,f=t.onDropAccepted,m=t.onDropRejected,g=t.onFileDialogCancel,v=t.onFileDialogOpen,y=t.useFsAccessApi,S=t.autoFocus,A=t.preventDropOnDocument,E=t.noClick,M=t.noKeyboard,C=t.noDrag,T=t.noDragEventsBubbling,P=t.onError,B=t.validator,q=(0,i.useMemo)(function(){return L(n)?Object.entries(n).reduce(function(e,t){var n=k(t,2),i=n[0],r=n[1];return[].concat(b(e),[i],b(r))},[]).filter(function(e){return D(e)||$(e)}).join(","):void 0},[n]),Z=(0,i.useMemo)(function(){return L(n)?[{description:"Files",accept:Object.entries(n).filter(function(e){var t=k(e,2),n=t[0],i=t[1],r=!0;return D(n)||(console.warn('Skipped "'.concat(n,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),r=!1),Array.isArray(i)&&i.every($)||(console.warn('Skipped "'.concat(n,'" because an invalid file extension was provided.')),r=!1),r}).reduce(function(e,t){var n=k(t,2),i=n[0],r=n[1];return x(x({},e),{},w({},i,r))},{})}]:n},[n]),Q=(0,i.useMemo)(function(){return"function"==typeof v?v:et},[v]),en=(0,i.useMemo)(function(){return"function"==typeof g?g:et},[g]),ei=(0,i.useRef)(null),er=(0,i.useRef)(null),eo=K((0,i.useReducer)(ee,Y),2),ea=eo[0],es=eo[1],el=ea.isFocused,ec=ea.isFileDialogActive,ep=(0,i.useRef)("undefined"!=typeof window&&window.isSecureContext&&y&&"showOpenFilePicker"in window),ed=function(){!ep.current&&ec&&setTimeout(function(){er.current&&(er.current.files.length||(es({type:"closeDialog"}),en()))},300)};(0,i.useEffect)(function(){return window.addEventListener("focus",ed,!1),function(){window.removeEventListener("focus",ed,!1)}},[er,ec,en,ep]);var eh=(0,i.useRef)([]),eu=function(e){ei.current&&ei.current.contains(e.target)||(e.preventDefault(),eh.current=[])};(0,i.useEffect)(function(){return A&&(document.addEventListener("dragover",j,!1),document.addEventListener("drop",eu,!1)),function(){A&&(document.removeEventListener("dragover",j),document.removeEventListener("drop",eu))}},[ei,A]),(0,i.useEffect)(function(){return!r&&S&&ei.current&&ei.current.focus(),function(){}},[ei,S,r]);var ef=(0,i.useCallback)(function(e){P?P(e):console.error(e)},[P]),em=(0,i.useCallback)(function(e){var t;e.preventDefault(),e.persist(),eT(e),eh.current=[].concat(function(e){if(Array.isArray(e))return J(e)}(t=eh.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||U(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),I(e)&&Promise.resolve(o(e)).then(function(t){if(!z(e)||T){var n,i,r,o,d,h,u,f,m=t.length,g=m>0&&(i=(n={files:t,accept:q,minSize:s,maxSize:a,multiple:l,maxFiles:c,validator:B}).files,r=n.accept,o=n.minSize,d=n.maxSize,h=n.multiple,u=n.maxFiles,f=n.validator,(!!h||!(i.length>1))&&(!h||!(u>=1)||!(i.length>u))&&i.every(function(e){var t=k(N(e,r),1)[0],n=k(R(e,o,d),1)[0],i=f?f(e):null;return t&&n&&!i}));es({isDragAccept:g,isDragReject:m>0&&!g,isDragActive:!0,type:"setDraggedFiles"}),p&&p(e)}}).catch(function(e){return ef(e)})},[o,p,ef,T,q,s,a,l,c,B]),eg=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eT(e);var t=I(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&h&&h(e),!1},[h,T]),ev=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eT(e);var t=eh.current.filter(function(e){return ei.current&&ei.current.contains(e)}),n=t.indexOf(e.target);-1!==n&&t.splice(n,1),eh.current=t,!(t.length>0)&&(es({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),I(e)&&d&&d(e))},[ei,d,T]),eb=(0,i.useCallback)(function(e,t){var n=[],i=[];e.forEach(function(e){var t=K(N(e,q),2),r=t[0],o=t[1],l=K(R(e,s,a),2),c=l[0],p=l[1],d=B?B(e):null;if(r&&c&&!d)n.push(e);else{var h=[o,p];d&&(h=h.concat(d)),i.push({file:e,errors:h.filter(function(e){return e})})}}),(!l&&n.length>1||l&&c>=1&&n.length>c)&&(n.forEach(function(e){i.push({file:e,errors:[O]})}),n.splice(0)),es({acceptedFiles:n,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),u&&u(n,i,t),i.length>0&&m&&m(i,t),n.length>0&&f&&f(n,t)},[es,l,q,s,a,c,u,f,m,B]),ey=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eT(e),eh.current=[],I(e)&&Promise.resolve(o(e)).then(function(t){(!z(e)||T)&&eb(t,e)}).catch(function(e){return ef(e)}),es({type:"reset"})},[o,eb,ef,T]),ex=(0,i.useCallback)(function(){if(ep.current){es({type:"openDialog"}),Q(),window.showOpenFilePicker({multiple:l,types:Z}).then(function(e){return o(e)}).then(function(e){eb(e,null),es({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(en(e),es({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(ep.current=!1,er.current?(er.current.value=null,er.current.click()):ef(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ef(e)});return}er.current&&(es({type:"openDialog"}),Q(),er.current.value=null,er.current.click())},[es,Q,en,y,eb,ef,Z,l]),ew=(0,i.useCallback)(function(e){ei.current&&ei.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),ex())},[ei,ex]),ek=(0,i.useCallback)(function(){es({type:"focus"})},[]),eS=(0,i.useCallback)(function(){es({type:"blur"})},[]),eA=(0,i.useCallback)(function(){E||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(ex,0):ex())},[E,ex]),eE=function(e){return r?null:e},eM=function(e){return M?null:eE(e)},eC=function(e){return C?null:eE(e)},eT=function(e){T&&e.stopPropagation()},eO=(0,i.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=e.role,i=e.onKeyDown,o=e.onFocus,a=e.onBlur,s=e.onClick,l=e.onDragEnter,c=e.onDragOver,p=e.onDragLeave,d=e.onDrop,h=G(e,F);return W(W(V({onKeyDown:eM(_(i,ew)),onFocus:eM(_(o,ek)),onBlur:eM(_(a,eS)),onClick:eE(_(s,eA)),onDragEnter:eC(_(l,em)),onDragOver:eC(_(c,eg)),onDragLeave:eC(_(p,ev)),onDrop:eC(_(d,ey)),role:"string"==typeof n&&""!==n?n:"presentation"},void 0===t?"ref":t,ei),r||M?{}:{tabIndex:0}),h)}},[ei,ew,ek,eS,eA,em,eg,ev,ey,M,C,r]),eN=(0,i.useCallback)(function(e){e.stopPropagation()},[]),eR=(0,i.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,n=e.onChange,i=e.onClick,r=G(e,H);return W(W({},V({accept:q,multiple:l,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:eE(_(n,ey)),onClick:eE(_(i,eN)),tabIndex:-1},void 0===t?"ref":t,er)),r)}},[er,n,l,ey,r]);return W(W({},ea),{},{isFocused:el&&!r,getRootProps:eO,getInputProps:eR,rootRef:ei,inputRef:er,open:eE(ex)})}function ee(e,t){switch(t.type){case"focus":return W(W({},e),{},{isFocused:!0});case"blur":return W(W({},e),{},{isFocused:!1});case"openDialog":return W(W({},Y),{},{isFileDialogActive:!0});case"closeDialog":return W(W({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return W(W({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return W(W({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return W({},Y);default:return e}}function et(){}},6761:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>eK});var i=n(4701);let r=(e,t)=>{for(let n in t)e[n]=t[n];return e},o="numeric",a="ascii",s="alpha",l="asciinumeric",c="alphanumeric",p="domain",d="emoji",h="whitespace";function u(e,t,n){for(let i in t[o]&&(t[l]=!0,t[c]=!0),t[a]&&(t[l]=!0,t[s]=!0),t[l]&&(t[c]=!0),t[s]&&(t[c]=!0),t[c]&&(t[p]=!0),t[d]&&(t[p]=!0),t){let t=(i in n||(n[i]=[]),n[i]);0>t.indexOf(e)&&t.push(e)}}function f(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}f.groups={},f.prototype={accepts(){return!!this.t},go(e){let t=this.j[e];if(t)return t;for(let t=0;t<this.jr.length;t++){let n=this.jr[t][0],i=this.jr[t][1];if(i&&n.test(e))return i}return this.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,i){for(let r=0;r<e.length;r++)this.tt(e[r],t,n,i)},tr(e,t,n,i){let r;return i=i||f.groups,t&&t.j?r=t:(r=new f(t),n&&i&&u(t,n,i)),this.jr.push([e,r]),r},ts(e,t,n,i){let r=this,o=e.length;if(!o)return r;for(let t=0;t<o-1;t++)r=r.tt(e[t]);return r.tt(e[o-1],t,n,i)},tt(e,t,n,i){if(i=i||f.groups,t&&t.j)return this.j[e]=t,t;let o,a=this.go(e);return a?(r((o=new f).j,a.j),o.jr.push.apply(o.jr,a.jr),o.jd=a.jd,o.t=a.t):o=new f,t&&(i&&(o.t&&"string"==typeof o.t?u(t,r(function(e,t){let n={};for(let i in t)t[i].indexOf(e)>=0&&(n[i]=!0);return n}(o.t,i),n),i):n&&u(t,n,i)),o.t=t),this.j[e]=o,o}};let m=(e,t,n,i,r)=>e.ta(t,n,i,r),g=(e,t,n,i,r)=>e.tr(t,n,i,r),v=(e,t,n,i,r)=>e.ts(t,n,i,r),b=(e,t,n,i,r)=>e.tt(t,n,i,r),y="WORD",x="UWORD",w="ASCIINUMERICAL",k="ALPHANUMERICAL",S="LOCALHOST",A="UTLD",E="SCHEME",M="SLASH_SCHEME",C="OPENBRACE",T="CLOSEBRACE",O="OPENBRACKET",N="CLOSEBRACKET",R="OPENPAREN",L="CLOSEPAREN",z="OPENANGLEBRACKET",I="CLOSEANGLEBRACKET",j="FULLWIDTHLEFTPAREN",_="FULLWIDTHRIGHTPAREN",D="LEFTCORNERBRACKET",$="RIGHTCORNERBRACKET",P="LEFTWHITECORNERBRACKET",B="RIGHTWHITECORNERBRACKET",F="FULLWIDTHLESSTHAN",H="FULLWIDTHGREATERTHAN",K="AMPERSAND",U="APOSTROPHE",J="ASTERISK",q="BACKSLASH",W="BACKTICK",V="CARET",G="COLON",Z="COMMA",X="DOLLAR",Y="EQUALS",Q="EXCLAMATION",ee="HYPHEN",et="PERCENT",en="PIPE",ei="PLUS",er="POUND",eo="QUERY",ea="QUOTE",es="FULLWIDTHMIDDLEDOT",el="SEMI",ec="SLASH",ep="TILDE",ed="UNDERSCORE",eh="EMOJI";var eu=Object.freeze({__proto__:null,ALPHANUMERICAL:k,AMPERSAND:K,APOSTROPHE:U,ASCIINUMERICAL:w,ASTERISK:J,AT:"AT",BACKSLASH:q,BACKTICK:W,CARET:V,CLOSEANGLEBRACKET:I,CLOSEBRACE:T,CLOSEBRACKET:N,CLOSEPAREN:L,COLON:G,COMMA:Z,DOLLAR:X,DOT:"DOT",EMOJI:eh,EQUALS:Y,EXCLAMATION:Q,FULLWIDTHGREATERTHAN:H,FULLWIDTHLEFTPAREN:j,FULLWIDTHLESSTHAN:F,FULLWIDTHMIDDLEDOT:es,FULLWIDTHRIGHTPAREN:_,HYPHEN:ee,LEFTCORNERBRACKET:D,LEFTWHITECORNERBRACKET:P,LOCALHOST:S,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:z,OPENBRACE:C,OPENBRACKET:O,OPENPAREN:R,PERCENT:et,PIPE:en,PLUS:ei,POUND:er,QUERY:eo,QUOTE:ea,RIGHTCORNERBRACKET:$,RIGHTWHITECORNERBRACKET:B,SCHEME:E,SEMI:el,SLASH:ec,SLASH_SCHEME:M,SYM:"SYM",TILDE:ep,TLD:"TLD",UNDERSCORE:ed,UTLD:A,UWORD:x,WORD:y,WS:"WS"});let ef=/[a-z]/,em=/\p{L}/u,eg=/\p{Emoji}/u,ev=/\d/,eb=/\s/,ey=null,ex=null;function ew(e,t){let n=function(e){let t=[],n=e.length,i=0;for(;i<n;){let r,o=e.charCodeAt(i),a=o<55296||o>56319||i+1===n||(r=e.charCodeAt(i+1))<56320||r>57343?e[i]:e.slice(i,i+2);t.push(a),i+=a.length}return t}(t.replace(/[A-Z]/g,e=>e.toLowerCase())),i=n.length,r=[],o=0,a=0;for(;a<i;){let s=e,l=null,c=0,p=null,d=-1,h=-1;for(;a<i&&(l=s.go(n[a]));)(s=l).accepts()?(d=0,h=0,p=s):d>=0&&(d+=n[a].length,h++),c+=n[a].length,o+=n[a].length,a++;o-=d,a-=h,c-=d,r.push({t:p.t,v:t.slice(o-c,o),s:o-c,e:o})}return r}function ek(e,t,n,i,r){let o,a=t.length;for(let n=0;n<a-1;n++){let a=t[n];e.j[a]?o=e.j[a]:((o=new f(i)).jr=r.slice(),e.j[a]=o),e=o}return(o=new f(n)).jr=r.slice(),e.j[t[a-1]]=o,o}function eS(e){let t=[],n=[],i=0;for(;i<e.length;){let r=0;for(;"0123456789".indexOf(e[i+r])>=0;)r++;if(r>0){t.push(n.join(""));for(let t=parseInt(e.substring(i,i+r),10);t>0;t--)n.pop();i+=r}else n.push(e[i]),i++}return t}let eA={defaultProtocol:"http",events:null,format:eM,formatHref:eM,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function eE(e,t=null){let n=r({},eA);e&&(n=r(n,e instanceof eE?e.o:e));let i=n.ignoreTags,o=[];for(let e=0;e<i.length;e++)o.push(i[e].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=o}function eM(e){return e}function eC(e,t){this.t="token",this.v=e,this.tk=t}function eT(e,t){class n extends eC{constructor(t,n){super(t,n),this.t=e}}for(let e in t)n.prototype[e]=t[e];return n.t=e,n}eE.prototype={o:eA,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){let i=null!=t,r=this.o[e];return r&&("object"==typeof r?"function"==typeof(r=n.t in r?r[n.t]:eA[e])&&i&&(r=r(t,n)):"function"==typeof r&&i&&(r=r(t,n.t,n))),r},getObj(e,t,n){let i=this.o[e];return"function"==typeof i&&null!=t&&(i=i(t,n.t,n)),i},render(e){let t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}},eC.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){let t=this.toString(),n=e.get("truncate",t,this),i=e.get("format",t,this);return n&&i.length>n?i.substring(0,n)+"…":i},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=eA.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){let t=this.toHref(e.get("defaultProtocol")),n=e.get("formatHref",t,this),i=e.get("tagName",t,this),o=this.toFormattedString(e),a={},s=e.get("className",t,this),l=e.get("target",t,this),c=e.get("rel",t,this),p=e.getObj("attributes",t,this),d=e.getObj("events",t,this);return a.href=n,s&&(a.class=s),l&&(a.target=l),c&&(a.rel=c),p&&r(a,p),{tagName:i,attributes:a,content:o,eventListeners:d}}};let eO=eT("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),eN=eT("text"),eR=eT("nl"),eL=eT("url",{isLink:!0,toHref(e=eA.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){let e=this.tk;return e.length>=2&&e[0].t!==S&&e[1].t===G}}),ez=e=>new f(e);function eI(e,t,n){let i=n[0].s,r=n[n.length-1].e;return new e(t.slice(i,r),n)}let ej="undefined"!=typeof console&&console&&console.warn||(()=>{}),e_={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function eD(e,t=!1){if(e_.initialized&&ej(`linkifyjs: already initialized - will not register custom scheme "${e}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);e_.customSchemes.push([e,t])}function e$(e){return e_.initialized||function(){e_.scanner=function(e=[]){let t={};f.groups=t;let n=new f;null==ey&&(ey=eS("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==ex&&(ex=eS("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),b(n,"'",U),b(n,"{",C),b(n,"}",T),b(n,"[",O),b(n,"]",N),b(n,"(",R),b(n,")",L),b(n,"<",z),b(n,">",I),b(n,"（",j),b(n,"）",_),b(n,"「",D),b(n,"」",$),b(n,"『",P),b(n,"』",B),b(n,"＜",F),b(n,"＞",H),b(n,"&",K),b(n,"*",J),b(n,"@","AT"),b(n,"`",W),b(n,"^",V),b(n,":",G),b(n,",",Z),b(n,"$",X),b(n,".","DOT"),b(n,"=",Y),b(n,"!",Q),b(n,"-",ee),b(n,"%",et),b(n,"|",en),b(n,"+",ei),b(n,"#",er),b(n,"?",eo),b(n,'"',ea),b(n,"/",ec),b(n,";",el),b(n,"~",ep),b(n,"_",ed),b(n,"\\",q),b(n,"・",es);let i=g(n,ev,"NUM",{[o]:!0});g(i,ev,i);let m=g(i,ef,w,{[l]:!0}),ew=g(i,em,k,{[c]:!0}),eA=g(n,ef,y,{[a]:!0});g(eA,ev,m),g(eA,ef,eA),g(m,ev,m),g(m,ef,m);let eE=g(n,em,x,{[s]:!0});g(eE,ef),g(eE,ev,ew),g(eE,em,eE),g(ew,ev,ew),g(ew,ef),g(ew,em,ew);let eM=b(n,"\n","NL",{[h]:!0}),eC=b(n,"\r","WS",{[h]:!0}),eT=g(n,eb,"WS",{[h]:!0});b(n,"￼",eT),b(eC,"\n",eM),b(eC,"￼",eT),g(eC,eb,eT),b(eT,"\r"),b(eT,"\n"),g(eT,eb,eT),b(eT,"￼",eT);let eO=g(n,eg,eh,{[d]:!0});b(eO,"#"),g(eO,eg,eO),b(eO,"️",eO);let eN=b(eO,"‍");b(eN,"#"),g(eN,eg,eO);let eR=[[ef,eA],[ev,m]],eL=[[ef,null],[em,eE],[ev,ew]];for(let e=0;e<ey.length;e++)ek(n,ey[e],"TLD",y,eR);for(let e=0;e<ex.length;e++)ek(n,ex[e],A,x,eL);u("TLD",{tld:!0,ascii:!0},t),u(A,{utld:!0,alpha:!0},t),ek(n,"file",E,y,eR),ek(n,"mailto",E,y,eR),ek(n,"http",M,y,eR),ek(n,"https",M,y,eR),ek(n,"ftp",M,y,eR),ek(n,"ftps",M,y,eR),u(E,{scheme:!0,ascii:!0},t),u(M,{slashscheme:!0,ascii:!0},t),e=e.sort((e,t)=>e[0]>t[0]?1:-1);for(let t=0;t<e.length;t++){let i=e[t][0],r=e[t][1]?{scheme:!0}:{slashscheme:!0};i.indexOf("-")>=0?r[p]=!0:ef.test(i)?ev.test(i)?r[l]=!0:r[a]=!0:r[o]=!0,v(n,i,i,r)}return v(n,"localhost",S,{ascii:!0}),n.jd=new f("SYM"),{start:n,tokens:r({groups:t},eu)}}(e_.customSchemes);for(let e=0;e<e_.tokenQueue.length;e++)e_.tokenQueue[e][1]({scanner:e_.scanner});e_.parser=function({groups:e}){let t=e.domain.concat([K,J,"AT",q,W,V,X,Y,ee,"NUM",et,en,ei,er,ec,"SYM",ep,ed]),n=[U,G,Z,"DOT",Q,et,eo,ea,el,z,I,C,T,N,O,R,L,j,_,D,$,P,B,F,H],i=[K,U,J,q,W,V,X,Y,ee,C,T,et,en,ei,er,eo,ec,"SYM",ep,ed],r=ez(),o=b(r,ep);m(o,i,o),m(o,e.domain,o);let a=ez(),s=ez(),l=ez();m(r,e.domain,a),m(r,e.scheme,s),m(r,e.slashscheme,l),m(a,i,o),m(a,e.domain,a);let c=b(a,"AT");b(o,"AT",c),b(s,"AT",c),b(l,"AT",c);let p=b(o,"DOT");m(p,i,o),m(p,e.domain,o);let d=ez();m(c,e.domain,d),m(d,e.domain,d);let h=b(d,"DOT");m(h,e.domain,d);let u=ez(eO);m(h,e.tld,u),m(h,e.utld,u),b(c,S,u);let f=b(d,ee);b(f,ee,f),m(f,e.domain,d),m(u,e.domain,d),b(u,"DOT",h),b(u,ee,f),m(b(u,G),e.numeric,eO);let g=b(a,ee),v=b(a,"DOT");b(g,ee,g),m(g,e.domain,a),m(v,i,o),m(v,e.domain,a);let y=ez(eL);m(v,e.tld,y),m(v,e.utld,y),m(y,e.domain,a),m(y,i,o),b(y,"DOT",v),b(y,ee,g),b(y,"AT",c);let x=b(y,G),w=ez(eL);m(x,e.numeric,w);let k=ez(eL),A=ez();m(k,t,k),m(k,n,A),m(A,t,k),m(A,n,A),b(y,ec,k),b(w,ec,k);let E=b(s,G),M=b(l,G),es=b(M,ec),eh=b(es,ec);m(s,e.domain,a),b(s,"DOT",v),b(s,ee,g),m(l,e.domain,a),b(l,"DOT",v),b(l,ee,g),m(E,e.domain,k),b(E,ec,k),b(E,eo,k),m(eh,e.domain,k),m(eh,t,k),b(eh,ec,k);let ef=[[C,T],[O,N],[R,L],[z,I],[j,_],[D,$],[P,B],[F,H]];for(let e=0;e<ef.length;e++){let[i,r]=ef[e],o=b(k,i);b(A,i,o),b(o,r,k);let a=ez(eL);m(o,t,a);let s=ez();m(o,n),m(a,t,a),m(a,n,s),m(s,t,a),m(s,n,s),b(a,r,k),b(s,r,k)}return b(r,S,y),b(r,"NL",eR),{start:r,tokens:eu}}(e_.scanner.tokens);for(let e=0;e<e_.pluginQueue.length;e++)e_.pluginQueue[e][1]({scanner:e_.scanner,parser:e_.parser});e_.initialized=!0}(),function(e,t,n){let i=n.length,r=0,o=[],a=[];for(;r<i;){let s=e,l=null,c=null,p=0,d=null,h=-1;for(;r<i&&!(l=s.go(n[r].t));)a.push(n[r++]);for(;r<i&&(c=l||s.go(n[r].t));)l=null,(s=c).accepts()?(h=0,d=s):h>=0&&h++,r++,p++;if(h<0)(r-=p)<i&&(a.push(n[r]),r++);else{a.length>0&&(o.push(eI(eN,t,a)),a=[]),r-=h,p-=h;let e=d.t,i=n.slice(r-p,r);o.push(eI(e,t,i))}}return a.length>0&&o.push(eI(eN,t,a)),o}(e_.parser.start,e,ew(e_.scanner.start,e))}function eP(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}let i=new eE(n),r=e$(e),o=[];for(let e=0;e<r.length;e++){let n=r[e];n.isLink&&(!t||n.t===t)&&i.check(n)&&o.push(n.toFormattedObject(i))}return o}e$.scan=ew;var eB=n(2571);let eF=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function eH(e,t){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach(e=>{let t="string"==typeof e?e:e.scheme;t&&n.push(t)}),!e||e.replace(eF,"").match(RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}let eK=i.CU.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if("string"==typeof e)return void eD(e);eD(e.scheme,e.optionalSlashes)})},onDestroy(){f.groups={},e_.scanner=null,e_.parser=null,e_.tokenQueue=[],e_.pluginQueue=[],e_.customSchemes=[],e_.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!eH(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}),addAttributes(){return{href:{default:null,parseHTML:e=>e.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{let t=e.getAttribute("href");return!!t&&!!this.options.isAllowedUri(t,{defaultValidate:e=>!!eH(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:e=>!!eH(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",(0,i.KV)(this.options.HTMLAttributes,e),0]:["a",(0,i.KV)(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!eH(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().setMark(this.name,e).setMeta("preventAutolink",!0).run()},toggleLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!eH(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[(0,i.Zc)({find:e=>{let t=[];if(e){let{protocols:n,defaultProtocol:i}=this.options,r=eP(e).filter(e=>e.isLink&&this.options.isAllowedUri(e.value,{defaultValidate:e=>!!eH(e,n),protocols:n,defaultProtocol:i}));r.length&&r.forEach(e=>t.push({text:e.value,data:{href:e.href},index:e.start}))}return t},type:this.type,getAttributes:e=>{var t;return{href:null==(t=e.data)?void 0:t.href}}})]},addProseMirrorPlugins(){var e,t,n;let r=[],{protocols:o,defaultProtocol:a}=this.options;return this.options.autolink&&r.push((e={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:e=>this.options.isAllowedUri(e,{defaultValidate:e=>!!eH(e,o),protocols:o,defaultProtocol:a}),shouldAutoLink:this.options.shouldAutoLink},new eB.k_({key:new eB.hs("autolink"),appendTransaction:(t,n,r)=>{let o=t.some(e=>e.docChanged)&&!n.doc.eq(r.doc),a=t.some(e=>e.getMeta("preventAutolink"));if(!o||a)return;let{tr:s}=r,l=(0,i.T7)(n.doc,[...t]);if((0,i.FF)(l).forEach(({newRange:t})=>{let n,o,a=(0,i.Nx)(r.doc,t,e=>e.isTextblock);if(a.length>1?(n=a[0],o=r.doc.textBetween(n.pos,n.pos+n.node.nodeSize,void 0," ")):a.length&&r.doc.textBetween(t.from,t.to," "," ").endsWith(" ")&&(n=a[0],o=r.doc.textBetween(n.pos,t.to,void 0," ")),n&&o){let t=o.split(" ").filter(e=>""!==e);if(t.length<=0)return!1;let a=t[t.length-1],l=n.pos+o.lastIndexOf(a);if(!a)return!1;let c=e$(a).map(t=>t.toObject(e.defaultProtocol));if(!(1===c.length?c[0].isLink:3===c.length&&!!c[1].isLink&&["()","[]"].includes(c[0].value+c[2].value)))return!1;c.filter(e=>e.isLink).map(e=>({...e,from:l+e.start+1,to:l+e.end+1})).filter(e=>!r.schema.marks.code||!r.doc.rangeHasMark(e.from,e.to,r.schema.marks.code)).filter(t=>e.validate(t.value)).filter(t=>e.shouldAutoLink(t.value)).forEach(t=>{(0,i.hO)(t.from,t.to,r.doc).some(t=>t.mark.type===e.type)||s.addMark(t.from,t.to,e.type.create({href:t.href}))})}}),s.steps.length)return s}}))),!0===this.options.openOnClick&&r.push((t={type:this.type},new eB.k_({key:new eB.hs("handleClickLink"),props:{handleClick:(e,n,r)=>{var o,a;if(0!==r.button||!e.editable)return!1;let s=r.target,l=[];for(;"DIV"!==s.nodeName;)l.push(s),s=s.parentNode;if(!l.find(e=>"A"===e.nodeName))return!1;let c=(0,i.gu)(e.state,t.type.name),p=r.target,d=null!=(o=null==p?void 0:p.href)?o:c.href,h=null!=(a=null==p?void 0:p.target)?a:c.target;return!!p&&!!d&&(window.open(d,h),!0)}}}))),this.options.linkOnPaste&&r.push((n={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new eB.k_({key:new eB.hs("handlePasteLink"),props:{handlePaste:(e,t,i)=>{let{state:r}=e,{selection:o}=r,{empty:a}=o;if(a)return!1;let s="";i.content.forEach(e=>{s+=e.textContent});let l=eP(s,{defaultProtocol:n.defaultProtocol}).find(e=>e.isLink&&e.value===s);return!!s&&!!l&&n.editor.commands.setMark(n.type,{href:l.href})}}}))),r}})},6770:(e,t,n)=>{"use strict";n.d(t,{K:()=>f,w:()=>u});for(var i={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},r={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},o="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),a="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),s=0;s<10;s++)i[48+s]=i[96+s]=String(s);for(var s=1;s<=24;s++)i[s+111]="F"+s;for(var s=65;s<=90;s++)i[s]=String.fromCharCode(s+32),r[s]=String.fromCharCode(s);for(var l in i)r.hasOwnProperty(l)||(r[l]=i[l]);var c=n(2571);let p="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),d="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function h(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function u(e){return new c.k_({props:{handleKeyDown:f(e)}})}function f(e){let t=function(e){let t=Object.create(null);for(let n in e)t[function(e){let t,n,i,r,o=e.split(/-(?!$)/),a=o[o.length-1];"Space"==a&&(a=" ");for(let e=0;e<o.length-1;e++){let a=o[e];if(/^(cmd|meta|m)$/i.test(a))r=!0;else if(/^a(lt)?$/i.test(a))t=!0;else if(/^(c|ctrl|control)$/i.test(a))n=!0;else if(/^s(hift)?$/i.test(a))i=!0;else if(/^mod$/i.test(a))p?r=!0:n=!0;else throw Error("Unrecognized modifier name: "+a)}return t&&(a="Alt-"+a),n&&(a="Ctrl-"+a),r&&(a="Meta-"+a),i&&(a="Shift-"+a),a}(n)]=e[n];return t}(e);return function(e,n){var s;let l=("Esc"==(s=!(o&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||a&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?r:i)[n.keyCode]||n.key||"Unidentified")&&(s="Escape"),"Del"==s&&(s="Delete"),"Left"==s&&(s="ArrowLeft"),"Up"==s&&(s="ArrowUp"),"Right"==s&&(s="ArrowRight"),"Down"==s&&(s="ArrowDown"),s),c,p=t[h(l,n)];if(p&&p(e.state,e.dispatch,e))return!0;if(1==l.length&&" "!=l){if(n.shiftKey){let i=t[h(l,n,!1)];if(i&&i(e.state,e.dispatch,e))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(d&&n.ctrlKey&&n.altKey)&&(c=i[n.keyCode])&&c!=l){let i=t[h(c,n)];if(i&&i(e.state,e.dispatch,e))return!0}}return!1}}},7e3:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var i=n(4701);let r=i.CU.create({name:"underline",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("underline")&&{}}],renderHTML({HTMLAttributes:e}){return["u",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setUnderline:()=>({commands:e})=>e.setMark(this.name),toggleUnderline:()=>({commands:e})=>e.toggleMark(this.name),unsetUnderline:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}})},7192:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});var i=n(4701);let r=i.CU.create({name:"superscript",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"sup"},{style:"vertical-align",getAttrs:e=>"super"===e&&null}],renderHTML({HTMLAttributes:e}){return["sup",(0,i.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setSuperscript:()=>({commands:e})=>e.setMark(this.name),toggleSuperscript:()=>({commands:e})=>e.toggleMark(this.name),unsetSuperscript:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-.":()=>this.editor.commands.toggleSuperscript()}}})},7213:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7240:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("underline",[["path",{d:"M6 4v6a6 6 0 0 0 12 0V4",key:"9kb039"}],["line",{x1:"4",x2:"20",y1:"20",y2:"20",key:"nun2al"}]])},7325:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("align-left",[["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M17 18H3",key:"1amg6g"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},7360:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});var i=n(4701),r=n(2571),o=n(2695);let a=i.YY.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new r.k_({key:new r.hs("placeholder"),props:{decorations:({doc:e,selection:t})=>{let n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:r}=t,a=[];if(!n)return null;let s=this.editor.isEmpty;return e.descendants((e,t)=>{let n=r>=t&&r<=t+e.nodeSize,l=!e.isLeaf&&(0,i.Op)(e);if((n||!this.options.showOnlyCurrent)&&l){let i=[this.options.emptyNodeClass];s&&i.push(this.options.emptyEditorClass);let r=o.NZ.node(t,t+e.nodeSize,{class:i.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:e,pos:t,hasAnchor:n}):this.options.placeholder});a.push(r)}return this.options.includeChildren}),o.zF.create(e,a)}}})]}})},7434:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7733:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("align-center",[["path",{d:"M17 12H7",key:"16if0g"}],["path",{d:"M19 18H5",key:"18s9l3"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},7855:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("unlink",[["path",{d:"m18.84 12.25 1.72-1.71h-.02a5.004 5.004 0 0 0-.12-7.07 5.006 5.006 0 0 0-6.95 0l-1.72 1.71",key:"yqzxt4"}],["path",{d:"m5.17 11.75-1.71 1.71a5.004 5.004 0 0 0 .12 7.07 5.006 5.006 0 0 0 6.95 0l1.71-1.71",key:"4qinb0"}],["line",{x1:"8",x2:"8",y1:"2",y2:"5",key:"1041cp"}],["line",{x1:"2",x2:"5",y1:"8",y2:"8",key:"14m1p5"}],["line",{x1:"16",x2:"16",y1:"19",y2:"22",key:"rzdirn"}],["line",{x1:"19",x2:"22",y1:"16",y2:"16",key:"ox905f"}]])},8033:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});let i="[A-Za-z$_][0-9A-Za-z$_]*",r=["as","in","of","if","for","while","finally","var","new","function","do","return","void","else","break","catch","instanceof","with","throw","case","default","try","switch","continue","typeof","delete","let","yield","const","class","debugger","async","await","static","import","from","export","extends","using"],o=["true","false","null","undefined","NaN","Infinity"],a=["Object","Function","Boolean","Symbol","Math","Date","Number","BigInt","String","RegExp","Array","Float32Array","Float64Array","Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Int32Array","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array","Set","Map","WeakSet","WeakMap","ArrayBuffer","SharedArrayBuffer","Atomics","DataView","JSON","Promise","Generator","GeneratorFunction","AsyncFunction","Reflect","Proxy","Intl","WebAssembly"],s=["Error","EvalError","InternalError","RangeError","ReferenceError","SyntaxError","TypeError","URIError"],l=["setInterval","setTimeout","clearInterval","clearTimeout","require","exports","eval","isFinite","isNaN","parseFloat","parseInt","decodeURI","decodeURIComponent","encodeURI","encodeURIComponent","escape","unescape"],c=["arguments","this","super","console","window","document","localStorage","sessionStorage","module","global"],p=[].concat(l,a,s);function d(e){var t;let n=e.regex,d=(e,{after:t})=>{let n="</"+e[0].slice(1);return -1!==e.input.indexOf(n,t)},h=/<[A-Za-z0-9\\._:-]+/,u=/\/[A-Za-z0-9\\._:-]+>|\/>/,f={$pattern:i,keyword:r,literal:o,built_in:p,"variable.language":c},m="[0-9](_?[0-9])*",g=`\\.(${m})`,v="0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*",b={className:"number",variants:[{begin:`(\\b(${v})((${g})|\\.)?|(${g}))[eE][+-]?(${m})\\b`},{begin:`\\b(${v})\\b((${g})\\b|\\.)?|(${g})\\b`},{begin:"\\b(0|[1-9](_?[0-9])*)n\\b"},{begin:"\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\b"},{begin:"\\b0[bB][0-1](_?[0-1])*n?\\b"},{begin:"\\b0[oO][0-7](_?[0-7])*n?\\b"},{begin:"\\b0[0-7]+n?\\b"}],relevance:0},y={className:"subst",begin:"\\$\\{",end:"\\}",keywords:f,contains:[]},x={begin:".?html`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,y],subLanguage:"xml"}},w={begin:".?css`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,y],subLanguage:"css"}},k={begin:".?gql`",end:"",starts:{end:"`",returnEnd:!1,contains:[e.BACKSLASH_ESCAPE,y],subLanguage:"graphql"}},S={className:"string",begin:"`",end:"`",contains:[e.BACKSLASH_ESCAPE,y]},A={className:"comment",variants:[e.COMMENT(/\/\*\*(?!\/)/,"\\*/",{relevance:0,contains:[{begin:"(?=@[A-Za-z]+)",relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+"},{className:"type",begin:"\\{",end:"\\}",excludeEnd:!0,excludeBegin:!0,relevance:0},{className:"variable",begin:i+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),e.C_BLOCK_COMMENT_MODE,e.C_LINE_COMMENT_MODE]},E=[e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,x,w,k,S,{match:/\$\d+/},b];y.contains=E.concat({begin:/\{/,end:/\}/,keywords:f,contains:["self"].concat(E)});let M=[].concat(A,y.contains),C=M.concat([{begin:/(\s*)\(/,end:/\)/,keywords:f,contains:["self"].concat(M)}]),T={className:"params",begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:f,contains:C},O={variants:[{match:[/class/,/\s+/,i,/\s+/,/extends/,/\s+/,n.concat(i,"(",n.concat(/\./,i),")*")],scope:{1:"keyword",3:"title.class",5:"keyword",7:"title.class.inherited"}},{match:[/class/,/\s+/,i],scope:{1:"keyword",3:"title.class"}}]},N={relevance:0,match:n.either(/\bJSON/,/\b[A-Z][a-z]+([A-Z][a-z]*|\d)*/,/\b[A-Z]{2,}([A-Z][a-z]+|\d)+([A-Z][a-z]*)*/,/\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\d)*([A-Z][a-z]*)*/),className:"title.class",keywords:{_:[...a,...s]}},R={match:n.concat(/\b/,(t=[...l,"super","import"].map(e=>`${e}\\s*\\(`),n.concat("(?!",t.join("|"),")")),i,n.lookahead(/\s*\(/)),className:"title.function",relevance:0},L={begin:n.concat(/\./,n.lookahead(n.concat(i,/(?![0-9A-Za-z$_(])/))),end:i,excludeBegin:!0,keywords:"prototype",className:"property",relevance:0},z="(\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)|"+e.UNDERSCORE_IDENT_RE+")\\s*=>",I={match:[/const|var|let/,/\s+/,i,/\s*/,/=\s*/,/(async\s*)?/,n.lookahead(z)],keywords:"async",className:{1:"keyword",3:"title.function"},contains:[T]};return{name:"JavaScript",aliases:["js","jsx","mjs","cjs"],keywords:f,exports:{PARAMS_CONTAINS:C,CLASS_REFERENCE:N},illegal:/#(?![$_A-z])/,contains:[e.SHEBANG({label:"shebang",binary:"node",relevance:5}),{label:"use_strict",className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},e.APOS_STRING_MODE,e.QUOTE_STRING_MODE,x,w,k,S,A,{match:/\$\d+/},b,N,{scope:"attr",match:i+n.lookahead(":"),relevance:0},I,{begin:"("+e.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",relevance:0,contains:[A,e.REGEXP_MODE,{className:"function",begin:z,returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:e.UNDERSCORE_IDENT_RE,relevance:0},{className:null,begin:/\(\s*\)/,skip:!0},{begin:/(\s*)\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:f,contains:C}]}]},{begin:/,/,relevance:0},{match:/\s+/,relevance:0},{variants:[{begin:"<>",end:"</>"},{match:/<[A-Za-z0-9\\._:-]+\s*\/>/},{begin:h,"on:begin":(e,t)=>{let n,i=e[0].length+e.index,r=e.input[i];if("<"===r||","===r)return void t.ignoreMatch();">"!==r||d(e,{after:i})||t.ignoreMatch();let o=e.input.substring(i);if((n=o.match(/^\s*=/))||(n=o.match(/^\s+extends\s+/))&&0===n.index)return void t.ignoreMatch()},end:u}],subLanguage:"xml",contains:[{begin:h,end:u,skip:!0,contains:["self"]}]}]},{variants:[{match:[/function/,/\s+/,i,/(?=\s*\()/]},{match:[/function/,/\s*(?=\()/]}],className:{1:"keyword",3:"title.function"},label:"func.def",contains:[T],illegal:/%/},{beginKeywords:"while if switch catch for"},{begin:"\\b(?!function)"+e.UNDERSCORE_IDENT_RE+"\\([^()]*(\\([^()]*(\\([^()]*\\)[^()]*)*\\)[^()]*)*\\)\\s*\\{",returnBegin:!0,label:"func.def",contains:[T,e.inherit(e.TITLE_MODE,{begin:i,className:"title.function"})]},{match:/\.\.\./,relevance:0},L,{match:"\\$"+i,relevance:0},{match:[/\bconstructor(?=\s*\()/],className:{1:"title.function"},contains:[T]},R,{relevance:0,match:/\b[A-Z][A-Z_0-9]+\b/,className:"variable.constant"},O,{match:[/get|set/,/\s+/,i,/(?=\()/],className:{1:"keyword",3:"title.function"},contains:[{begin:/\(\)/},T]},{match:/\$[(.]/}]}}},8164:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},8440:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("heading-1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]])},8637:(e,t,n)=>{e.exports=n(9399)()},8673:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("subscript",[["path",{d:"m4 5 8 8",key:"1eunvl"}],["path",{d:"m12 5-8 8",key:"1ah0jp"}],["path",{d:"M20 19h-4c0-1.5.44-2 1.5-2.5S20 15.33 20 14c0-.47-.17-.93-.48-1.29a2.11 2.11 0 0 0-2.62-.44c-.42.24-.74.62-.9 1.07",key:"e8ta8j"}]])},8702:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("align-right",[["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M21 18H7",key:"1ygte8"}],["path",{d:"M21 6H3",key:"1jwq7v"}]])},8772:e=>{class t{constructor(e){void 0===e.data&&(e.data={}),this.data=e.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function n(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function i(e,...t){let n=Object.create(null);for(let t in e)n[t]=e[t];return t.forEach(function(e){for(let t in e)n[t]=e[t]}),n}let r=e=>!!e.scope,o=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){let n=e.split(".");return[`${t}${n.shift()}`,...n.map((e,t)=>`${e}${"_".repeat(t+1)}`)].join(" ")}return`${t}${e}`};class a{constructor(e,t){this.buffer="",this.classPrefix=t.classPrefix,e.walk(this)}addText(e){this.buffer+=n(e)}openNode(e){if(!r(e))return;let t=o(e.scope,{prefix:this.classPrefix});this.span(t)}closeNode(e){r(e)&&(this.buffer+="</span>")}value(){return this.buffer}span(e){this.buffer+=`<span class="${e}">`}}let s=(e={})=>{let t={children:[]};return Object.assign(t,e),t};class l{constructor(){this.rootNode=s(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(e){this.top.children.push(e)}openNode(e){let t=s({scope:e});this.add(t),this.stack.push(t)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(e){return this.constructor._walk(e,this.rootNode)}static _walk(e,t){return"string"==typeof t?e.addText(t):t.children&&(e.openNode(t),t.children.forEach(t=>this._walk(e,t)),e.closeNode(t)),e}static _collapse(e){"string"!=typeof e&&e.children&&(e.children.every(e=>"string"==typeof e)?e.children=[e.children.join("")]:e.children.forEach(e=>{l._collapse(e)}))}}class c extends l{constructor(e){super(),this.options=e}addText(e){""!==e&&this.add(e)}startScope(e){this.openNode(e)}endScope(){this.closeNode()}__addSublanguage(e,t){let n=e.root;t&&(n.scope=`language:${t}`),this.add(n)}toHTML(){return new a(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function p(e){return e?"string"==typeof e?e:e.source:null}function d(e){return f("(?=",e,")")}function h(e){return f("(?:",e,")*")}function u(e){return f("(?:",e,")?")}function f(...e){return e.map(e=>p(e)).join("")}function m(...e){return"("+(function(e){let t=e[e.length-1];return"object"==typeof t&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}(e).capture?"":"?:")+e.map(e=>p(e)).join("|")+")"}function g(e){return RegExp(e.toString()+"|").exec("").length-1}let v=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function b(e,{joinWith:t}){let n=0;return e.map(e=>{let t=n+=1,i=p(e),r="";for(;i.length>0;){let e=v.exec(i);if(!e){r+=i;break}r+=i.substring(0,e.index),i=i.substring(e.index+e[0].length),"\\"===e[0][0]&&e[1]?r+="\\"+String(Number(e[1])+t):(r+=e[0],"("===e[0]&&n++)}return r}).map(e=>`(${e})`).join(t)}let y="[a-zA-Z]\\w*",x="[a-zA-Z_]\\w*",w="\\b\\d+(\\.\\d+)?",k="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",S="\\b(0b[01]+)",A={begin:"\\\\[\\s\\S]",relevance:0},E=function(e,t,n={}){let r=i({scope:"comment",begin:e,end:t,contains:[]},n);r.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});let o=m("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return r.contains.push({begin:f(/[ ]+/,"(",o,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),r},M=E("//","$"),C=E("/\\*","\\*/"),T=E("#","$");var O=Object.freeze({__proto__:null,APOS_STRING_MODE:{scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[A]},BACKSLASH_ESCAPE:A,BINARY_NUMBER_MODE:{scope:"number",begin:S,relevance:0},BINARY_NUMBER_RE:S,COMMENT:E,C_BLOCK_COMMENT_MODE:C,C_LINE_COMMENT_MODE:M,C_NUMBER_MODE:{scope:"number",begin:k,relevance:0},C_NUMBER_RE:k,END_SAME_AS_BEGIN:function(e){return Object.assign(e,{"on:begin":(e,t)=>{t.data._beginMatch=e[1]},"on:end":(e,t)=>{t.data._beginMatch!==e[1]&&t.ignoreMatch()}})},HASH_COMMENT_MODE:T,IDENT_RE:y,MATCH_NOTHING_RE:/\b\B/,METHOD_GUARD:{begin:"\\.\\s*"+x,relevance:0},NUMBER_MODE:{scope:"number",begin:w,relevance:0},NUMBER_RE:w,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},QUOTE_STRING_MODE:{scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[A]},REGEXP_MODE:{scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[A,{begin:/\[/,end:/\]/,relevance:0,contains:[A]}]},RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(e={})=>{let t=/^#![ ]*\//;return e.binary&&(e.begin=f(t,/.*\b/,e.binary,/\b.*/)),i({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(e,t)=>{0!==e.index&&t.ignoreMatch()}},e)},TITLE_MODE:{scope:"title",begin:y,relevance:0},UNDERSCORE_IDENT_RE:x,UNDERSCORE_TITLE_MODE:{scope:"title",begin:x,relevance:0}});function N(e,t){"."===e.input[e.index-1]&&t.ignoreMatch()}function R(e,t){void 0!==e.className&&(e.scope=e.className,delete e.className)}function L(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=N,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,void 0===e.relevance&&(e.relevance=0))}function z(e,t){Array.isArray(e.illegal)&&(e.illegal=m(...e.illegal))}function I(e,t){if(e.match){if(e.begin||e.end)throw Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function j(e,t){void 0===e.relevance&&(e.relevance=1)}let _=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw Error("beforeMatch cannot be used with starts");let n=Object.assign({},e);Object.keys(e).forEach(t=>{delete e[t]}),e.keywords=n.keywords,e.begin=f(n.beforeMatch,d(n.begin)),e.starts={relevance:0,contains:[Object.assign(n,{endsParent:!0})]},e.relevance=0,delete n.beforeMatch},D=["of","and","for","in","not","or","if","then","parent","list","value"],$={},P=e=>{console.error(e)},B=(e,...t)=>{console.log(`WARN: ${e}`,...t)},F=(e,t)=>{$[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),$[`${e}/${t}`]=!0)},H=Error();function K(e,t,{key:n}){let i=0,r=e[n],o={},a={};for(let e=1;e<=t.length;e++)a[e+i]=r[e],o[e+i]=!0,i+=g(t[e-1]);e[n]=a,e[n]._emit=o,e[n]._multi=!0}function U(e){if(e.scope&&"object"==typeof e.scope&&null!==e.scope&&(e.beginScope=e.scope,delete e.scope),"string"==typeof e.beginScope&&(e.beginScope={_wrap:e.beginScope}),"string"==typeof e.endScope&&(e.endScope={_wrap:e.endScope}),Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw P("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),H;if("object"!=typeof e.beginScope||null===e.beginScope)throw P("beginScope must be object"),H;K(e,e.begin,{key:"beginScope"}),e.begin=b(e.begin,{joinWith:""})}if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw P("skip, excludeEnd, returnEnd not compatible with endScope: {}"),H;if("object"!=typeof e.endScope||null===e.endScope)throw P("endScope must be object"),H;K(e,e.end,{key:"endScope"}),e.end=b(e.end,{joinWith:""})}}class J extends Error{constructor(e,t){super(e),this.name="HTMLInjectionError",this.html=t}}let q=Symbol("nomatch"),W=function(e){let r=Object.create(null),o=Object.create(null),a=[],s=!0,l="Could not find the language '{}', did you forget to load/include a language module?",v={disableAutodetect:!0,name:"Plain text",contains:[]},y={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:c};function x(e){return y.noHighlightRe.test(e)}function w(e,t,n){let i="",r="";"object"==typeof t?(i=e,n=t.ignoreIllegals,r=t.language):(F("10.7.0","highlight(lang, code, ...args) has been deprecated."),F("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),r=e,i=t),void 0===n&&(n=!0);let o={code:i,language:r};$("before:highlight",o);let a=o.result?o.result:k(o.language,o.code,n);return a.code=o.code,$("after:highlight",a),a}function k(e,o,a,c){let d=Object.create(null);function h(){if(!T.keywords)return void N.addText(B);let e=0;T.keywordPatternRe.lastIndex=0;let t=T.keywordPatternRe.exec(B),n="";for(;t;){n+=B.substring(e,t.index);let i=A.case_insensitive?t[0].toLowerCase():t[0],r=T.keywords[i];if(r){let[e,o]=r;if(N.addText(n),n="",d[i]=(d[i]||0)+1,d[i]<=7&&(F+=o),e.startsWith("_"))n+=t[0];else{let n=A.classNameAliases[e]||e;f(t[0],n)}}else n+=t[0];e=T.keywordPatternRe.lastIndex,t=T.keywordPatternRe.exec(B)}n+=B.substring(e),N.addText(n)}function u(){null!=T.subLanguage?function(){if(""===B)return;let e=null;if("string"==typeof T.subLanguage){if(!r[T.subLanguage])return N.addText(B);e=k(T.subLanguage,B,!0,O[T.subLanguage]),O[T.subLanguage]=e._top}else e=S(B,T.subLanguage.length?T.subLanguage:null);T.relevance>0&&(F+=e.relevance),N.__addSublanguage(e._emitter,e.language)}():h(),B=""}function f(e,t){""!==e&&(N.startScope(t),N.addText(e),N.endScope())}function m(e,t){let n=1,i=t.length-1;for(;n<=i;){if(!e._emit[n]){n++;continue}let i=A.classNameAliases[e[n]]||e[n],r=t[n];i?f(r,i):(B=r,h(),B=""),n++}}function v(e,t){return e.scope&&"string"==typeof e.scope&&N.openNode(A.classNameAliases[e.scope]||e.scope),e.beginScope&&(e.beginScope._wrap?(f(B,A.classNameAliases[e.beginScope._wrap]||e.beginScope._wrap),B=""):e.beginScope._multi&&(m(e.beginScope,t),B="")),T=Object.create(e,{parent:{value:T}})}let x={};function w(n,i){let r=i&&i[0];if(B+=n,null==r)return u(),0;if("begin"===x.type&&"end"===i.type&&x.index===i.index&&""===r){if(B+=o.slice(i.index,i.index+1),!s){let t=Error(`0 width match regex (${e})`);throw t.languageName=e,t.badRule=x.rule,t}return 1}if(x=i,"begin"===i.type){let e=i[0],n=i.rule,r=new t(n);for(let t of[n.__beforeBegin,n["on:begin"]])if(t&&(t(i,r),r.isMatchIgnored))return 0===T.matcher.regexIndex?(B+=e[0],1):(J=!0,0);return n.skip?B+=e:(n.excludeBegin&&(B+=e),u(),n.returnBegin||n.excludeBegin||(B=e)),v(n,i),n.returnBegin?0:e.length}if("illegal"!==i.type||a){if("end"===i.type){let e=function(e){let n=e[0],i=o.substring(e.index),r=function e(n,i,r){let o=function(e,t){let n=e&&e.exec(t);return n&&0===n.index}(n.endRe,r);if(o){if(n["on:end"]){let e=new t(n);n["on:end"](i,e),e.isMatchIgnored&&(o=!1)}if(o){for(;n.endsParent&&n.parent;)n=n.parent;return n}}if(n.endsWithParent)return e(n.parent,i,r)}(T,e,i);if(!r)return q;let a=T;T.endScope&&T.endScope._wrap?(u(),f(n,T.endScope._wrap)):T.endScope&&T.endScope._multi?(u(),m(T.endScope,e)):a.skip?B+=n:(a.returnEnd||a.excludeEnd||(B+=n),u(),a.excludeEnd&&(B=n));do T.scope&&N.closeNode(),T.skip||T.subLanguage||(F+=T.relevance),T=T.parent;while(T!==r.parent);return r.starts&&v(r.starts,e),a.returnEnd?0:n.length}(i);if(e!==q)return e}}else{let e=Error('Illegal lexeme "'+r+'" for mode "'+(T.scope||"<unnamed>")+'"');throw e.mode=T,e}if("illegal"===i.type&&""===r)return B+="\n",1;if(K>1e5&&K>3*i.index)throw Error("potential infinite loop, way more iterations than matches");return B+=r,r.length}let A=C(e);if(!A)throw P(l.replace("{}",e)),Error('Unknown language: "'+e+'"');let E=function(e){function t(t,n){return RegExp(p(t),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(e,t){t.position=this.position++,this.matchIndexes[this.matchAt]=t,this.regexes.push([t,e]),this.matchAt+=g(e)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);let e=this.regexes.map(e=>e[1]);this.matcherRe=t(b(e,{joinWith:"|"}),!0),this.lastIndex=0}exec(e){this.matcherRe.lastIndex=this.lastIndex;let t=this.matcherRe.exec(e);if(!t)return null;let n=t.findIndex((e,t)=>t>0&&void 0!==e),i=this.matchIndexes[n];return t.splice(0,n),Object.assign(t,i)}}class r{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(e){if(this.multiRegexes[e])return this.multiRegexes[e];let t=new n;return this.rules.slice(e).forEach(([e,n])=>t.addRule(e,n)),t.compile(),this.multiRegexes[e]=t,t}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(e,t){this.rules.push([e,t]),"begin"===t.type&&this.count++}exec(e){let t=this.getMatcher(this.regexIndex);t.lastIndex=this.lastIndex;let n=t.exec(e);if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{let t=this.getMatcher(0);t.lastIndex=this.lastIndex+1,n=t.exec(e)}return n&&(this.regexIndex+=n.position+1,this.regexIndex===this.count&&this.considerAll()),n}}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=i(e.classNameAliases||{}),function n(o,a){if(o.isCompiled)return o;[R,I,U,_].forEach(e=>e(o,a)),e.compilerExtensions.forEach(e=>e(o,a)),o.__beforeBegin=null,[L,z,j].forEach(e=>e(o,a)),o.isCompiled=!0;let s=null;return"object"==typeof o.keywords&&o.keywords.$pattern&&(o.keywords=Object.assign({},o.keywords),s=o.keywords.$pattern,delete o.keywords.$pattern),s=s||/\w+/,o.keywords&&(o.keywords=function e(t,n,i="keyword"){let r=Object.create(null);return"string"==typeof t?o(i,t.split(" ")):Array.isArray(t)?o(i,t):Object.keys(t).forEach(function(i){Object.assign(r,e(t[i],n,i))}),r;function o(e,t){n&&(t=t.map(e=>e.toLowerCase())),t.forEach(function(t){var n,i,o;let a=t.split("|");r[a[0]]=[e,(n=a[0],(i=a[1])?Number(i):+(o=n,!D.includes(o.toLowerCase())))]})}}(o.keywords,e.case_insensitive)),o.keywordPatternRe=t(s,!0),a&&(o.begin||(o.begin=/\B|\b/),o.beginRe=t(o.begin),o.end||o.endsWithParent||(o.end=/\B|\b/),o.end&&(o.endRe=t(o.end)),o.terminatorEnd=p(o.end)||"",o.endsWithParent&&a.terminatorEnd&&(o.terminatorEnd+=(o.end?"|":"")+a.terminatorEnd)),o.illegal&&(o.illegalRe=t(o.illegal)),o.contains||(o.contains=[]),o.contains=[].concat(...o.contains.map(function(e){var t;return((t="self"===e?o:e).variants&&!t.cachedVariants&&(t.cachedVariants=t.variants.map(function(e){return i(t,{variants:null},e)})),t.cachedVariants)?t.cachedVariants:!function e(t){return!!t&&(t.endsWithParent||e(t.starts))}(t)?Object.isFrozen(t)?i(t):t:i(t,{starts:t.starts?i(t.starts):null})})),o.contains.forEach(function(e){n(e,o)}),o.starts&&n(o.starts,a),o.matcher=function(e){let t=new r;return e.contains.forEach(e=>t.addRule(e.begin,{rule:e,type:"begin"})),e.terminatorEnd&&t.addRule(e.terminatorEnd,{type:"end"}),e.illegal&&t.addRule(e.illegal,{type:"illegal"}),t}(o),o}(e)}(A),M="",T=c||E,O={},N=new y.__emitter(y),$=[];for(let e=T;e!==A;e=e.parent)e.scope&&$.unshift(e.scope);$.forEach(e=>N.openNode(e));let B="",F=0,H=0,K=0,J=!1;try{if(A.__emitTokens)A.__emitTokens(o,N);else{for(T.matcher.considerAll();;){K++,J?J=!1:T.matcher.considerAll(),T.matcher.lastIndex=H;let e=T.matcher.exec(o);if(!e)break;let t=o.substring(H,e.index),n=w(t,e);H=e.index+n}w(o.substring(H))}return N.finalize(),M=N.toHTML(),{language:e,value:M,relevance:F,illegal:!1,_emitter:N,_top:T}}catch(t){if(t.message&&t.message.includes("Illegal"))return{language:e,value:n(o),illegal:!0,relevance:0,_illegalBy:{message:t.message,index:H,context:o.slice(H-100,H+100),mode:t.mode,resultSoFar:M},_emitter:N};if(s)return{language:e,value:n(o),illegal:!1,relevance:0,errorRaised:t,_emitter:N,_top:T};throw t}}function S(e,t){t=t||y.languages||Object.keys(r);let i=function(e){let t={value:n(e),illegal:!1,relevance:0,_top:v,_emitter:new y.__emitter(y)};return t._emitter.addText(e),t}(e),o=t.filter(C).filter(N).map(t=>k(t,e,!1));o.unshift(i);let[a,s]=o.sort((e,t)=>{if(e.relevance!==t.relevance)return t.relevance-e.relevance;if(e.language&&t.language){if(C(e.language).supersetOf===t.language)return 1;else if(C(t.language).supersetOf===e.language)return -1}return 0});return a.secondBest=s,a}function A(e){let t=null,n=function(e){let t=e.className+" ";t+=e.parentNode?e.parentNode.className:"";let n=y.languageDetectRe.exec(t);if(n){let t=C(n[1]);return t||(B(l.replace("{}",n[1])),B("Falling back to no-highlight mode for this block.",e)),t?n[1]:"no-highlight"}return t.split(/\s+/).find(e=>x(e)||C(e))}(e);if(x(n))return;if($("before:highlightElement",{el:e,language:n}),e.dataset.highlighted)return void console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",e);if(e.children.length>0&&(y.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(e)),y.throwUnescapedHTML))throw new J("One of your code blocks includes unescaped HTML.",e.innerHTML);let i=e.textContent,r=n?w(i,{language:n,ignoreIllegals:!0}):S(i);e.innerHTML=r.value,e.dataset.highlighted="yes";var a=r.language;let s=n&&o[n]||a;e.classList.add("hljs"),e.classList.add(`language-${s}`),e.result={language:r.language,re:r.relevance,relevance:r.relevance},r.secondBest&&(e.secondBest={language:r.secondBest.language,relevance:r.secondBest.relevance}),$("after:highlightElement",{el:e,result:r,text:i})}let E=!1;function M(){if("loading"===document.readyState){E||window.addEventListener("DOMContentLoaded",function(){M()},!1),E=!0;return}document.querySelectorAll(y.cssSelector).forEach(A)}function C(e){return r[e=(e||"").toLowerCase()]||r[o[e]]}function T(e,{languageName:t}){"string"==typeof e&&(e=[e]),e.forEach(e=>{o[e.toLowerCase()]=t})}function N(e){let t=C(e);return t&&!t.disableAutodetect}function $(e,t){a.forEach(function(n){n[e]&&n[e](t)})}for(let t in Object.assign(e,{highlight:w,highlightAuto:S,highlightAll:M,highlightElement:A,highlightBlock:function(e){return F("10.7.0","highlightBlock will be removed entirely in v12.0"),F("10.7.0","Please use highlightElement now."),A(e)},configure:function(e){y=i(y,e)},initHighlighting:()=>{M(),F("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){M(),F("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(t,n){let i=null;try{i=n(e)}catch(e){if(P("Language definition for '{}' could not be registered.".replace("{}",t)),s)P(e);else throw e;i=v}i.name||(i.name=t),r[t]=i,i.rawDefinition=n.bind(null,e),i.aliases&&T(i.aliases,{languageName:t})},unregisterLanguage:function(e){for(let t of(delete r[e],Object.keys(o)))o[t]===e&&delete o[t]},listLanguages:function(){return Object.keys(r)},getLanguage:C,registerAliases:T,autoDetection:N,inherit:i,addPlugin:function(e){var t;(t=e)["before:highlightBlock"]&&!t["before:highlightElement"]&&(t["before:highlightElement"]=e=>{t["before:highlightBlock"](Object.assign({block:e.el},e))}),t["after:highlightBlock"]&&!t["after:highlightElement"]&&(t["after:highlightElement"]=e=>{t["after:highlightBlock"](Object.assign({block:e.el},e))}),a.push(e)},removePlugin:function(e){let t=a.indexOf(e);-1!==t&&a.splice(t,1)}}),e.debugMode=function(){s=!1},e.safeMode=function(){s=!0},e.versionString="11.11.1",e.regex={concat:f,lookahead:d,either:m,optional:u,anyNumberOfTimes:h},O)"object"==typeof O[t]&&function e(t){return t instanceof Map?t.clear=t.delete=t.set=function(){throw Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=function(){throw Error("set is read-only")}),Object.freeze(t),Object.getOwnPropertyNames(t).forEach(n=>{let i=t[n],r=typeof i;"object"!==r&&"function"!==r||Object.isFrozen(i)||e(i)}),t}(O[t]);return Object.assign(e,O),e},V=W({});V.newInstance=()=>W({}),e.exports=V,V.HighlightJS=V,V.default=V},8932:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},9030:(e,t,n)=>{"use strict";n.d(t,{h:()=>r});var i=n(4701);let r=i.bP.create({name:"tableHeader",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:e=>{let t=e.getAttribute("colwidth");return t?t.split(",").map(e=>parseInt(e,10)):null}}}),tableRole:"header_cell",isolating:!0,parseHTML:()=>[{tag:"th"}],renderHTML({HTMLAttributes:e}){return["th",(0,i.KV)(this.options.HTMLAttributes,e),0]}})},9140:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},9144:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},9145:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("square-check-big",[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9399:(e,t,n)=>{"use strict";var i=n(2948);function r(){}function o(){}o.resetWarningCache=r,e.exports=function(){function e(e,t,n,r,o,a){if(a!==i){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:r};return n.PropTypes=n,n}},9621:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},9727:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},9803:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},9869:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let i=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}}]);