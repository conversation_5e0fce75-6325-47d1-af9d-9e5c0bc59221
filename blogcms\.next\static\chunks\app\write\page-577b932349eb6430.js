(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[426],{1754:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>w});var s=t(5155),l=t(2115),r=t(2108),i=t(5695),n=t(8590),o=t(6710),c=t(7213),d=t(5339),m=t(9869),u=t(4416),g=t(646);function x(e){let{imageUrl:a,altText:t,onImageChange:r,onAltTextChange:i}=e,[n,x]=(0,l.useState)(!1),[h,p]=(0,l.useState)(""),[b,f]=(0,l.useState)(!1),y=e=>e.type.startsWith("image/")?e.size>0xa00000?{valid:!1,error:"Image size too large. Maximum size is 10MB."}:["image/png","image/jpeg","image/jpg","image/gif","image/webp","image/svg+xml"].includes(e.type)?{valid:!0}:{valid:!1,error:"Unsupported image format. Please use PNG, JPG, JPEG, GIF, WebP, or SVG."}:{valid:!1,error:"Please upload an image file (PNG, JPG, JPEG, GIF, WebP, SVG)."},j=(0,l.useCallback)(async e=>{p("");let a=y(e);if(!a.valid)return void p(a.error||"Invalid file");x(!0);try{let a=new FileReader;a.onload=()=>{let e=a.result;r(e),x(!1)},a.onerror=()=>{p("Failed to process image. Please try again."),x(!1)},a.readAsDataURL(e)}catch(e){console.error("Upload failed:",e),p("Failed to process image. Please try again."),x(!1)}},[r]),v=(0,l.useCallback)(e=>{let a=e[0];a&&j(a),f(!1)},[j]),{getRootProps:N,getInputProps:w,isDragActive:k}=(0,o.VB)({onDrop:v,accept:{"image/*":[".png",".jpg",".jpeg",".gif",".webp",".svg"]},maxFiles:1,onDragEnter:()=>f(!0),onDragLeave:()=>f(!1)});return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 inline mr-1"}),"Featured Image"]}),h&&(0,s.jsxs)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 text-red-500 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-red-700 text-sm",children:h})]}),a?(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("img",{src:a,alt:t||"Featured image preview",className:"w-full h-48 object-cover rounded-lg border",onError:()=>{p("Failed to load image. Please check the URL or try uploading again."),r("")}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all rounded-lg flex items-center justify-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>{r(""),p("")},className:"opacity-0 group-hover:opacity-100 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-all",title:"Remove image",children:(0,s.jsx)(u.A,{className:"h-4 w-4"})})}),n&&(0,s.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Processing image..."})]})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-green-600",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:"Image uploaded successfully"})]})]}):(0,s.jsxs)("div",{...N(),className:"border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ".concat(k||b?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"),children:[(0,s.jsx)("input",{...w()}),(0,s.jsx)(m.A,{className:"h-8 w-8 mx-auto mb-3 text-gray-400"}),(0,s.jsx)("p",{className:"text-gray-600 mb-2",children:k?"Drop the image here...":"Drag & drop an image here, or click to select"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Supports: PNG, JPG, JPEG, GIF, WebP, SVG"}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Maximum size: 10MB"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Alt Text (for accessibility)"}),(0,s.jsx)("input",{type:"text",placeholder:"Describe the image for screen readers...",value:t,onChange:e=>i(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Provide a brief description of the image for accessibility and SEO"})]})]})}var h=t(3332),p=t(9946);let b=(0,p.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);function f(e){let{selectedTags:a,availableTags:t,onTagsChange:r}=e,[i,n]=(0,l.useState)(""),[o,c]=(0,l.useState)(!1),[d,m]=(0,l.useState)([]),[g,x]=(0,l.useState)(-1),p=(0,l.useRef)(null),f=(0,l.useRef)(null);(0,l.useEffect)(()=>{if(i.length>=2){let e=t.filter(e=>e.name.toLowerCase().includes(i.toLowerCase())&&!a.includes(e.id));m(e),c(e.length>0),x(-1)}else m([]),c(!1),x(-1)},[i,t,a]);let y=a.map(e=>t.find(a=>a.id===e)).filter(Boolean),j=e=>{var t;a.includes(e.id)||r([...a,e.id]),n(""),c(!1),x(-1),null==(t=p.current)||t.focus()},v=async e=>{try{let s=await fetch("/api/tags",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e})});if(s.ok){var t;let e=await s.json();r([...a,e.id]),n(""),c(!1),null==(t=p.current)||t.focus()}else{let e=await s.json();console.error("Failed to create tag:",e.error),n("")}}catch(e){console.error("Error creating tag:",e),n("")}},N=e=>{r(a.filter(a=>a!==e))};return(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 inline mr-1"}),"Tags"]}),y.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:y.map(e=>(0,s.jsxs)("span",{className:"inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full border border-blue-200",children:[e.name,(0,s.jsx)("button",{type:"button",onClick:()=>N(e.id),className:"hover:bg-blue-200 rounded-full p-0.5 transition-colors",title:"Remove ".concat(e.name," tag"),children:(0,s.jsx)(u.A,{className:"h-3 w-3"})})]},e.id))}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)("input",{ref:p,type:"text",value:i,onChange:e=>{n(e.target.value)},onKeyDown:e=>{"ArrowDown"===e.key?(e.preventDefault(),x(e=>e<d.length-1?e+1:e)):"ArrowUp"===e.key?(e.preventDefault(),x(e=>e>0?e-1:-1)):"Enter"===e.key?(e.preventDefault(),g>=0&&d[g]?j(d[g]):i.trim()&&0===d.length&&v(i.trim())):"Escape"===e.key?(c(!1),x(-1)):"Tab"===e.key&&g>=0&&(e.preventDefault(),j(d[g]))},onFocus:()=>{i.length>=2&&d.length>0&&c(!0)},onBlur:()=>{setTimeout(()=>{c(!1),x(-1)},200)},placeholder:"Type to search tags or create new ones...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),o&&(0,s.jsxs)("div",{ref:f,className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto",children:[d.map((e,a)=>(0,s.jsx)("button",{type:"button",onClick:()=>j(e),className:"w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors ".concat(a===g?"bg-blue-50 text-blue-700":""),children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{children:e.name}),(0,s.jsx)(h.A,{className:"h-3 w-3 text-gray-400"})]})},e.id)),i.trim()&&0===d.length&&(0,s.jsx)("button",{type:"button",onClick:()=>v(i.trim()),className:"w-full text-left px-3 py-2 hover:bg-gray-100 transition-colors border-t border-gray-200",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-green-600",children:[(0,s.jsx)(b,{className:"h-3 w-3"}),(0,s.jsxs)("span",{children:['Create "',i.trim(),'"']})]})})]})]})}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Type at least 2 characters to see suggestions. Press Enter to create new tags."})]})]})}var y=t(4186);let j=(0,p.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),v=(0,p.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),N=(0,p.A)("folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);function w(){let{data:e,status:a}=(0,r.useSession)(),t=(0,i.useRouter)(),[o,c]=(0,l.useState)({title:"",subtitle:"",content:"",categoryId:"",tags:[],featuredImageUrl:"",featuredImageAlt:"",status:"DRAFT",metaDescription:"",allowComments:!0}),[d,m]=(0,l.useState)([]),[u,g]=(0,l.useState)([]),[h,p]=(0,l.useState)(!1),[b,w]=(0,l.useState)(!1),[k,S]=(0,l.useState)(null);(0,l.useEffect)(()=>{"unauthenticated"===a&&t.push("/auth/signin?callbackUrl=/write")},[a,t]),(0,l.useEffect)(()=>{let a=async()=>{try{let[e,a]=await Promise.all([fetch("/api/categories"),fetch("/api/tags")]);if(e.ok){let a=await e.json();m(a)}if(a.ok){let e=await a.json();g(e)}}catch(e){console.error("Error loading data:",e)}};e&&a()},[e]),(0,l.useEffect)(()=>{if(!o.title&&!o.content)return;let e=setTimeout(()=>{A("DRAFT")},3e4);return()=>clearTimeout(e)},[o.title,o.content]);let A=async function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"DRAFT";if(e&&o.title.trim()){w(!0);try{let e=await fetch("/api/articles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...o,status:a})});if(e.ok){let s=await e.json();S(new Date),"PUBLISHED"===a&&t.push("/articles/".concat(s.slug))}else{let a=await e.json();alert(a.error||"Failed to save article")}}catch(e){console.error("Error saving article:",e),alert("Failed to save article")}finally{w(!1)}}};return"loading"===a?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):e?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto py-8 px-4",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Write Article"}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[k&&(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 inline mr-1"}),"Saved ",k.toLocaleTimeString()]}),(0,s.jsxs)("button",{onClick:()=>A("DRAFT"),disabled:b,className:"flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50",children:[(0,s.jsx)(j,{className:"h-4 w-4"}),b?"Saving...":"Save Draft"]}),(0,s.jsxs)("button",{onClick:()=>A("PUBLISHED"),disabled:b||!o.title.trim(),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:[(0,s.jsx)(v,{className:"h-4 w-4"}),"Publish"]})]})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsx)("input",{type:"text",placeholder:"Article title...",value:o.title,onChange:e=>c({...o,title:e.target.value}),className:"w-full text-3xl font-bold border-none outline-none placeholder-gray-400 resize-none"})}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("input",{type:"text",placeholder:"Add a subtitle (optional)...",value:o.subtitle,onChange:e=>c({...o,subtitle:e.target.value}),className:"w-full text-xl text-gray-600 border-none outline-none placeholder-gray-400"})}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(x,{imageUrl:o.featuredImageUrl,altText:o.featuredImageAlt,onImageChange:e=>c({...o,featuredImageUrl:e}),onAltTextChange:e=>c({...o,featuredImageAlt:e})})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,s.jsx)(N,{className:"h-4 w-4 inline mr-1"}),"Category"]}),(0,s.jsxs)("select",{value:o.categoryId,onChange:e=>c({...o,categoryId:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Select a category"}),d.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsx)("div",{children:(0,s.jsx)(f,{selectedTags:o.tags,availableTags:u,onTagsChange:e=>c({...o,tags:e})})})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Meta Description (SEO)"}),(0,s.jsx)("textarea",{placeholder:"Brief description for search engines...",value:o.metaDescription,onChange:e=>c({...o,metaDescription:e.target.value}),rows:2,maxLength:160,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[o.metaDescription.length,"/160 characters"]})]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border overflow-hidden",children:(0,s.jsx)(n.d,{content:o.content,onChange:e=>c({...o,content:e}),placeholder:"Tell your story..."})})]})}):null}},3937:(e,a,t)=>{Promise.resolve().then(t.bind(t,1754))}},e=>{var a=a=>e(e.s=a);e.O(0,[4,277,533,445,590,441,684,358],()=>a(3937)),_N_E=e.O()}]);