"use strict";(()=>{var e={};e.id=612,e.ids=[612],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{function s(e){return e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function a(e){return Math.ceil(e.trim().split(/\s+/).length/200)}function o(e,r=160){let t=e.replace(/<[^>]*>/g,"").replace(/[#*_`]/g,"").trim();return t.length<=r?t:t.substring(0,r).trim()+"..."}function n(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function i(e){let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),{isValid:0===r.length,errors:r}}t.d(r,{DT:()=>n,Oj:()=>i,_C:()=>a,dn:()=>o,z9:()=>s})},16698:e=>{e.exports=require("node:async_hooks")},21222:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(96559),o=t(48088),n=t(37719),i=t(32190),u=t(85663),p=t(31183),l=t(10974);async function d(e){try{let{email:r,username:t,password:s,firstName:a,lastName:o}=await e.json();if(!r||!t||!s)return i.NextResponse.json({error:"Email, username, and password are required"},{status:400});if(!(0,l.DT)(r))return i.NextResponse.json({error:"Invalid email format"},{status:400});let n=(0,l.Oj)(s);if(!n.isValid)return i.NextResponse.json({error:"Password validation failed",details:n.errors},{status:400});if(await p.z.user.findFirst({where:{OR:[{email:r},{username:t}]}}))return i.NextResponse.json({error:"User with this email or username already exists"},{status:409});let d=await u.Ay.hash(s,12),c=await p.z.user.create({data:{email:r,username:t,password:d,firstName:a||null,lastName:o||null,role:"READER"},select:{id:!0,email:!0,username:!0,firstName:!0,lastName:!0,role:!0,createdAt:!0}});return i.NextResponse.json({message:"User created successfully",user:c},{status:201})}catch(e){return console.error("Registration error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:h}=c;function g(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},55511:e=>{e.exports=require("crypto")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,663,275],()=>t(21222));module.exports=s})();