(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[778],{646:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2560:(e,r,s)=>{Promise.resolve().then(s.bind(s,4820))},2657:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4820:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>p});var t=s(5155),a=s(2115),n=s(5695),o=s(6874),l=s.n(o),i=s(646),d=s(5339),c=s(1007),m=s(8883),u=s(2919),x=s(8749),f=s(2657);function p(){let e=(0,n.useRouter)(),[r,s]=(0,a.useState)({firstName:"",lastName:"",username:"",email:"",password:"",confirmPassword:""}),[o,p]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),[b,N]=(0,a.useState)(!1),[v,y]=(0,a.useState)({}),[w,j]=(0,a.useState)(!1),k=()=>{let e={};return r.firstName.trim()||(e.firstName="First name is required"),r.lastName.trim()||(e.lastName="Last name is required"),r.username.trim()?r.username.length<3?e.username="Username must be at least 3 characters":/^[a-zA-Z0-9_]+$/.test(r.username)||(e.username="Username can only contain letters, numbers, and underscores"):e.username="Username is required",r.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.email)||(e.email="Please enter a valid email address"):e.email="Email is required",r.password?r.password.length<6&&(e.password="Password must be at least 6 characters"):e.password="Password is required",r.confirmPassword?r.password!==r.confirmPassword&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your password",y(e),0===Object.keys(e).length},A=async s=>{if(s.preventDefault(),k()){N(!0),y({});try{let s=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:r.firstName,lastName:r.lastName,username:r.username,email:r.email,password:r.password})}),t=await s.json();s.ok?(j(!0),setTimeout(()=>{e.push("/auth/signin?message=Account created successfully")},2e3)):t.errors?y(t.errors):y({general:t.error||"An error occurred during registration"})}catch(e){y({general:"An error occurred. Please try again."})}finally{N(!1)}}};return w?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(i.A,{className:"mx-auto h-16 w-16 text-green-500"}),(0,t.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Account Created!"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Your account has been created successfully. Redirecting to sign in..."})]})})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-foreground",children:"Create your account"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-muted-foreground",children:["Or"," ",(0,t.jsx)(l(),{href:"/auth/signin",className:"font-medium text-primary hover:text-primary/80",children:"sign in to your existing account"})]})]}),v.general&&(0,t.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-destructive"}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm text-destructive",children:v.general})})]})}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:A,children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-foreground",children:"First Name"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"firstName",name:"firstName",type:"text",required:!0,value:r.firstName,onChange:e=>s({...r,firstName:e.target.value}),className:"appearance-none relative block w-full pl-10 pr-3 py-2 border ".concat(v.firstName?"border-destructive":"border-input"," placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm"),placeholder:"First name"})]}),v.firstName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:v.firstName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-foreground",children:"Last Name"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)("input",{id:"lastName",name:"lastName",type:"text",required:!0,value:r.lastName,onChange:e=>s({...r,lastName:e.target.value}),className:"appearance-none relative block w-full px-3 py-2 border ".concat(v.lastName?"border-destructive":"border-input"," placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm"),placeholder:"Last name"})}),v.lastName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:v.lastName})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-foreground",children:"Username"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"username",name:"username",type:"text",required:!0,value:r.username,onChange:e=>s({...r,username:e.target.value.toLowerCase()}),className:"appearance-none relative block w-full pl-10 pr-3 py-2 border ".concat(v.username?"border-destructive":"border-input"," placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm"),placeholder:"Choose a username"})]}),v.username&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:v.username})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground",children:"Email address"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:r.email,onChange:e=>s({...r,email:e.target.value}),className:"appearance-none relative block w-full pl-10 pr-3 py-2 border ".concat(v.email?"border-destructive":"border-input"," placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm"),placeholder:"Enter your email"})]}),v.email&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:v.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground",children:"Password"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"password",name:"password",type:o?"text":"password",autoComplete:"new-password",required:!0,value:r.password,onChange:e=>s({...r,password:e.target.value}),className:"appearance-none relative block w-full pl-10 pr-10 py-2 border ".concat(v.password?"border-destructive":"border-input"," placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm"),placeholder:"Create a password"}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!o),children:o?(0,t.jsx)(x.A,{className:"h-5 w-5 text-muted-foreground"}):(0,t.jsx)(f.A,{className:"h-5 w-5 text-muted-foreground"})})]}),v.password&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:v.password})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-foreground",children:"Confirm Password"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:h?"text":"password",autoComplete:"new-password",required:!0,value:r.confirmPassword,onChange:e=>s({...r,confirmPassword:e.target.value}),className:"appearance-none relative block w-full pl-10 pr-10 py-2 border ".concat(v.confirmPassword?"border-destructive":"border-input"," placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm"),placeholder:"Confirm your password"}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g(!h),children:h?(0,t.jsx)(x.A,{className:"h-5 w-5 text-muted-foreground"}):(0,t.jsx)(f.A,{className:"h-5 w-5 text-muted-foreground"})})]}),v.confirmPassword&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:v.confirmPassword})]})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:b,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring disabled:opacity-50 disabled:cursor-not-allowed",children:b?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create account"})})]})]})})}},5339:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,r,s)=>{"use strict";var t=s(8999);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},8749:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9946:(e,r,s)=>{"use strict";s.d(r,{A:()=>m});var t=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),o=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return r.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim()},i=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,t.forwardRef)((e,r)=>{let{color:s="currentColor",size:a=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:c="",children:m,iconNode:u,...x}=e;return(0,t.createElement)("svg",{ref:r,...d,width:a,height:a,stroke:s,strokeWidth:o?24*Number(n)/Number(a):n,className:l("lucide",c),...!m&&!i(x)&&{"aria-hidden":"true"},...x},[...u.map(e=>{let[r,s]=e;return(0,t.createElement)(r,s)}),...Array.isArray(m)?m:[m]])}),m=(e,r)=>{let s=(0,t.forwardRef)((s,n)=>{let{className:i,...d}=s;return(0,t.createElement)(c,{ref:n,iconNode:r,className:l("lucide-".concat(a(o(e))),"lucide-".concat(e),i),...d})});return s.displayName=o(e),s}}},e=>{var r=r=>e(e.s=r);e.O(0,[874,441,684,358],()=>r(2560)),_N_E=e.O()}]);