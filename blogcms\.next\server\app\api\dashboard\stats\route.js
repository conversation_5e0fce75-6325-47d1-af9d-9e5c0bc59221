"use strict";(()=>{var e={};e.id=694,e.ids=[694],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},12909:(e,r,t)=>{t.d(r,{N:()=>u});var s=t(13581),a=t(60890),o=t(31183),i=t(85663);let u={adapter:(0,a.y)(o.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await o.z.user.findUnique({where:{email:e.email}});return r&&await i.Ay.compare(e.password,r.password)?(await o.z.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,username:r.username,role:r.role,firstName:r.firstName,lastName:r.lastName,avatarUrl:r.avatarUrl}):null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.username=r.username),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.username=r.username),e)},pages:{signIn:"/auth/signin"}}},16698:e=>{e.exports=require("node:async_hooks")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72278:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>l});var a=t(96559),o=t(48088),i=t(37719),u=t(32190),n=t(19854),p=t(12909),d=t(31183);async function l(){try{let e=await (0,n.getServerSession)(p.N);if(!e?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});let[r,t,s,a,o,i]=await Promise.all([d.z.article.count({where:{authorId:e.user.id}}),d.z.article.count({where:{authorId:e.user.id,status:"PUBLISHED"}}),d.z.article.count({where:{authorId:e.user.id,status:"DRAFT"}}),d.z.article.aggregate({where:{authorId:e.user.id},_sum:{viewCount:!0}}),d.z.comment.count({where:{article:{authorId:e.user.id}}}),d.z.reaction.count({where:{article:{authorId:e.user.id}}})]),l={totalArticles:r,publishedArticles:t,draftArticles:s,totalViews:a._sum.viewCount||0,totalComments:o,totalReactions:i};return u.NextResponse.json(l)}catch(e){return console.error("Error fetching dashboard stats:",e),u.NextResponse.json({error:"Failed to fetch dashboard statistics"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:m}=c;function q(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},73024:e=>{e.exports=require("node:fs")},74075:e=>{e.exports=require("zlib")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,663,819,275],()=>t(72278));module.exports=s})();