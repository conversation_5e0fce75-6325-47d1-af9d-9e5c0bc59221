"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[590],{8590:(e,s,t)=>{t.d(s,{d:()=>eh});var r=t(5155),a=t(5109),l=t(353),i=t(1891),o=t(192),n=t(423),c=t(3557),d=t(9030),u=t(3007),m=t(6761),g=t(4109),b=t(4589),h=t(4652),x=t(1580),p=t(7e3),j=t(4927),v=t(7192),y=t(5254),f=t(3356),N=t(6333),w=t(3083),A=t(7360),C=t(34),k=t(8033),F=t(5625),M=t(4923),L=t(3709),B=t(2115),E=t(6710),S=t(9727),T=t(9144),D=t(7240),I=t(2643),H=t(9621),P=t(8440),U=t(2705),R=t(2406),z=t(5968),V=t(9140),W=t(9145),O=t(224),Y=t(7325),G=t(7733),Q=t(8702),_=t(6340),$=t(8164),q=t(7855),J=t(7213),X=t(9803),Z=t(5112),K=t(2713),ee=t(3127),es=t(4347),et=t(3500),er=t(8673),ea=t(2976),el=t(7434),ei=t(3654),eo=t(8932);function en(e){let{editor:s,onOpenMediaDialog:t,onOpenChartDialog:a,onOpenTableDialog:l}=e,[i,o]=(0,B.useState)(!1),[n,c]=(0,B.useState)(!1),[d,u]=(0,B.useState)(!1);return(0,r.jsxs)("div",{className:"border-b bg-gray-50 p-2 flex flex-wrap gap-1 items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleBold().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("bold")?"bg-gray-300":""),title:"Bold",children:(0,r.jsx)(S.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleItalic().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("italic")?"bg-gray-300":""),title:"Italic",children:(0,r.jsx)(T.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleUnderline().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("underline")?"bg-gray-300":""),title:"Underline",children:(0,r.jsx)(D.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleStrike().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("strike")?"bg-gray-300":""),title:"Strikethrough",children:(0,r.jsx)(I.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleCode().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("code")?"bg-gray-300":""),title:"Inline Code",children:(0,r.jsx)(H.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleHeading({level:1}).run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("heading",{level:1})?"bg-gray-300":""),title:"Heading 1",children:(0,r.jsx)(P.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleHeading({level:2}).run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("heading",{level:2})?"bg-gray-300":""),title:"Heading 2",children:(0,r.jsx)(U.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleHeading({level:3}).run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("heading",{level:3})?"bg-gray-300":""),title:"Heading 3",children:(0,r.jsx)(R.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleBulletList().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("bulletList")?"bg-gray-300":""),title:"Bullet List",children:(0,r.jsx)(z.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleOrderedList().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("orderedList")?"bg-gray-300":""),title:"Numbered List",children:(0,r.jsx)(V.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleTaskList().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("taskList")?"bg-gray-300":""),title:"Task List",children:(0,r.jsx)(W.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleBlockquote().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("blockquote")?"bg-gray-300":""),title:"Quote",children:(0,r.jsx)(O.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,r.jsx)("button",{onClick:()=>s.chain().focus().setTextAlign("left").run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive({textAlign:"left"})?"bg-gray-300":""),title:"Align Left",children:(0,r.jsx)(Y.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().setTextAlign("center").run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive({textAlign:"center"})?"bg-gray-300":""),title:"Align Center",children:(0,r.jsx)(G.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().setTextAlign("right").run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive({textAlign:"right"})?"bg-gray-300":""),title:"Align Right",children:(0,r.jsx)(Q.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().setTextAlign("justify").run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive({textAlign:"justify"})?"bg-gray-300":""),title:"Justify",children:(0,r.jsx)(_.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,r.jsx)("button",{onClick:()=>{let e=window.prompt("Enter URL:");e&&s.chain().focus().setLink({href:e}).run()},className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("link")?"bg-gray-300":""),title:"Add Link",children:(0,r.jsx)($.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().unsetLink().run(),className:"p-2 rounded hover:bg-gray-200",title:"Remove Link",disabled:!s.isActive("link"),children:(0,r.jsx)(q.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"p-2 rounded hover:bg-gray-200",title:"Insert Media (Images, Videos, Audio, PDFs)",children:(0,r.jsx)(J.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>{let e=window.prompt("Enter YouTube URL:");e&&s.chain().focus().setYoutubeVideo({src:e}).run()},className:"p-2 rounded hover:bg-gray-200",title:"Insert Video",children:(0,r.jsx)(X.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:l,className:"p-2 rounded hover:bg-gray-200",title:"Insert Table",children:(0,r.jsx)(Z.A,{className:"h-4 w-4"})}),a&&(0,r.jsx)("button",{type:"button",onClick:a,className:"p-2 rounded hover:bg-gray-200",title:"Insert Chart",children:(0,r.jsx)(K.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2 relative",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{onClick:()=>o(!i),className:"p-2 rounded hover:bg-gray-200",title:"Text Color",children:(0,r.jsx)(ee.A,{className:"h-4 w-4"})}),i&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-1 p-2 bg-white border rounded shadow-lg z-10 grid grid-cols-5 gap-1",children:["#000000","#374151","#6B7280","#9CA3AF","#EF4444","#F97316","#F59E0B","#EAB308","#84CC16","#22C55E","#10B981","#14B8A6","#06B6D4","#0EA5E9","#3B82F6","#6366F1","#8B5CF6","#A855F7","#D946EF","#EC4899"].map(e=>(0,r.jsx)("button",{onClick:()=>{s.chain().focus().setColor(e).run(),o(!1)},className:"w-6 h-6 rounded border",style:{backgroundColor:e}},e))})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{onClick:()=>c(!n),className:"p-2 rounded hover:bg-gray-200",title:"Highlight",children:(0,r.jsx)(es.A,{className:"h-4 w-4"})}),n&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-1 p-2 bg-white border rounded shadow-lg z-10 grid grid-cols-5 gap-1",children:["#FEF3C7","#FDE68A","#FCD34D","#F59E0B","#FECACA","#FCA5A5","#F87171","#EF4444","#D1FAE5","#A7F3D0","#6EE7B7","#10B981","#DBEAFE","#93C5FD","#60A5FA","#3B82F6","#E0E7FF","#C7D2FE","#A5B4FC","#6366F1"].map(e=>(0,r.jsx)("button",{onClick:()=>{s.chain().focus().toggleHighlight({color:e}).run(),c(!1)},className:"w-6 h-6 rounded border",style:{backgroundColor:e}},e))})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2 relative",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("button",{onClick:()=>u(!d),className:"p-2 rounded hover:bg-gray-200",title:"Font Family",children:(0,r.jsx)(et.A,{className:"h-4 w-4"})}),d&&(0,r.jsx)("div",{className:"absolute top-full left-0 mt-1 p-2 bg-white border rounded shadow-lg z-10 min-w-32",children:["Inter","Arial","Helvetica","Times New Roman","Georgia","Courier New","Monaco","Roboto","Open Sans","Lato"].map(e=>(0,r.jsx)("button",{onClick:()=>{s.chain().focus().setFontFamily(e).run(),u(!1)},className:"block w-full text-left px-2 py-1 hover:bg-gray-100 text-sm",style:{fontFamily:e},children:e},e))})]})}),(0,r.jsxs)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:[(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleSubscript().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("subscript")?"bg-gray-300":""),title:"Subscript",children:(0,r.jsx)(er.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleSuperscript().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("superscript")?"bg-gray-300":""),title:"Superscript",children:(0,r.jsx)(ea.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"flex items-center gap-1 border-r pr-2 mr-2",children:(0,r.jsx)("button",{onClick:()=>s.chain().focus().toggleCodeBlock().run(),className:"p-2 rounded hover:bg-gray-200 ".concat(s.isActive("codeBlock")?"bg-gray-300":""),title:"Code Block",children:(0,r.jsx)(el.A,{className:"h-4 w-4"})})}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)("button",{onClick:()=>s.chain().focus().undo().run(),className:"p-2 rounded hover:bg-gray-200",title:"Undo",disabled:!s.can().undo(),children:(0,r.jsx)(ei.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>s.chain().focus().redo().run(),className:"p-2 rounded hover:bg-gray-200",title:"Redo",disabled:!s.can().redo(),children:(0,r.jsx)(eo.A,{className:"h-4 w-4"})})]})]})}var ec=t(4416),ed=t(5339),eu=t(9869),em=t(646);function eg(e){let{isOpen:s,onClose:t,onInsert:a}=e,[l,i]=(0,B.useState)("upload"),[o,n]=(0,B.useState)(""),[c,d]=(0,B.useState)(!1),[u,m]=(0,B.useState)([]),[g,b]=(0,B.useState)(""),[h,x]=(0,B.useState)(""),p={image:0xa00000,video:0x6400000,pdf:0x1900000,audio:0x3200000},j=e=>{let s=e.type.startsWith("image/"),t=e.type.startsWith("video/"),r="application/pdf"===e.type,a=e.type.startsWith("audio/");if(!s&&!t&&!r&&!a)return{valid:!1,error:"Unsupported file type. Please upload images, videos, PDFs, or audio files."};let l=p.image;if(t?l=p.video:r?l=p.pdf:a&&(l=p.audio),e.size>l){let e=Math.round(l/1048576);return{valid:!1,error:"File size too large. Maximum size is ".concat(e,"MB.")}}return{valid:!0}},v=(0,B.useCallback)(async e=>{b("");let s=e[0];if(!s)return;let t=j(s);if(!t.valid)return void b(t.error||"Invalid file");d(!0);try{let e=new FileReader;e.onload=()=>{let t=e.result,r="image";s.type.startsWith("video/")?r="video":"application/pdf"===s.type?r="pdf":s.type.startsWith("audio/")&&(r="audio"),m([{file:s,preview:t,type:r}])},e.readAsDataURL(s)}catch(e){console.error("Upload failed:",e),b("Failed to process file. Please try again.")}finally{d(!1)}},[]),{getRootProps:y,getInputProps:f,isDragActive:N}=(0,E.VB)({onDrop:v,accept:{"image/*":[".png",".jpg",".jpeg",".gif",".webp",".svg"],"video/*":[".mp4",".webm",".ogg"],"audio/*":[".mp3",".wav",".ogg",".m4a"],"application/pdf":[".pdf"]},maxFiles:1}),w=e=>{let s=e.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);if(s)return{embedUrl:"https://www.youtube.com/embed/".concat(s[1]),type:"youtube"};let t=e.match(/(?:vimeo\.com\/)(?:.*\/)?(\d+)/);if(t)return{embedUrl:"https://player.vimeo.com/video/".concat(t[1]),type:"vimeo"};let r=e.match(/(?:dailymotion\.com\/video\/|dai\.ly\/)([a-zA-Z0-9]+)/);return r?{embedUrl:"https://www.dailymotion.com/embed/video/".concat(r[1]),type:"dailymotion"}:null},A=e=>{a(e.preview,e.type),m([]),t()};return s?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Insert Media"}),(0,r.jsx)("button",{type:"button",onClick:t,className:"p-1 hover:bg-gray-100 rounded",title:"Close dialog",children:(0,r.jsx)(ec.A,{className:"h-5 w-5"})})]}),g&&(0,r.jsxs)("div",{className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2",children:[(0,r.jsx)(ed.A,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)("span",{className:"text-red-700 text-sm",children:g})]}),(0,r.jsxs)("div",{className:"flex border-b mb-4",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>i("upload"),className:"px-4 py-2 font-medium ".concat("upload"===l?"border-b-2 border-blue-500 text-blue-600":"text-gray-600 hover:text-gray-800"),children:[(0,r.jsx)(eu.A,{className:"h-4 w-4 inline mr-2"}),"Upload"]}),(0,r.jsxs)("button",{type:"button",onClick:()=>i("url"),className:"px-4 py-2 font-medium ".concat("url"===l?"border-b-2 border-blue-500 text-blue-600":"text-gray-600 hover:text-gray-800"),children:[(0,r.jsx)($.A,{className:"h-4 w-4 inline mr-2"}),"URL"]}),(0,r.jsxs)("button",{type:"button",onClick:()=>i("embed"),className:"px-4 py-2 font-medium ".concat("embed"===l?"border-b-2 border-blue-500 text-blue-600":"text-gray-600 hover:text-gray-800"),children:[(0,r.jsx)(X.A,{className:"h-4 w-4 inline mr-2"}),"Embed"]})]}),"upload"===l&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{...y(),className:"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ".concat(N?"border-blue-400 bg-blue-50":"border-gray-300 hover:border-gray-400"),children:[(0,r.jsx)("input",{...f()}),(0,r.jsx)(eu.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-400"}),(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:N?"Drop the file here...":"Drag & drop a file here, or click to select"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Supports: Images (PNG, JPG, GIF, WebP, SVG), Videos (MP4, WebM), Audio (MP3, WAV), PDFs"}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Max sizes: Images 10MB, Videos 100MB, Audio 50MB, PDFs 25MB"})]}),c&&(0,r.jsxs)("div",{className:"mt-4 text-center",children:[(0,r.jsx)("div",{className:"inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Processing file..."})]}),u.length>0&&(0,r.jsxs)("div",{className:"mt-4 space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-700",children:"Preview:"}),u.map((e,s)=>(0,r.jsxs)("div",{className:"border rounded-lg p-4 bg-gray-50",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(em.A,{className:"h-4 w-4 text-green-500"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.file.name}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["(",Math.round(e.file.size/1024),"KB)"]})]})}),"image"===e.type&&(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsx)("img",{src:e.preview,alt:"Preview",className:"max-w-full h-32 object-contain rounded border"})}),"video"===e.type&&(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsx)("video",{src:e.preview,className:"max-w-full h-32 rounded border",controls:!0})}),"audio"===e.type&&(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsx)("audio",{src:e.preview,className:"w-full",controls:!0})}),(0,r.jsxs)("button",{type:"button",onClick:()=>A(e),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700",children:["Insert ",e.type]})]},s))]})]}),"url"===l&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Media URL"}),(0,r.jsx)("input",{type:"url",value:o,onChange:e=>n(e.target.value),placeholder:"https://example.com/image.jpg",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{className:"font-medium mb-2",children:"Supported URLs:"}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(J.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Direct image links (.jpg, .png, .gif, etc.)"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(X.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"YouTube, Vimeo, Dailymotion, or direct video links"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(el.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"PDF documents and audio files"})]})]}),(0,r.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:[(0,r.jsx)("p",{children:"Examples:"}),(0,r.jsx)("p",{children:"• https://youtube.com/watch?v=dQw4w9WgXcQ"}),(0,r.jsx)("p",{children:"• https://youtu.be/dQw4w9WgXcQ"}),(0,r.jsx)("p",{children:"• https://vimeo.com/123456789"}),(0,r.jsx)("p",{children:"• https://example.com/image.jpg"})]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>{if(!o.trim())return;b("");let e="image",s=o.trim(),r=w(s);r?(e="video",s=r.embedUrl):s.includes("vimeo.com")||s.includes("youtube.com")||s.includes("youtu.be")||s.includes("dailymotion.com")?e="video":s.endsWith(".pdf")?e="pdf":s.match(/\.(mp3|wav|ogg|m4a)$/i)?e="audio":s.match(/\.(mp4|webm|ogg|avi|mov)$/i)&&(e="video"),a(s,e),n(""),t()},disabled:!o.trim(),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Insert Media"})]})}),"embed"===l&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Embed Code"}),(0,r.jsx)("textarea",{value:h,onChange:e=>x(e.target.value),placeholder:'<iframe src="https://www.youtube.com/embed/..." width="560" height="315" frameborder="0"></iframe>',rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{className:"font-medium mb-2",children:"Supported embed codes:"}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(X.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"YouTube, Vimeo, Dailymotion iframes"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(X.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Social media embeds (Twitter, Instagram)"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(X.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Other iframe-based embeds"})]})]}),(0,r.jsx)("div",{className:"mt-2 text-xs text-gray-500",children:(0,r.jsx)("p",{children:"Paste the complete embed code from the platform's share/embed option."})})]}),(0,r.jsx)("button",{type:"button",onClick:()=>{h.trim()&&(b(""),a(h.trim(),"video"),x(""),t())},disabled:!h.trim(),className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed",children:"Insert Embed"})]})})]})}):null}function eb(e){let{isOpen:s,onClose:t,onInsert:a}=e,[l,i]=(0,B.useState)(3),[o,n]=(0,B.useState)(3);return s?(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold flex items-center gap-2",children:[(0,r.jsx)(Z.A,{className:"h-5 w-5"}),"Insert Table"]}),(0,r.jsx)("button",{type:"button",onClick:t,className:"p-1 hover:bg-gray-100 rounded",children:(0,r.jsx)(ec.A,{className:"h-5 w-5"})})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Rows"}),(0,r.jsx)("input",{type:"number",min:"2",max:"20",value:l,onChange:e=>i(Math.max(2,Math.min(20,parseInt(e.target.value)||2))),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Includes header row"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Number of Columns"}),(0,r.jsx)("input",{type:"number",min:"1",max:"10",value:o,onChange:e=>n(Math.max(1,Math.min(10,parseInt(e.target.value)||1))),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Preview"}),(0,r.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,r.jsx)("table",{className:"w-full border-collapse",children:(0,r.jsx)("tbody",{children:(()=>{let e=[];e.push((0,r.jsx)("tr",{className:"bg-gray-100",children:Array.from({length:o},(e,s)=>(0,r.jsxs)("th",{className:"border border-gray-300 p-2 text-sm font-semibold",children:["Header ",s+1]},s))},"header"));for(let s=1;s<l;s++)e.push((0,r.jsx)("tr",{children:Array.from({length:o},(e,t)=>(0,r.jsxs)("td",{className:"border border-gray-300 p-2 text-sm",children:["Cell ",s,"-",t+1]},t))},s));return e})()})})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Quick Select"}),(0,r.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,r.jsx)("button",{type:"button",onClick:()=>{i(3),n(3)},className:"p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm",children:"3\xd73"}),(0,r.jsx)("button",{type:"button",onClick:()=>{i(4),n(4)},className:"p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm",children:"4\xd74"}),(0,r.jsx)("button",{type:"button",onClick:()=>{i(5),n(3)},className:"p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm",children:"5\xd73"}),(0,r.jsx)("button",{type:"button",onClick:()=>{i(3),n(5)},className:"p-2 border border-gray-300 rounded hover:border-blue-500 hover:bg-blue-50 text-sm",children:"3\xd75"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-3",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,r.jsx)("button",{type:"button",onClick:()=>{a(l,o),t()},className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"Insert Table"})]})]})]})}):null}function eh(e){let{content:s="",onChange:t,placeholder:S="Start writing your story...",className:T="",readOnly:D=!1}=e,[I,H]=(0,B.useState)(!1),[P,U]=(0,B.useState)(!1),[R,z]=(0,B.useState)(!1),V=(0,C.$)();V.register("javascript",k.A),V.register("typescript",F.A),V.register("html",M.A),V.register("css",L.A);let W=(0,a.hG)({extensions:[l.A.configure({codeBlock:!1}),i.Ay.configure({HTMLAttributes:{class:"rounded-lg max-w-full h-auto"}}),o.Ay.configure({resizable:!0,HTMLAttributes:{class:"border-collapse border border-border my-4"}}),n.A.configure({HTMLAttributes:{class:"border border-border"}}),d.h.configure({HTMLAttributes:{class:"border border-border bg-muted font-semibold p-2"}}),c.A.configure({HTMLAttributes:{class:"border border-border p-2"}}),u.A.configure({width:640,height:480,HTMLAttributes:{class:"rounded-lg my-4"}}),m.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-primary hover:text-primary/80 underline"}}),g.A.configure({types:["heading","paragraph"]}),b.A.configure({types:["textStyle"]}),h.Ay.configure({multicolor:!0}),x.A.configure({types:["textStyle"]}),p.A,j.A,v.A,y.A.configure({lowlight:V,HTMLAttributes:{class:"bg-muted rounded-lg p-4 my-4 overflow-x-auto"}}),f.A.configure({HTMLAttributes:{class:"not-prose"}}),N.Ay.configure({nested:!0,HTMLAttributes:{class:"flex items-start gap-2"}}),w.A,A.A.configure({placeholder:S})],content:s,editable:!D,onUpdate:e=>{let{editor:s}=e;null==t||t(s.getHTML())},editorProps:{attributes:{class:"prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4 ".concat(T)}}}),O=(0,B.useCallback)(e=>{e.forEach(e=>{if(e.type.startsWith("image/")){let s=new FileReader;s.onload=()=>{let e=s.result;null==W||W.chain().focus().setImage({src:e}).run()},s.readAsDataURL(e)}})},[W]),{getRootProps:Y,getInputProps:G,isDragActive:Q}=(0,E.VB)({onDrop:O,accept:{"image/*":[".png",".jpg",".jpeg",".gif",".webp"],"video/*":[".mp4",".webm",".ogg"],"application/pdf":[".pdf"]},noClick:!0});return W?(0,r.jsxs)("div",{className:"border rounded-lg overflow-hidden",children:[!D&&(0,r.jsx)(en,{editor:W,onOpenMediaDialog:()=>H(!0),onOpenTableDialog:()=>z(!0)}),(0,r.jsxs)("div",{...Y(),className:"relative ".concat(Q?"bg-primary/5 border-primary/30":""),children:[(0,r.jsx)("input",{...G()}),Q&&(0,r.jsx)("div",{className:"absolute inset-0 bg-primary/5 bg-opacity-90 flex items-center justify-center z-10 border-2 border-dashed border-primary/30",children:(0,r.jsx)("p",{className:"text-primary font-medium",children:"Drop files here to upload"})}),(0,r.jsx)(a.$Z,{editor:W})]}),!D&&W.storage.characterCount&&(0,r.jsxs)("div",{className:"px-4 py-2 bg-muted border-t text-sm text-muted-foreground flex justify-between",children:[(0,r.jsxs)("span",{children:[W.storage.characterCount.characters()," characters, ",W.storage.characterCount.words()," words"]}),(0,r.jsxs)("span",{children:["Reading time: ~",Math.ceil(W.storage.characterCount.words()/200)," min"]})]}),(0,r.jsx)(eg,{isOpen:I,onClose:()=>H(!1),onInsert:(e,s)=>{if("image"===s)W.chain().focus().setImage({src:e}).run();else if("video"===s)if(e.includes("youtube.com/embed/")||e.includes("youtu.be/")||e.includes("youtube.com/watch"))W.chain().focus().setYoutubeVideo({src:e}).run();else if(e.startsWith("<iframe")||e.startsWith("<embed"))W.chain().focus().insertContent(e).run();else{let s='<video controls class="max-w-full h-auto rounded-lg my-4"><source src="'.concat(e,'" type="video/mp4">Your browser does not support the video tag.</video>');W.chain().focus().insertContent(s).run()}else if("audio"===s){let s='<audio controls class="w-full my-4"><source src="'.concat(e,'" type="audio/mpeg">Your browser does not support the audio tag.</audio>');W.chain().focus().insertContent(s).run()}else if("pdf"===s){let s='<div class="pdf-embed my-4 p-4 border rounded-lg bg-gray-50"><a href="'.concat(e,'" target="_blank" class="flex items-center gap-2 text-blue-600 hover:text-blue-800"><svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path d="M4 18h12V6l-4-4H4v16z"/></svg>View PDF Document</a></div>');W.chain().focus().insertContent(s).run()}}}),(0,r.jsx)(eb,{isOpen:R,onClose:()=>z(!1),onInsert:(e,s)=>{W.chain().focus().insertTable({rows:e,cols:s,withHeaderRow:!0}).run()}})]}):(0,r.jsxs)("div",{className:"animate-pulse",children:[(0,r.jsx)("div",{className:"h-12 bg-muted rounded mb-4"}),(0,r.jsx)("div",{className:"h-64 bg-muted rounded"})]})}}}]);