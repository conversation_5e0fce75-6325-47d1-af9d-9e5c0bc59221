"use strict";(()=>{var e={};e.id=487,e.ids=[487],e.modules={1708:e=>{e.exports=require("node:process")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7066:e=>{e.exports=require("node:tty")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{function s(e){return e.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim()}function a(e){return Math.ceil(e.trim().split(/\s+/).length/200)}function n(e,r=160){let t=e.replace(/<[^>]*>/g,"").replace(/[#*_`]/g,"").trim();return t.length<=r?t:t.substring(0,r).trim()+"..."}function o(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function i(e){let r=[];return e.length<8&&r.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||r.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||r.push("Password must contain at least one lowercase letter"),/\d/.test(e)||r.push("Password must contain at least one number"),{isValid:0===r.length,errors:r}}t.d(r,{DT:()=>o,Oj:()=>i,_C:()=>a,dn:()=>n,z9:()=>s})},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},12909:(e,r,t)=>{t.d(r,{N:()=>i});var s=t(13581),a=t(60890),n=t(31183),o=t(85663);let i={adapter:(0,a.y)(n.z),providers:[(0,s.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)return null;let r=await n.z.user.findUnique({where:{email:e.email}});return r&&await o.Ay.compare(e.password,r.password)?(await n.z.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,username:r.username,role:r.role,firstName:r.firstName,lastName:r.lastName,avatarUrl:r.avatarUrl}):null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role,e.username=r.username),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.username=r.username),e)},pages:{signIn:"/auth/signin"}}},16698:e=>{e.exports=require("node:async_hooks")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31421:e=>{e.exports=require("node:child_process")},33873:e=>{e.exports=require("path")},44857:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>m,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>g});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),u=t(19854),l=t(12909),p=t(31183),c=t(10974);async function d(e){try{let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),a=r.get("category"),n=r.get("tag"),o=r.get("author"),u=r.get("search"),l=r.get("status")||"PUBLISHED",c=(t-1)*s,d={status:l};a&&(d.category={slug:a}),n&&(d.tags={some:{tag:{slug:n}}}),o&&(d.author={username:o}),u&&(d.OR=[{title:{contains:u,mode:"insensitive"}},{subtitle:{contains:u,mode:"insensitive"}},{content:{contains:u,mode:"insensitive"}}]);let[g,m]=await Promise.all([p.z.article.findMany({where:d,include:{author:{select:{id:!0,username:!0,firstName:!0,lastName:!0,avatarUrl:!0}},category:{select:{id:!0,name:!0,slug:!0,color:!0}},tags:{include:{tag:{select:{id:!0,name:!0,slug:!0}}}},_count:{select:{comments:!0,reactions:!0,bookmarks:!0}}},orderBy:{publishedAt:"desc"},skip:c,take:s}),p.z.article.count({where:d})]);return i.NextResponse.json({articles:g,pagination:{page:t,limit:s,total:m,pages:Math.ceil(m/s)}})}catch(e){return console.error("Error fetching articles:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let r=await (0,u.getServerSession)(l.N);if(!r?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{title:t,subtitle:s,content:a,categoryId:n,tags:o,featuredImageUrl:d,featuredImageAlt:g,status:m="DRAFT",scheduledAt:x,metaDescription:h,ogTitle:w,ogDescription:q,ogImageUrl:f,allowComments:y=!0}=await e.json();if(!t||!a)return i.NextResponse.json({error:"Title and content are required"},{status:400});let v=(0,c.z9)(t),b=v,j=1;for(;await p.z.article.findUnique({where:{slug:b}});)b=`${v}-${j}`,j++;let N=(0,c._C)(a),k=(0,c.dn)(a),P=await p.z.article.create({data:{title:t,subtitle:s,slug:b,content:a,excerpt:k,featuredImageUrl:d,featuredImageAlt:g,status:m,readingTime:N,metaDescription:h,ogTitle:w,ogDescription:q,ogImageUrl:f,allowComments:y,scheduledAt:x?new Date(x):null,publishedAt:"PUBLISHED"===m?new Date:null,authorId:r.user.id,categoryId:n||null,tags:o?{create:o.map(e=>({tag:{connect:{id:e}}}))}:void 0},include:{author:{select:{id:!0,username:!0,firstName:!0,lastName:!0,avatarUrl:!0}},category:!0,tags:{include:{tag:!0}}}});return i.NextResponse.json(P,{status:201})}catch(e){return console.error("Error creating article:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/articles/route",pathname:"/api/articles",filename:"route",bundlePath:"app/api/articles/route"},resolvedPagePath:"C:\\D\\Books\\Blog\\blogcms\\src\\app\\api\\articles\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:w}=m;function q(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:e=>{e.exports=require("node:os")},51455:e=>{e.exports=require("node:fs/promises")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57975:e=>{e.exports=require("node:util")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},74075:e=>{e.exports=require("zlib")},76760:e=>{e.exports=require("node:path")},77598:e=>{e.exports=require("node:crypto")},78474:e=>{e.exports=require("node:events")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,580,663,819,275],()=>t(44857));module.exports=s})();