'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { X, Upload, Image as ImageIcon, AlertCircle, CheckCircle } from 'lucide-react'

interface FeaturedImageUploadProps {
  imageUrl: string
  altText: string
  onImageChange: (url: string) => void
  onAltTextChange: (alt: string) => void
}

export function FeaturedImageUpload({ 
  imageUrl, 
  altText, 
  onImageChange, 
  onAltTextChange 
}: FeaturedImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState('')
  const [dragActive, setDragActive] = useState(false)

  // File size limit for images (10MB)
  const MAX_FILE_SIZE = 10 * 1024 * 1024

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    // Check if it's an image
    if (!file.type.startsWith('image/')) {
      return { valid: false, error: 'Please upload an image file (PNG, JPG, JPEG, GIF, WebP, SVG).' }
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return { valid: false, error: 'Image size too large. Maximum size is 10MB.' }
    }

    // Check specific image types
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp', 'image/svg+xml']
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Unsupported image format. Please use PNG, JPG, JPEG, GIF, WebP, or SVG.' }
    }

    return { valid: true }
  }

  const processFile = useCallback(async (file: File) => {
    setError('')
    
    const validation = validateFile(file)
    if (!validation.valid) {
      setError(validation.error || 'Invalid file')
      return
    }

    setUploading(true)
    try {
      // In a real app, you would upload to your storage service (Cloudinary, AWS S3, etc.)
      // For now, we'll create a base64 URL for demonstration
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        onImageChange(result)
        setUploading(false)
      }
      reader.onerror = () => {
        setError('Failed to process image. Please try again.')
        setUploading(false)
      }
      reader.readAsDataURL(file)
    } catch (error) {
      console.error('Upload failed:', error)
      setError('Failed to process image. Please try again.')
      setUploading(false)
    }
  }, [onImageChange])

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      processFile(file)
    }
    setDragActive(false)
  }, [processFile])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg'],
    },
    maxFiles: 1,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
  })

  const handleRemoveImage = () => {
    onImageChange('')
    setError('')
  }

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700">
        <ImageIcon className="h-4 w-4 inline mr-1" />
        Featured Image
      </label>

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md flex items-center gap-2">
          <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
          <span className="text-red-700 text-sm">{error}</span>
        </div>
      )}

      {!imageUrl ? (
        /* Upload Area */
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
            isDragActive || dragActive
              ? 'border-blue-400 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400'
          }`}
        >
          <input {...getInputProps()} />
          <Upload className="h-8 w-8 mx-auto mb-3 text-gray-400" />
          <p className="text-gray-600 mb-2">
            {isDragActive
              ? 'Drop the image here...'
              : 'Drag & drop an image here, or click to select'}
          </p>
          <p className="text-sm text-gray-500">
            Supports: PNG, JPG, JPEG, GIF, WebP, SVG
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Maximum size: 10MB
          </p>
        </div>
      ) : (
        /* Image Preview */
        <div className="space-y-3">
          <div className="relative group">
            <img
              src={imageUrl}
              alt={altText || 'Featured image preview'}
              className="w-full h-48 object-cover rounded-lg border"
              onError={() => {
                setError('Failed to load image. Please check the URL or try uploading again.')
                onImageChange('')
              }}
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all rounded-lg flex items-center justify-center">
              <button
                type="button"
                onClick={handleRemoveImage}
                className="opacity-0 group-hover:opacity-100 bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-all"
                title="Remove image"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
            {uploading && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
                <div className="text-center">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mb-2"></div>
                  <p className="text-sm text-gray-600">Processing image...</p>
                </div>
              </div>
            )}
          </div>
          
          {/* Success indicator */}
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm">Image uploaded successfully</span>
          </div>
        </div>
      )}

      {/* Alt Text Input */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Alt Text (for accessibility)
        </label>
        <input
          type="text"
          placeholder="Describe the image for screen readers..."
          value={altText}
          onChange={(e) => onAltTextChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <p className="text-xs text-gray-500 mt-1">
          Provide a brief description of the image for accessibility and SEO
        </p>
      </div>
    </div>
  )
}
