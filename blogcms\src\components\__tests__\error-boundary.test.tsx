import React from 'react'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { ErrorBoundary, EditorErrorBoundary, DashboardErrorBoundary } from '../error-boundary'

// Component that throws an error for testing
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// Mock console.error to avoid noise in tests
const originalError = console.error
beforeAll(() => {
  console.error = jest.fn()
})

afterAll(() => {
  console.error = originalError
})

describe('ErrorBoundary', () => {
  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    )

    expect(screen.getByText('No error')).toBeInTheDocument()
  })

  it('renders error UI when there is an error', () => {
    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
    expect(screen.getByText(/We encountered an unexpected error/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument()
  })

  it('allows resetting the error state', async () => {
    const user = userEvent.setup()
    const TestComponent = () => {
      const [shouldThrow, setShouldThrow] = React.useState(true)
      
      return (
        <ErrorBoundary>
          <button type="button" onClick={() => setShouldThrow(false)}>Fix Error</button>
          <ThrowError shouldThrow={shouldThrow} />
        </ErrorBoundary>
      )
    }

    render(<TestComponent />)

    // Error should be displayed
    expect(screen.getByText('Something went wrong')).toBeInTheDocument()

    // Click try again button
    const tryAgainButton = screen.getByRole('button', { name: /try again/i })
    await user.click(tryAgainButton)

    // Should attempt to re-render the component
    expect(screen.getByText('Something went wrong')).toBeInTheDocument()
  })

  it('uses custom fallback when provided', () => {
    const CustomFallback = ({ error, resetError }: { error?: Error; resetError: () => void }) => (
      <div>
        <h2>Custom Error UI</h2>
        <p>Error: {error?.message}</p>
        <button type="button" onClick={resetError}>Reset</button>
      </div>
    )

    render(
      <ErrorBoundary fallback={CustomFallback}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Custom Error UI')).toBeInTheDocument()
    expect(screen.getByText('Error: Test error')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Reset' })).toBeInTheDocument()
  })

  it('shows error details in development mode', () => {
    const originalEnv = process.env.NODE_ENV
    process.env.NODE_ENV = 'development'

    render(
      <ErrorBoundary>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    )

    expect(screen.getByText('Error Details')).toBeInTheDocument()

    process.env.NODE_ENV = originalEnv
  })
})

describe('EditorErrorBoundary', () => {
  it('renders editor-specific error UI', () => {
    render(
      <EditorErrorBoundary>
        <ThrowError shouldThrow={true} />
      </EditorErrorBoundary>
    )

    expect(screen.getByText('Editor Error')).toBeInTheDocument()
    expect(screen.getByText(/The editor encountered an error/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /reload editor/i })).toBeInTheDocument()
  })

  it('allows resetting editor error', async () => {
    const user = userEvent.setup()
    
    render(
      <EditorErrorBoundary>
        <ThrowError shouldThrow={true} />
      </EditorErrorBoundary>
    )

    const reloadButton = screen.getByRole('button', { name: /reload editor/i })
    await user.click(reloadButton)

    // Should attempt to re-render
    expect(screen.getByText('Editor Error')).toBeInTheDocument()
  })
})

describe('DashboardErrorBoundary', () => {
  it('renders dashboard-specific error UI', () => {
    render(
      <DashboardErrorBoundary>
        <ThrowError shouldThrow={true} />
      </DashboardErrorBoundary>
    )

    expect(screen.getByText('Dashboard Error')).toBeInTheDocument()
    expect(screen.getByText(/Unable to load dashboard data/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /reload dashboard/i })).toBeInTheDocument()
  })

  it('allows resetting dashboard error', async () => {
    const user = userEvent.setup()
    
    render(
      <DashboardErrorBoundary>
        <ThrowError shouldThrow={true} />
      </DashboardErrorBoundary>
    )

    const reloadButton = screen.getByRole('button', { name: /reload dashboard/i })
    await user.click(reloadButton)

    // Should attempt to re-render
    expect(screen.getByText('Dashboard Error')).toBeInTheDocument()
  })
})
