(()=>{var e={};e.id=778,e.ids=[778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16189:(e,r,s)=>{"use strict";var t=s(65773);s.o(t,"useParams")&&s.d(r,{useParams:function(){return t.useParams}}),s.o(t,"useRouter")&&s.d(r,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(r,{useSearchParams:function(){return t.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27142:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(65239),a=s(48088),n=s(88170),o=s.n(n),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let d={children:["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94796)),"C:\\D\\Books\\Blog\\blogcms\\src\\app\\auth\\signup\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,80871)),"C:\\D\\Books\\Blog\\blogcms\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\D\\Books\\Blog\\blogcms\\src\\app\\auth\\signup\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41550:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},65336:(e,r,s)=>{Promise.resolve().then(s.bind(s,94796))},70058:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>f});var t=s(60687),a=s(43210),n=s(16189),o=s(85814),i=s.n(o),l=s(5336),d=s(93613),c=s(58869),u=s(41550),m=s(64021),p=s(12597),x=s(13861);function f(){let e=(0,n.useRouter)(),[r,s]=(0,a.useState)({firstName:"",lastName:"",username:"",email:"",password:"",confirmPassword:""}),[o,f]=(0,a.useState)(!1),[h,g]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1),[y,N]=(0,a.useState)({}),[w,j]=(0,a.useState)(!1),k=()=>{let e={};return r.firstName.trim()||(e.firstName="First name is required"),r.lastName.trim()||(e.lastName="Last name is required"),r.username.trim()?r.username.length<3?e.username="Username must be at least 3 characters":/^[a-zA-Z0-9_]+$/.test(r.username)||(e.username="Username can only contain letters, numbers, and underscores"):e.username="Username is required",r.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.email)||(e.email="Please enter a valid email address"):e.email="Email is required",r.password?r.password.length<6&&(e.password="Password must be at least 6 characters"):e.password="Password is required",r.confirmPassword?r.password!==r.confirmPassword&&(e.confirmPassword="Passwords do not match"):e.confirmPassword="Please confirm your password",N(e),0===Object.keys(e).length},P=async s=>{if(s.preventDefault(),k()){v(!0),N({});try{let s=await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:r.firstName,lastName:r.lastName,username:r.username,email:r.email,password:r.password})}),t=await s.json();s.ok?(j(!0),setTimeout(()=>{e.push("/auth/signin?message=Account created successfully")},2e3)):t.errors?N(t.errors):N({general:t.error||"An error occurred during registration"})}catch{N({general:"An error occurred. Please try again."})}finally{v(!1)}}};return w?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(l.A,{className:"mx-auto h-16 w-16 text-green-500"}),(0,t.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Account Created!"}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Your account has been created successfully. Redirecting to sign in..."})]})})}):(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-foreground",children:"Create your account"}),(0,t.jsxs)("p",{className:"mt-2 text-center text-sm text-muted-foreground",children:["Or"," ",(0,t.jsx)(i(),{href:"/auth/signin",className:"font-medium text-primary hover:text-primary/80",children:"sign in to your existing account"})]})]}),y.general&&(0,t.jsx)("div",{className:"bg-destructive/10 border border-destructive/20 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-destructive"}),(0,t.jsx)("div",{className:"ml-3",children:(0,t.jsx)("p",{className:"text-sm text-destructive",children:y.general})})]})}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:P,children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-foreground",children:"First Name"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"firstName",name:"firstName",type:"text",required:!0,value:r.firstName,onChange:e=>s({...r,firstName:e.target.value}),className:`appearance-none relative block w-full pl-10 pr-3 py-2 border ${y.firstName?"border-destructive":"border-input"} placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm`,placeholder:"First name"})]}),y.firstName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:y.firstName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-foreground",children:"Last Name"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)("input",{id:"lastName",name:"lastName",type:"text",required:!0,value:r.lastName,onChange:e=>s({...r,lastName:e.target.value}),className:`appearance-none relative block w-full px-3 py-2 border ${y.lastName?"border-destructive":"border-input"} placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm`,placeholder:"Last name"})}),y.lastName&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:y.lastName})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-foreground",children:"Username"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"username",name:"username",type:"text",required:!0,value:r.username,onChange:e=>s({...r,username:e.target.value.toLowerCase()}),className:`appearance-none relative block w-full pl-10 pr-3 py-2 border ${y.username?"border-destructive":"border-input"} placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm`,placeholder:"Choose a username"})]}),y.username&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:y.username})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-foreground",children:"Email address"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:r.email,onChange:e=>s({...r,email:e.target.value}),className:`appearance-none relative block w-full pl-10 pr-3 py-2 border ${y.email?"border-destructive":"border-input"} placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm`,placeholder:"Enter your email"})]}),y.email&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:y.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-foreground",children:"Password"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"password",name:"password",type:o?"text":"password",autoComplete:"new-password",required:!0,value:r.password,onChange:e=>s({...r,password:e.target.value}),className:`appearance-none relative block w-full pl-10 pr-10 py-2 border ${y.password?"border-destructive":"border-input"} placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm`,placeholder:"Create a password"}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>f(!o),children:o?(0,t.jsx)(p.A,{className:"h-5 w-5 text-muted-foreground"}):(0,t.jsx)(x.A,{className:"h-5 w-5 text-muted-foreground"})})]}),y.password&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:y.password})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-foreground",children:"Confirm Password"}),(0,t.jsxs)("div",{className:"mt-1 relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(m.A,{className:"h-5 w-5 text-muted-foreground"})}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:h?"text":"password",autoComplete:"new-password",required:!0,value:r.confirmPassword,onChange:e=>s({...r,confirmPassword:e.target.value}),className:`appearance-none relative block w-full pl-10 pr-10 py-2 border ${y.confirmPassword?"border-destructive":"border-input"} placeholder-muted-foreground text-foreground bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring focus:z-10 sm:text-sm`,placeholder:"Confirm your password"}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g(!h),children:h?(0,t.jsx)(p.A,{className:"h-5 w-5 text-muted-foreground"}):(0,t.jsx)(x.A,{className:"h-5 w-5 text-muted-foreground"})})]}),y.confirmPassword&&(0,t.jsx)("p",{className:"mt-1 text-sm text-destructive",children:y.confirmPassword})]})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:b,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring disabled:opacity-50 disabled:cursor-not-allowed",children:b?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create account"})})]})]})})}},70440:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75064:(e,r,s)=>{Promise.resolve().then(s.bind(s,70058))},79551:e=>{"use strict";e.exports=require("url")},93613:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94796:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\D\\\\Books\\\\Blog\\\\blogcms\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\D\\Books\\Blog\\blogcms\\src\\app\\auth\\signup\\page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[243,789,658,575],()=>s(27142));module.exports=t})();